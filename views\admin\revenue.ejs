<%- include('../partials/admin-header') %>

<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <h1 class="h3 mb-4">Revenue Tracking</h1>
    </div>
  </div>

  <!-- Email Search Section -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h5 class="card-title mb-0">Search Orders by Email</h5>
        </div>
        <div class="card-body">
          <form id="emailSearchForm" class="mb-3">
            <div class="row">
              <div class="col-md-8">
                <input type="email" id="searchEmail" class="form-control" placeholder="Enter customer email to search..." required>
              </div>
              <div class="col-md-4">
                <button type="submit" class="btn btn-primary w-100">Search Orders</button>
              </div>
            </div>
          </form>
          <div id="searchResults" class="d-none">
            <h6 class="mb-3">Search Results:</h6>
            <div class="table-responsive">
              <table class="table table-hover" id="searchResultsTable">
                <thead>
                  <tr>
                    <th>Order ID</th>
                    <th>Customer</th>
                    <th>Service</th>
                    <th>Amount</th>
                    <th>Date</th>
                    <th>Payment Status</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  <!-- Search results will be loaded here -->
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Revenue Overview -->
  <div class="row mb-4">
    <div class="col-md-4">
      <div class="card">
        <div class="card-body">
          <h5 class="card-title">Total Revenue</h5>
          <h2 class="mt-3 mb-0" id="totalRevenue">AED 0.00</h2>
          <p class="text-muted">From all paid orders</p>
        </div>
      </div>
    </div>
    <div class="col-md-4">
      <div class="card">
        <div class="card-body">
          <h5 class="card-title">Monthly Revenue</h5>
          <h2 class="mt-3 mb-0" id="monthlyRevenue">AED 0.00</h2>
          <p class="text-muted">Current month</p>
        </div>
      </div>
    </div>
    <div class="col-md-4">
      <div class="card">
        <div class="card-body">
          <h5 class="card-title">COD Payments</h5>
          <h2 class="mt-3 mb-0" id="codRevenue">AED 0.00</h2>
          <p class="text-muted">Total cash payments</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Revenue by Payment Method -->
  <div class="row mb-4">
    <div class="col-md-6">
      <div class="card">
        <div class="card-header">
          <h5 class="card-title mb-0">Revenue by Payment Method</h5>
        </div>
        <div class="card-body">
          <div class="table-responsive">
            <table class="table table-bordered">
              <thead>
                <tr>
                  <th>Payment Method</th>
                  <th>Orders</th>
                  <th>Amount</th>
                </tr>
              </thead>
              <tbody id="paymentMethodTable">
                <!-- Payment methods will be loaded here -->
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
    <div class="col-md-6">
      <div class="card">
        <div class="card-header">
          <h5 class="card-title mb-0">Monthly Revenue</h5>
        </div>
        <div class="card-body">
          <canvas id="monthlyRevenueChart"></canvas>
        </div>
      </div>
    </div>
  </div>

  <!-- Pending COD Payments -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h5 class="card-title mb-0">Pending COD Payments</h5>
          <a href="/admin/cod-payments" class="btn btn-primary btn-sm">Manage COD Payments</a>
        </div>
        <div class="card-body">
          <div class="table-responsive">
            <table class="table table-hover" id="pendingPaymentsTable">
              <thead>
                <tr>
                  <th>Order ID</th>
                  <th>Customer</th>
                  <th>Service</th>
                  <th>Amount</th>
                  <th>Scheduled Date</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                <!-- Pending payments will be loaded here -->
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Payment Update Modal -->
<div class="modal fade" id="paymentUpdateModal" tabindex="-1">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Update Payment Status</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body">
        <form id="paymentUpdateForm">
          <input type="hidden" id="orderId">
          <div class="mb-3">
            <label class="form-label">Payment Status</label>
            <select class="form-select" id="paymentStatus" required>
              <option value="cod_pending">Pending</option>
              <option value="cod_paid">Paid</option>
            </select>
          </div>
          <div class="mb-3">
            <label class="form-label">Notes</label>
            <textarea class="form-control" id="paymentNotes" rows="3"></textarea>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
        <button type="button" class="btn btn-primary" id="updatePaymentBtn">Update Status</button>
      </div>
    </div>
  </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
  // Initialize DataTable with options for better performance
  const pendingTable = $('#pendingPaymentsTable').DataTable({
    order: [[4, 'asc']],
    pageLength: 5,
    lengthMenu: [[5, 10, 25], [5, 10, 25]],
    language: {
      processing: "Loading...",
      search: "Filter:",
      paginate: {
        first: "First",
        previous: "Prev",
        next: "Next",
        last: "Last"
      }
    },
    // Use deferRender for better performance with larger datasets
    deferRender: true
  });

  // Initialize chart
  let monthlyChart;
  
  // Helper function to format currency
  const formatCurrency = (amount) => {
    return `AED ${parseFloat(amount || 0).toFixed(2)}`;
  };

  // Load revenue data more efficiently
  async function loadRevenueData() {
    try {
      // Show loading indicators
      document.getElementById('totalRevenue').textContent = 'Loading...';
      document.getElementById('monthlyRevenue').textContent = 'Loading...';
      document.getElementById('codRevenue').textContent = 'Loading...';
      
      // Clear payment method table and show loading
      document.getElementById('paymentMethodTable').innerHTML = `
        <tr>
          <td colspan="3" class="text-center py-3">Loading payment data...</td>
        </tr>
      `;
      
      // Fetch data
      const response = await fetch('/admin/api/revenue-analysis');
      if (!response.ok) throw new Error('Failed to fetch revenue data');
      const data = await response.json();

      // Update summary cards
      document.getElementById('totalRevenue').textContent = formatCurrency(data.totalRevenue);

      // Find monthly revenue for current month
      const now = new Date();
      const currentMonth = now.getMonth() + 1; // JS months are 0-indexed
      const currentYear = now.getFullYear();
      
      const currentMonthData = data.monthlyRevenue.find(item => 
        item._id && item._id.month === currentMonth && item._id.year === currentYear
      );
      
      document.getElementById('monthlyRevenue').textContent = formatCurrency(currentMonthData ? currentMonthData.total : 0);

      // Calculate COD revenue
      const codMethod = data.revenueByMethod.find(item => item._id === 'cash' || item._id === 'cod');
      const codRevenue = codMethod ? codMethod.total : 0;
      document.getElementById('codRevenue').textContent = formatCurrency(codRevenue);

      // Populate payment method table - more efficient approach
      const paymentMethodTable = document.getElementById('paymentMethodTable');
      paymentMethodTable.innerHTML = '';
      
      // Map payment method names for better display
      const methodNames = {
        'cash': 'Cash on Delivery',
        'card': 'Credit/Debit Card',
        'bank_transfer': 'Bank Transfer',
        'other': 'Other Methods'
      };
      
      data.revenueByMethod.forEach(method => {
        const methodName = methodNames[method._id] || method._id;
        const row = document.createElement('tr');
        row.innerHTML = `
          <td>${methodName}</td>
          <td>${method.count}</td>
          <td>${formatCurrency(method.total)}</td>
        `;
        paymentMethodTable.appendChild(row);
      });

      // Load chart data asynchronously
      setTimeout(() => loadMonthlyChart(data.monthlyRevenue), 0);

      // Load pending payments more efficiently
      loadPendingPayments(data.pendingCODPayments);
    } catch (error) {
      console.error('Error loading revenue data:', error);
      showError();
    }
  }
  
  // Show error messages
  function showError() {
    document.getElementById('totalRevenue').textContent = 'Error loading data';
    document.getElementById('monthlyRevenue').textContent = 'Error loading data';
    document.getElementById('codRevenue').textContent = 'Error loading data';
    
    document.getElementById('paymentMethodTable').innerHTML = `
      <tr>
        <td colspan="3" class="text-center py-3 text-danger">Failed to load payment data. Please refresh to try again.</td>
      </tr>
    `;
    
    // Show error in chart
    const ctx = document.getElementById('monthlyRevenueChart').getContext('2d');
    ctx.font = '14px Arial';
    ctx.fillStyle = 'red';
    ctx.textAlign = 'center';
    ctx.fillText('Failed to load chart data', ctx.canvas.width/2, ctx.canvas.height/2);
  }

  // Load pending payments separately
  function loadPendingPayments(payments) {
    try {
      pendingTable.clear();
      
      if (!payments || payments.length === 0) {
        pendingTable.row.add([
          '-',
          'No pending payments',
          '-',
          '-',
          '-',
          '-'
        ]);
      } else {
        payments.forEach(payment => {
          const customerName = payment.customer?.name || 'Unknown';
          const customerPhone = payment.customer?.phone || 'No phone';
          const serviceName = payment.service?.name || 'Unknown Service';
          const amount = payment.amount || payment.totalAmount || 0;
          const scheduledDate = payment.scheduledDate || payment.date || new Date();
          
          pendingTable.row.add([
            payment._id,
            `${customerName}<br>${customerPhone}`,
            serviceName,
            formatCurrency(amount),
            new Date(scheduledDate).toLocaleDateString(),
            `<button class="btn btn-sm btn-primary update-payment" data-order-id="${payment._id}">Update Status</button>`
          ]);
        });
      }
      
      pendingTable.draw();
    } catch (error) {
      console.error('Error loading pending payments:', error);
      pendingTable.clear();
      pendingTable.row.add([
        '-',
        'Error loading data',
        '-',
        '-',
        '-',
        '-'
      ]);
      pendingTable.draw();
    }
  }

  // Load monthly chart more efficiently
  function loadMonthlyChart(monthlyData) {
    try {
      // Transform data for chart
      const last6Months = [];
      const now = new Date();
      
      for (let i = 5; i >= 0; i--) {
        const monthDate = new Date(now.getFullYear(), now.getMonth() - i, 1);
        const monthName = monthDate.toLocaleString('default', { month: 'short' });
        const year = monthDate.getFullYear();
        const month = monthDate.getMonth() + 1; // JS months are 0-indexed
        
        // First try to find the month in the provided data
        const monthData = monthlyData.find(item => 
          item._id && item._id.month === month && item._id.year === year
        );
        
        last6Months.push({
          month: `${monthName} ${year}`,
          revenue: monthData ? Number(monthData.total) : 0
        });
      }
      
      const chartLabels = last6Months.map(item => item.month);
      const chartData = last6Months.map(item => item.revenue);
      
      // Create chart
      const ctx = document.getElementById('monthlyRevenueChart').getContext('2d');
      
      if (monthlyChart) {
        monthlyChart.destroy();
      }
      
      monthlyChart = new Chart(ctx, {
        type: 'bar',
        data: {
          labels: chartLabels,
          datasets: [{
            label: 'Monthly Revenue',
            data: chartData,
            backgroundColor: 'rgba(54, 162, 235, 0.5)',
            borderColor: 'rgba(54, 162, 235, 1)',
            borderWidth: 1
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          animation: {
            duration: 500 // Shorter animation for better performance
          },
          plugins: {
            legend: {
              display: false
            },
            tooltip: {
              callbacks: {
                label: function(context) {
                  return 'AED ' + context.raw.toFixed(2);
                }
              }
            }
          },
          scales: {
            y: {
              beginAtZero: true,
              ticks: {
                callback: function(value) {
                  return 'AED ' + value;
                },
                maxTicksLimit: 5 // Limit number of ticks for better performance
              },
              grid: {
                color: 'rgba(0, 0, 0, 0.05)'
              }
            },
            x: {
              grid: {
                display: false
              }
            }
          }
        }
      });
    } catch (error) {
      console.error('Error creating chart:', error);
      const ctx = document.getElementById('monthlyRevenueChart').getContext('2d');
      ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);
      ctx.font = '14px Arial';
      ctx.fillStyle = 'red';
      ctx.textAlign = 'center';
      ctx.fillText('Error loading chart', ctx.canvas.width/2, ctx.canvas.height/2);
    }
  }

  // Handle payment status update
  document.getElementById('updatePaymentBtn').addEventListener('click', async function() {
    const orderId = document.getElementById('orderId').value;
    const paymentStatus = document.getElementById('paymentStatus').value;
    const notes = document.getElementById('paymentNotes').value;

    try {
      // Update button state
      const updateBtn = document.getElementById('updatePaymentBtn');
      updateBtn.textContent = 'Updating...';
      updateBtn.disabled = true;
      
      const response = await fetch(`/admin/api/order/${orderId}/payment`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ paymentStatus, notes })
      });
      
      const data = await response.json();
      
      if (data.success) {
        // Close modal and reload data
        bootstrap.Modal.getInstance(document.getElementById('paymentUpdateModal')).hide();
        
        // Show success message
        Swal.fire({
          icon: 'success',
          title: 'Success!',
          text: 'Payment status updated successfully',
          timer: 2000,
          showConfirmButton: false
        });
        
        // Reload data
        loadRevenueData();
      } else {
        throw new Error(data.error || 'Failed to update payment status');
      }
    } catch (error) {
      console.error('Error updating payment:', error);
      Swal.fire({
        icon: 'error',
        title: 'Error',
        text: 'Failed to update payment status'
      });
    } finally {
      // Reset button state
      const updateBtn = document.getElementById('updatePaymentBtn');
      updateBtn.textContent = 'Update Status';
      updateBtn.disabled = false;
    }
  });

  // Handle update payment button click
  document.addEventListener('click', function(e) {
    if (e.target.classList.contains('update-payment')) {
      const orderId = e.target.dataset.orderId;
      document.getElementById('orderId').value = orderId;
      document.getElementById('paymentStatus').value = 'cod_pending'; // Reset to default
      document.getElementById('paymentNotes').value = ''; // Clear notes
      
      // Show modal
      new bootstrap.Modal(document.getElementById('paymentUpdateModal')).show();
    }
  });

  // Initial load
  loadRevenueData();

  // Initialize search results table
  const searchResultsTable = $('#searchResultsTable').DataTable({
    order: [[4, 'desc']],
    pageLength: 5,
    lengthMenu: [[5, 10, 25], [5, 10, 25]],
    language: {
      processing: "Loading...",
      search: "Filter results:",
      paginate: {
        first: "First",
        previous: "Prev",
        next: "Next",
        last: "Last"
      }
    },
    deferRender: true
  });

  // Handle email search form submission
  document.getElementById('emailSearchForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const email = document.getElementById('searchEmail').value.trim();
    if (!email) return;
    
    try {
      // Show loading indicator
      const searchResultsDiv = document.getElementById('searchResults');
      searchResultsDiv.classList.remove('d-none');
      searchResultsTable.clear();
      searchResultsTable.row.add([
        'Loading...',
        'Searching for orders...',
        '',
        '',
        '',
        '',
        ''
      ]);
      searchResultsTable.draw();
      
      // Fetch orders by email
      const response = await fetch(`/admin/api/orders/search?email=${encodeURIComponent(email)}`);
      if (!response.ok) throw new Error('Failed to search orders');
      
      const orders = await response.json();
      
      // Update search results table
      searchResultsTable.clear();
      
      if (orders.length === 0) {
        searchResultsTable.row.add([
          'No results',
          'No orders found for this email',
          '',
          '',
          '',
          '',
          ''
        ]);
      } else {
        orders.forEach(order => {
          const orderDate = new Date(order.date || order.scheduledDate || order.createdAt).toLocaleDateString();
          const paymentStatusClass = getPaymentStatusClass(order.paymentStatus);
          const actionButton = getActionButton(order);
          
          searchResultsTable.row.add([
            order._id,
            `${order.customerName}<br>${order.email}`,
            order.service || 'Unknown Service',
            formatCurrency(order.totalAmount || order.amount || 0),
            orderDate,
            `<span class="badge ${paymentStatusClass}">${getPaymentStatusText(order.paymentStatus)}</span>`,
            actionButton
          ]);
        });
      }
      
      searchResultsTable.draw();
    } catch (error) {
      console.error('Error searching orders:', error);
      searchResultsTable.clear();
      searchResultsTable.row.add([
        'Error',
        'Failed to search orders. Please try again.',
        '',
        '',
        '',
        '',
        ''
      ]);
      searchResultsTable.draw();
    }
  });

  // Helper function to get payment status text
  function getPaymentStatusText(status) {
    switch (status) {
      case 'paid': return 'Paid';
      case 'cod_paid': return 'COD Paid';
      case 'pending': return 'Pending';
      case 'cod_pending': return 'COD Pending';
      default: return status || 'Unknown';
    }
  }

  // Helper function to get payment status class
  function getPaymentStatusClass(status) {
    switch (status) {
      case 'paid': 
      case 'cod_paid': 
        return 'bg-success';
      case 'pending':
      case 'cod_pending':
        return 'bg-warning';
      default: 
        return 'bg-secondary';
    }
  }

  // Helper function to get action button based on order status
  function getActionButton(order) {
    if (order.paymentMethod === 'cash' || order.paymentMethod === 'cod') {
      if (order.paymentStatus === 'pending' || order.paymentStatus === 'cod_pending') {
        return `<button class="btn btn-sm btn-success mark-as-paid" data-order-id="${order._id}">Mark as Paid</button>`;
      } else {
        return '<span class="badge bg-success">Payment Recorded</span>';
      }
    } else {
      return `<span class="badge bg-info">${order.paymentMethod || 'Unknown'} Payment</span>`;
    }
  }

  // Handle mark as paid button click from search results
  document.addEventListener('click', function(e) {
    if (e.target.classList.contains('mark-as-paid')) {
      const orderId = e.target.dataset.orderId;
      document.getElementById('orderId').value = orderId;
      document.getElementById('paymentStatus').value = 'cod_paid'; // Set to paid by default
      document.getElementById('paymentNotes').value = 'Payment collected by admin'; // Default note
      
      // Show modal
      new bootstrap.Modal(document.getElementById('paymentUpdateModal')).show();
    }
  });
});
</script>

<%- include('../partials/admin-footer') %> 