const mongoose = require('mongoose');
const User = require('../models/User');
const bcrypt = require('bcryptjs');

// Connect to database
mongoose.connect('mongodb://localhost:27017/junkexpert')
  .then(async () => {
    console.log('Connected to MongoDB');
    
    try {
      // Create a new super admin user with a simple password
      const salt = await bcrypt.genSalt(10);
      const hashedPassword = await bcrypt.hash('admin123', salt);
      
      const superAdmin = new User({
        name: 'Super Admin',
        email: '<EMAIL>',
        password: hashedPassword,
        phone: '+971 50 000 0000',
        address: 'Dubai, UAE',
        role: 'admin'
      });
      
      await superAdmin.save();
      console.log('Super Admin user created successfully');
      console.log('Email: <EMAIL>');
      console.log('Password: admin123');
      
      // List all admin users
      const admins = await User.find({ role: 'admin' }).select('-password');
      console.log('\nAll admin users:');
      console.log(admins);
      
    } catch (error) {
      console.error('Error creating super admin:', error);
    } finally {
      mongoose.disconnect();
      console.log('Disconnected from MongoDB');
    }
  })
  .catch(err => {
    console.error('Error connecting to MongoDB:', err);
  }); 