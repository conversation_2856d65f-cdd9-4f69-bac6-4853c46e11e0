const stripe = require('../config/stripe');
const Order = require('../models/Order');
const mongoose = require('mongoose');
const { 
  sendPaymentConfirmationEmail,  
  sendPaymentNotificationToAdmin 
} = require('../config/email');

// Create a payment intent
exports.createPaymentIntent = async (req, res) => {
  try {
    const { orderId } = req.body;
    
    // Handle pending orders from session
    if (orderId === 'pending') {
      if (!req.session.pendingOrderData) {
        return res.status(400).json({ 
          success: false, 
          message: 'No pending order found. Please try again.' 
        });
      }
      
      const orderData = req.session.pendingOrderData;
      
      // Calculate amount in cents
      const amount = Math.round(orderData.totalAmount * 100);
      
      // Create a payment intent
      const paymentIntent = await stripe.paymentIntents.create({
        amount,
        currency: 'aed', // UAE currency
        metadata: {
          isPendingOrder: 'true', // Flag to identify pending orders in webhook
          orderData: JSON.stringify({
            customerName: orderData.customerName,
            email: orderData.email,
            service: orderData.service,
            additionalServices: orderData.additionalServices,
            amount: orderData.totalAmount
          })
        },
        description: `Payment for JunkExperts service`,
        receipt_email: orderData.email
      });
      
      // Store payment intent ID in session
      req.session.pendingOrderPaymentIntentId = paymentIntent.id;
      
      // Return client secret
      return res.json({
        success: true,
        clientSecret: paymentIntent.client_secret,
        totalAmount: orderData.totalAmount,
        isPending: true
      });
    }
    
    // Regular flow for database-stored orders
    if (!orderId || !mongoose.Types.ObjectId.isValid(orderId)) {
      return res.status(400).json({ 
        success: false, 
        message: 'Invalid order ID' 
      });
    }
    
    // Find the order
    const order = await Order.findById(orderId);
    if (!order) {
      return res.status(404).json({ 
        success: false, 
        message: 'Order not found' 
      });
    }
    
    // Check if order is already paid
    if (order.paymentStatus === 'paid') {
      return res.status(400).json({ 
        success: false, 
        message: 'Order has already been paid' 
      });
    }
    
    // Calculate amount in cents
    const amount = Math.round(order.totalAmount * 100);
    
    // Create a payment intent
    const paymentIntent = await stripe.paymentIntents.create({
      amount,
      currency: 'aed', // UAE currency
      metadata: {
        orderId: order._id.toString(),
        customerName: order.customerName,
        customerEmail: order.email
      },
      description: `Payment for order #${order._id} - JunkExperts`,
      receipt_email: order.email
    });
    
    // Update order with payment intent ID
    order.stripePaymentIntentId = paymentIntent.id;
    await order.save();
    
    // Return client secret
    res.json({
      success: true,
      clientSecret: paymentIntent.client_secret,
      totalAmount: order.totalAmount
    });
  } catch (error) {
    console.error('Error creating payment intent:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Failed to create payment intent' 
    });
  }
};

// Handle Stripe webhook
exports.handleWebhook = async (req, res) => {
  const signature = req.headers['stripe-signature'];
  let event;
  
  try {
    // Verify webhook signature
    const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;
    if (!webhookSecret) {
      // If webhook secret is not set, skip signature verification in development
      if (process.env.NODE_ENV === 'production') {
        return res.status(400).json({ received: false, message: 'Webhook secret not configured' });
      }
      event = req.body;
    } else {
      event = stripe.webhooks.constructEvent(req.body, signature, webhookSecret);
    }
    
    // Handle different event types
    switch (event.type) {
      case 'payment_intent.succeeded':
        await handleSuccessfulPayment(event.data.object);
        break;
      case 'payment_intent.payment_failed':
        await handleFailedPayment(event.data.object);
        break;
      // Add more event handlers as needed
    }
    
    res.json({ received: true });
  } catch (error) {
    console.error('Webhook error:', error);
    res.status(400).json({ received: false, error: error.message });
  }
};

// Handle successful payment
async function handleSuccessfulPayment(paymentIntent) {
  try {
    // Check if this is a pending order (not yet in database)
    if (paymentIntent.metadata.isPendingOrder === 'true') {
      // Store payment info in metadata to be used when customer visits success page
      console.log(`Payment successful for pending order. Order will be created when customer visits success page.`);
      return;
    }
    
    // Regular flow for database orders
    const orderId = paymentIntent.metadata.orderId;
    
    // Update order status
    const order = await Order.findById(orderId);
    if (order) {
      order.paymentStatus = 'paid';
      order.paidAt = new Date();
      order.stripePaymentIntentId = paymentIntent.id;
      order.paymentId = paymentIntent.id; // Store payment ID in standard field
      await order.save();
      
      // Log payment success but don't send emails yet
      console.log(`Payment successful for order ${orderId}. Emails will be sent when user reaches success page.`);
      
      // NOTE: Emails will be sent only when the user reaches the success page
      // This prevents emails from being sent too early, before the user sees the success page
    }
  } catch (error) {
    console.error('Error handling successful payment:', error);
  }
}

// Handle failed payment
async function handleFailedPayment(paymentIntent) {
  try {
    const orderId = paymentIntent.metadata.orderId;
    
    // Update order status
    const order = await Order.findById(orderId);
    if (order) {
      order.paymentStatus = 'failed';
      order.paymentError = paymentIntent.last_payment_error?.message || 'Payment failed';
      await order.save();
      
      // Add here: Notify admin about failed payment
      // emailService.notifyAdminOfFailedPayment(order);
    }
  } catch (error) {
    console.error('Error handling failed payment:', error);
  }
}

// Get Stripe payment page for an order
exports.getPaymentPage = async (req, res) => {
  try {
    const orderId = req.params.orderId;
    
    // Handle pending orders that haven't been saved to the database yet
    if (orderId === 'pending') {
      // Check if we have pending order data in the session
      if (!req.session.pendingOrderData) {
        req.session.error = 'No pending order found. Please try again.';
        return res.redirect('/booking');
      }
      
      const orderData = req.session.pendingOrderData;
      
      // Get service info if needed
      const Service = require('../models/Service');
      let servicesData = orderData.servicesData || [];
      
      // If we don't have services data, get it from the database
      if (servicesData.length === 0) {
        try {
          if (orderData.service) {
            const service = await Service.findById(orderData.service);
            if (service) {
              servicesData.push({
                id: service._id,
                name: service.name,
                price: service.price,
                image: service.image
              });
            }
          }
          
          // Add additional services if they exist
          if (orderData.additionalServices && orderData.additionalServices.length > 0) {
            for (const additionalServiceId of orderData.additionalServices) {
              const service = await Service.findById(additionalServiceId);
              if (service) {
                servicesData.push({
                  id: service._id,
                  name: service.name,
                  price: service.price,
                  image: service.image
                });
              }
            }
          }
          
          // Update session data with services info
          orderData.servicesData = servicesData;
          req.session.pendingOrderData = orderData;
        } catch (error) {
          console.error('Error fetching service data for pending order:', error);
        }
      }
      
      // Create temp order object for view
      const pendingOrder = {
        ...orderData,
        _id: 'pending',
        servicesData
      };
      
      // Render payment page with pending order data
      return res.render('payment/stripe', {
        title: 'Pay with Card | JunkExperts',
        order: pendingOrder,
        stripePublishableKey: process.env.STRIPE_PUBLISHABLE_KEY
      });
    }
    
    // Regular flow for existing orders
    if (!mongoose.Types.ObjectId.isValid(orderId)) {
      req.session.error = 'Invalid order ID';
      return res.redirect('/');
    }
    
    // Find the order and populate the service to get its image
    const order = await Order.findById(orderId)
      .populate('service')
      .populate('additionalServices');
      
    if (!order) {
      req.session.error = 'Order not found';
      return res.redirect('/');
    }
    
    // If order is already paid, redirect to success page
    if (order.paymentStatus === 'paid') {
      req.session.success = 'This order has already been paid';
      return res.redirect(`/payment/success/${orderId}`);
    }
    
    // Ensure services data is available for the template
    if (!order.servicesData || order.servicesData.length === 0) {
      order.servicesData = [];
      
      // Add primary service
      if (order.service && typeof order.service === 'object') {
        order.servicesData.push({
          id: order.service._id,
          name: order.service.name,
          price: order.service.price,
          image: order.service.image
        });
      }
      
      // Add additional services
      if (order.additionalServices && order.additionalServices.length > 0) {
        order.additionalServices.forEach(service => {
          if (service && typeof service === 'object') {
            order.servicesData.push({
              id: service._id,
              name: service.name,
              price: service.price,
              image: service.image
            });
          }
        });
      }
    }
    
    // Render payment page
    res.render('payment/stripe', {
      title: 'Pay with Card | JunkExperts',
      order,
      stripePublishableKey: process.env.STRIPE_PUBLISHABLE_KEY
    });
  } catch (error) {
    console.error('Error getting payment page:', error);
    req.session.error = 'Failed to load payment page';
    res.redirect('/');
  }
};

// Payment success page
exports.getPaymentSuccessPage = async (req, res) => {
  try {
    const orderId = req.params.orderId;
    let order;
    
    // Handle successful payment for pending orders
    if (orderId === 'pending') {
      // Check if we have pending order data in session
      if (!req.session.pendingOrderData || !req.session.pendingOrderPaymentIntentId) {
        req.session.error = 'Payment information not found';
        return res.redirect('/');
      }
      
      // Get order data from session
      const orderData = req.session.pendingOrderData;
      const paymentIntentId = req.session.pendingOrderPaymentIntentId;
      
      // Finalize order (create in database)
      const orderController = require('./orderController');
      order = await orderController.finalizeOrder(orderData, paymentIntentId);
      
      if (!order) {
        req.session.error = 'Failed to finalize your order. Please contact support.';
        return res.redirect('/');
      }
      
      // Send confirmation emails only after order is created and customer is on success page
      try {
        const { sendOrderConfirmationToCustomer, sendOrderNotificationToAdmin, 
                sendPaymentConfirmationEmail, sendPaymentNotificationToAdmin } = require('../config/email');
        
        // Send both order and payment confirmation emails
        await sendOrderConfirmationToCustomer(order);
        await sendOrderNotificationToAdmin(order);
        await sendPaymentConfirmationEmail(order);
        await sendPaymentNotificationToAdmin(order);
        
        // Mark emails as sent
        order.confirmationEmailSent = true;
        await order.save();
        
        console.log('All confirmation emails sent for new order after payment');
        
        // Clear pending order data from session
        delete req.session.pendingOrderData;
        delete req.session.pendingOrderPaymentIntentId;
      } catch (emailError) {
        console.error('Error sending emails for new order after payment:', emailError);
      }
      
      // Populate necessary data for view
      const Service = require('../models/Service');
      try {
        order.service = await Service.findById(order.service);
        if (order.additionalServices && order.additionalServices.length > 0) {
          order.additionalServices = await Promise.all(
            order.additionalServices.map(id => Service.findById(id))
          );
        }
      } catch (err) {
        console.error('Error populating service data:', err);
      }
    } else {
      // Regular flow for database orders
      if (!mongoose.Types.ObjectId.isValid(orderId)) {
        req.session.error = 'Invalid order ID';
        return res.redirect('/');
      }
      
      // Find the order and populate services
      order = await Order.findById(orderId)
        .populate('service')
        .populate('additionalServices');
        
      if (!order) {
        req.session.error = 'Order not found';
        return res.redirect('/');
      }
      
      // Ensure services data is available for the template
      if (!order.servicesData || order.servicesData.length === 0) {
        order.servicesData = [];
        
        // Add primary service
        if (order.service && typeof order.service === 'object') {
          order.servicesData.push({
            id: order.service._id,
            name: order.service.name,
            price: order.service.price,
            image: order.service.image
          });
        }
        
        // Add additional services
        if (order.additionalServices && order.additionalServices.length > 0) {
          order.additionalServices.forEach(service => {
            if (service && typeof service === 'object') {
              order.servicesData.push({
                id: service._id,
                name: service.name,
                price: service.price,
                image: service.image
              });
            }
          });
        }
      }
      
      // Check if the order is paid and if confirmation emails have been sent
      if (order.paymentStatus === 'paid' && !order.confirmationEmailSent) {
        // Send confirmation emails
        try {
          await sendPaymentConfirmationEmail(order);
          await sendPaymentNotificationToAdmin(order);
          
          // Mark emails as sent to prevent duplicate emails
          order.confirmationEmailSent = true;
          await order.save();
          
          console.log('Payment confirmation emails sent when user reached success page');
        } catch (emailError) {
          console.error('Error sending payment emails from success page:', emailError);
        }
      } else if (order.paymentStatus !== 'paid') {
        // If for some reason the order is not marked as paid yet
        order.paymentStatus = 'paid';
        order.paidAt = new Date();
        order.confirmationEmailSent = true; // Mark as sent
        await order.save();
        
        // Send confirmation emails
        try {
          await sendPaymentConfirmationEmail(order);
          await sendPaymentNotificationToAdmin(order);
          console.log('Payment confirmation emails sent on success page load');
        } catch (emailError) {
          console.error('Error sending payment emails from success page:', emailError);
        }
      }
    }
    
    res.render('payment/success', {
      title: 'Payment Successful | JunkExperts',
      order
    });
  } catch (error) {
    console.error('Error getting success page:', error);
    req.session.error = 'Failed to load success page';
    res.redirect('/');
  }
};

// Payment failed page
exports.getPaymentFailedPage = async (req, res) => {
  try {
    const orderId = req.params.orderId;
    
    // Find the order and populate services
    const order = await Order.findById(orderId)
      .populate('service')
      .populate('additionalServices');
      
    if (!order) {
      req.session.error = 'Order not found';
      return res.redirect('/');
    }
    
    // Ensure services data is available for the template
    if (!order.servicesData || order.servicesData.length === 0) {
      order.servicesData = [];
      
      // Add primary service
      if (order.service && typeof order.service === 'object') {
        order.servicesData.push({
          id: order.service._id,
          name: order.service.name,
          price: order.service.price,
          image: order.service.image
        });
      }
      
      // Add additional services
      if (order.additionalServices && order.additionalServices.length > 0) {
        order.additionalServices.forEach(service => {
          if (service && typeof service === 'object') {
            order.servicesData.push({
              id: service._id,
              name: service.name,
              price: service.price,
              image: service.image
            });
          }
        });
      }
    }
    
    // Check if there's an error message in the query string
    const errorMessage = req.query.error;
    if (errorMessage && !order.paymentError) {
      order.paymentError = errorMessage;
      order.paymentStatus = 'failed';
      await order.save();
    }
    
    // Send notification to admin about the failed payment
    try {
      const adminMessage = {
        orderId: order._id,
        customerName: order.customerName,
        email: order.email,
        service: order.service,
        amount: order.totalAmount,
        error: order.paymentError || 'Unknown payment error'
      };
      
      // You can add a notification to admin here if needed
      console.log('Payment failed notification:', adminMessage);
    } catch (notifyError) {
      console.error('Failed to notify admin of payment failure:', notifyError);
    }
    
    res.render('payment/failed', {
      title: 'Payment Failed | JunkExperts',
      order,
      errorMessage: order.paymentError || 'Your payment could not be processed'
    });
  } catch (error) {
    console.error('Error getting failed page:', error);
    req.session.error = 'Failed to load error page';
    res.redirect('/');
  }
}; 