const User = require('../models/User');

// Check if user is authenticated
exports.isAuthenticated = async (req, res, next) => {
  try {
    // Detect if we're coming from a logout request 
    const referer = req.get('Referer') || '';
    const isFromLogout = referer.includes('/auth/logout');
    
    // If we're coming from logout, don't allow authentication using stale data
    if (isFromLogout) {
      req.session.isAuthenticated = false;
      req.session.userId = null;
      req.session.userRole = null;
      req.session.userName = null;
      
      // Clear cookies again as an extra precaution
      res.clearCookie('connect.sid');
      res.clearCookie('admin-auth');
      res.clearCookie('junkexpert.sid');
      
      // Redirect to login with error
      req.flash('error', 'You have been logged out');
      return res.redirect('/auth/login');
    }
    
    // Get current path to check if we're on admin pages
    const currentPath = req.path || '';
    const isAdminPage = currentPath.startsWith('/admin');
    const isLoginPage = currentPath.startsWith('/auth/login');
    const isLandingPage = currentPath === '/';
    
    // Get platform information
    const hostname = req.hostname || '';
    const host = req.get('host') || '';
    const isRender = hostname.includes('onrender.com') || host.includes('onrender.com');
    const isVercel = hostname.includes('vercel.app') || host.includes('vercel.app');
    
    // Check for fresh login (within the last 5 minutes)
    const isFreshLogin = req.session && req.session.loginTime && 
                        (Date.now() - req.session.loginTime < 5 * 60 * 1000);
    
    // Check for Vercel-specific auth flag
    const hasVercelAuth = req.session && req.session.vercelAuth === true;
    
    // Only allow cookie-based auto-login for admin pages or when explicitly logged in
    const allowCookieLogin = isAdminPage || isFreshLogin || hasVercelAuth;
    
    // Log session state for debugging
    console.log('Auth check - Session:', {
      path: currentPath,
      isAuthenticated: req.session?.isAuthenticated,
      userId: req.session?.userId,
      userRole: req.session?.userRole,
      sessionID: req.sessionID,
      isFreshLogin,
      platform: isRender ? 'Render' : isVercel ? 'Vercel' : 'Other',
      hasVercelAuth,
      allowCookieLogin
    });

    // VERCEL SPECIAL CASE - prioritize cookie auth for admins on Vercel
    if (allowCookieLogin && isVercel && req.cookies['admin-auth'] && req.session?.vercelAuth) {
      console.log('Vercel platform detected with admin cookie - special auth flow');
      return next();
    }

    // First priority: Check fresh login session
    if (isFreshLogin && req.session?.isAuthenticated) {
      console.log('Using fresh login session');
      return next();
    }

    // Second priority: Check for Vercel-specific auth flag
    if (isVercel && hasVercelAuth && req.session?.isAuthenticated) {
      console.log('Using Vercel-specific auth flag');
      return next();
    }

    // Third priority: Check for admin cookie (admin auto-login) ONLY FOR ADMIN PAGES
    if (allowCookieLogin && req.cookies['admin-auth']) {
      console.log('Admin cookie found, attempting auth via cookie for admin page');
      const admin = await User.findOne({ role: 'admin' });
      if (admin) {
        req.session.isAuthenticated = true;
        req.session.userId = admin._id.toString();
        req.session.userRole = 'admin';
        req.session.userName = admin.name || 'Admin';
        
        // For Vercel, add the special flag
        if (isVercel) {
          req.session.vercelAuth = true;
          
          // Try to save session but don't wait for it
          req.session.save(err => {
            if (err) console.error('Error saving Vercel session in isAuthenticated:', err);
          });
        }
        
        return next();
      }
    }

    // Fourth priority: Check normal session
    if (req.session?.isAuthenticated) {
      return next();
    }

    // If not authenticated, redirect to login
    req.flash('error', 'Please log in to access this page');
    res.redirect('/auth/login');
  } catch (error) {
    console.error('Auth middleware error:', error);
    req.flash('error', 'Authentication error occurred');
    res.redirect('/auth/login');
  }
};

// Check if user is admin
exports.isAdmin = async (req, res, next) => {
  try {
    // Detect if we're coming from a logout request 
    const referer = req.get('Referer') || '';
    const isFromLogout = referer.includes('/auth/logout');
    
    // If we're coming from logout, don't allow authentication using stale data
    if (isFromLogout) {
      req.session.isAuthenticated = false;
      req.session.userId = null;
      req.session.userRole = null;
      req.session.userName = null;
      
      // Clear cookies again as an extra precaution
      res.clearCookie('connect.sid');
      res.clearCookie('admin-auth');
      res.clearCookie('junkexpert.sid');
      
      // Redirect to login with error
      req.flash('error', 'You have been logged out');
      return res.redirect('/auth/login');
    }
    
    // Get current path to check if we're on admin pages
    const currentPath = req.path || '';
    const isAdminPage = currentPath.startsWith('/admin');
    const isLoginPage = currentPath.startsWith('/auth/login');
    const isLandingPage = currentPath === '/';
    
    // Get the platform information
    const hostname = req.hostname || '';
    const host = req.get('host') || '';
    const isRender = hostname.includes('onrender.com') || host.includes('onrender.com');
    const isVercel = hostname.includes('vercel.app') || host.includes('vercel.app');
    
    // Check for fresh login (within the last 5 minutes)
    const isFreshLogin = req.session && req.session.loginTime && 
                        (Date.now() - req.session.loginTime < 5 * 60 * 1000);
    
    // Check for Vercel-specific auth flag
    const hasVercelAuth = req.session && req.session.vercelAuth === true;
    
    // Get admin cookie
    const adminCookie = req.cookies['admin-auth'];
    
    // Only allow cookie-based auto-login for admin pages or when explicitly logged in
    const allowCookieLogin = isAdminPage || isFreshLogin || hasVercelAuth;
    
    // Log admin check details with platform info for debugging
    console.log('Admin check - Session:', {
      path: currentPath,
      isAuthenticated: req.session?.isAuthenticated,
      userRole: req.session?.userRole,
      userId: req.session?.userId,
      isFreshLogin,
      platform: isRender ? 'Render' : isVercel ? 'Vercel' : 'Other',
      hostname,
      hasCookie: !!adminCookie,
      hasVercelAuth,
      allowCookieLogin
    });

    // -- Authentication Priority Chain --
    
    // First check: Special Vercel auth based on session flag
    if (isVercel && hasVercelAuth && req.session?.userRole === 'admin') {
      console.log('Access granted: Using Vercel session flag');
      return next();
    }
    
    // Second check: Platform-specific cookie auth - ONLY FOR ADMIN PAGES OR EXPLICIT LOGIN
    if (allowCookieLogin && adminCookie) {
      // VERCEL - prioritize cookie auth on Vercel (for admin pages or explicit login only)
      if (isVercel) {
        console.log('Vercel platform detected with admin cookie - applying special auth flow');
        
        // On Vercel, cookies are more reliable than sessions
        const admin = await User.findOne({ role: 'admin' });
        if (admin) {
          // Refresh the session data
          req.session.isAuthenticated = true;
          req.session.userId = admin._id.toString();
          req.session.userRole = 'admin';
          req.session.userName = admin.name || 'Admin';
          req.session.loginTime = Date.now();
          req.session.vercelAuth = true;
          
          // Save the session in the background (don't await)
          req.session.save(err => {
            if (err) console.error('Error saving Vercel session:', err);
          });
          
          console.log('Access granted: Admin verified via cookie on Vercel');
          return next();
        }
      } 
      // RENDER - only use cookie auth if explicitly allowed or the cookie value matches environment setting
      // AND we're on an admin page or have explicit login
      else if (isRender && 
              (process.env.ALLOW_RENDER_AUTO_LOGIN === 'true' || 
               adminCookie === process.env.ADMIN_COOKIE_VALUE)) {
        console.log('Render detected: Checking admin cookie authentication');
        const admin = await User.findOne({ role: 'admin' });
        
        if (admin) {
          // Set up session data
          req.session.isAuthenticated = true;
          req.session.userId = admin._id.toString();
          req.session.userRole = 'admin';
          req.session.userName = admin.name || 'Admin';
          req.session.loginTime = Date.now();
          console.log('Access granted: Admin verified via cookie on Render');
          return next();
        }
      }
      // OTHER PLATFORMS - only use cookie if explicitly enabled or in development
      // AND we're on an admin page or have explicit login
      else if (allowCookieLogin && 
              (process.env.ALLOW_COOKIE_LOGIN === 'true' || process.env.NODE_ENV === 'development')) {
        console.log('Cookie auth attempt allowed on this platform');
        const admin = await User.findOne({ role: 'admin' });
        
        if (admin) {
          // Set up session data
          req.session.isAuthenticated = true;
          req.session.userId = admin._id.toString();
          req.session.userRole = 'admin';
          req.session.userName = admin.name || 'Admin';
          req.session.loginTime = Date.now();
          console.log('Access granted: Admin verified via cookie');
          return next();
        }
      } else {
        console.log('Cookie auth not allowed on this platform configuration');
      }
    }
    
    // Third check: Fresh login session
    if (isFreshLogin && req.session?.isAuthenticated && req.session?.userRole === 'admin') {
      console.log('Access granted: Using fresh login admin session');
      return next();
    }
    
    // Fourth check: Regular session
    if (req.session?.isAuthenticated && req.session?.userRole === 'admin') {
      console.log('Access granted: Using existing admin session');
      return next();
    }

    // If all authentication methods fail, deny access
    console.log('Access denied: Not authenticated as admin');
    req.flash('error', 'Admin access required. Please log in with admin credentials.');
    return res.redirect('/auth/login');
  } catch (error) {
    console.error('Admin middleware error:', error);
    req.flash('error', 'Error checking admin privileges');
    return res.status(403).redirect('/auth/login');
  }
};

// Set user data for all views
exports.setUserData = (req, res, next) => {
  try {
    // Reset any existing user data that might be cached
    res.locals.user = null;
    
    // Set a default empty user object
    let userData = {
      isAuthenticated: false,
      id: null,
      role: null,
      name: null
    };
    
    // Detect logout by checking for the logout parameter in the URL
    const isLoggingOut = req.query.logout !== undefined;
    
    // Get current path to check if we're on admin pages
    const currentPath = req.path || '';
    const isAdminPage = currentPath.startsWith('/admin');
    const isLoginPage = currentPath.startsWith('/auth/login');
    const isLandingPage = currentPath === '/';
    
    // Get platform information
    const hostname = req.hostname || '';
    const host = req.get('host') || '';
    const isRender = hostname.includes('onrender.com') || host.includes('onrender.com');
    const isVercel = hostname.includes('vercel.app') || host.includes('vercel.app');
    
    // Check if we have an explicit login timestamp from the login process
    const hasExplicitLogin = req.session && req.session.loginTime && 
                           (Date.now() - req.session.loginTime < 60 * 60 * 1000); // 1 hour threshold
    
    // Check if session exists and is properly authenticated
    const isValidSession = !isLoggingOut && 
                          req.session && 
                          req.session.isAuthenticated === true && 
                          req.session.userId && 
                          req.session.userRole;
    
    // IMPORTANT: Only use auto-login with cookies if:
    // 1. We're on an admin page (not the landing page)
    // 2. OR we're accessing from Vercel/Render with explicit login
    // 3. OR we have an explicit login timestamp from the login process
    const shouldAllowAutoLogin = !isLandingPage || 
                               (hasExplicitLogin) || 
                               (isAdminPage && (isRender || isVercel));
    
    if (isValidSession) {
      // Always populate from session if it exists
      userData = {
        isAuthenticated: true,
        id: req.session.userId,
        role: req.session.userRole,
        name: req.session.userName
      };
      
      // CRITICAL FIX: Don't show admin role on landing page unless explicitly logged in
      if (isLandingPage && !hasExplicitLogin && userData.role === 'admin') {
        console.log('Hiding admin role on landing page without explicit login');
        userData.role = 'user'; // Temporarily downgrade to hide admin UI elements
      }
      
      // Also attach to req.user for controller use
      req.user = {
        id: req.session.userId,
        role: isLandingPage && !hasExplicitLogin && req.session.userRole === 'admin' ? 'user' : req.session.userRole,
        name: req.session.userName
      };
      
      // Only log successful authentications if needed for debugging
      if (process.env.SESSION_DEBUG === 'true') {
        console.log('User authenticated from session:', userData.name, 'role:', userData.role, 'path:', currentPath);
      }
    } 
    // If we don't have a valid session but should allow auto-login
    else if (shouldAllowAutoLogin && req.cookies['admin-auth'] && !isLoggingOut) {
      // Special case for admin cookie auto-login - only if accessing admin pages or after explicit login
      console.log('Trying cookie auth for path:', currentPath, 'admin page:', isAdminPage, 'has explicit login:', hasExplicitLogin);
      
      // Do not use this path for landing page without explicit login
      if (!isLandingPage || hasExplicitLogin) {
        const adminCookie = req.cookies['admin-auth'];
        
        // We'll only set the user info but not modify the session
        // This is enough for displaying the right navbar options
        userData = {
          isAuthenticated: true,
          id: 'admin',
          role: isLandingPage && !hasExplicitLogin ? 'user' : 'admin', // Hide admin on landing page
          name: 'Admin'
        };
        
        // Also attach to req.user for controller use
        req.user = {
          id: 'admin',
          role: isLandingPage && !hasExplicitLogin ? 'user' : 'admin', // Hide admin on landing page
          name: 'Admin'
        };
        
        if (process.env.SESSION_DEBUG === 'true') {
          console.log('Admin auto-login from cookie for path:', currentPath, 'showing role:', userData.role);
        }
      }
    } else {
      // No valid auth - ensure no req.user exists if not authenticated
      req.user = null;
      
      // If we're logging out, make sure session has no auth data
      if (isLoggingOut && req.session) {
        req.session.isAuthenticated = false;
        req.session.userId = null;
        req.session.userRole = null;
        req.session.userName = null;
        
        // Also clear admin cookie to ensure complete logout
        res.clearCookie('admin-auth', { path: '/' });
        res.clearCookie('connect.sid', { path: '/' });
      }
    }
    
    // DIRECT FIX: Explicitly check and override for landing page
    if (isLandingPage && !hasExplicitLogin && userData.role === 'admin') {
      console.log('Final check: Hiding admin role on landing page');
      userData.role = 'user';
      if (req.user) req.user.role = 'user';
    }
    
    // Set the user data in res.locals for EJS templates
    res.locals.user = userData;
    next();
  } catch (error) {
    console.error('Error setting user data:', error);
    // Ensure user is not authenticated on error
    res.locals.user = { isAuthenticated: false };
    req.user = null;
    next();
  }
}; 