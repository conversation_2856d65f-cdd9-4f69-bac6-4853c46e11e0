<%- include('../partials/header') %>

<div class="pt-24 pb-16 bg-gray-50">
    <div class="container mx-auto px-4">
        
        <!-- Page Header -->
        <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
            <div>
                <h1 class="text-2xl font-bold text-gray-800">Manage Orders</h1>
                <p class="text-gray-600">View, filter, and manage all customer orders</p>
            </div>
            <div class="mt-4 md:mt-0 flex items-center">
                <button id="force-refresh" class="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-opacity-50 mr-4">
                    <i class="fas fa-sync-alt mr-1"></i> Force Refresh
                </button>
                <a href="/admin/dashboard" class="text-blue-600 hover:text-blue-800">
                    <i class="fas fa-arrow-left mr-1"></i> Back to Dashboard
                </a>
            </div>
        </div>
        
        <!-- Filters -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-lg font-bold text-gray-800 mb-4">Filter Orders</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label for="status-filter" class="block text-sm font-medium text-gray-700 mb-1">Payment Status</label>
                    <select id="status-filter" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                        <option value="">All Statuses</option>
                        <option value="pending">Pending</option>
                        <option value="payment did not clear">Payment Did Not Clear</option>
                    </select>
                </div>
                
                <div>
                    <label for="date-filter" class="block text-sm font-medium text-gray-700 mb-1">Date Range</label>
                    <select id="date-filter" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                        <option value="">All Time</option>
                        <option value="today">Today</option>
                        <option value="week">This Week</option>
                        <option value="month">This Month</option>
                    </select>
                </div>
                
                <div>
                    <label for="payment-method-filter" class="block text-sm font-medium text-gray-700 mb-1">Payment Method</label>
                    <select id="payment-method-filter" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                        <option value="">All Methods</option>
                        <option value="cash">Cash on Service</option>
                        <option value="card">Credit/Debit Card</option>
                        <option value="bank_transfer">Bank Transfer</option>
                    </select>
                </div>
                
                <div>
                    <label for="search" class="block text-sm font-medium text-gray-700 mb-1">Search</label>
                    <input type="text" id="search" placeholder="Name, Email, Order ID..." class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                </div>
            </div>
            
            <div class="mt-4 flex justify-end">
                <button id="apply-filters" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50">
                    Apply Filters
                </button>
            </div>
        </div>
        
        <!-- Orders Table -->
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <div class="flex justify-between items-center p-6 border-b">
                <h2 class="text-lg font-bold text-gray-800">Orders</h2>
                <div>
                    <select id="sort-orders" class="rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                        <option value="date-desc">Newest First</option>
                        <option value="date-asc">Oldest First</option>
                        <option value="amount-desc">Highest Amount</option>
                        <option value="amount-asc">Lowest Amount</option>
                    </select>
                </div>
            </div>
            
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Order ID
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Customer
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Service
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Date
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Amount
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Payment Method
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Status
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Actions
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200" id="orders-table-body">
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500" colspan="8">
                                Loading orders...
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            <div class="px-6 py-4 border-t flex items-center justify-between">
                <div class="text-sm text-gray-700">
                    <span id="pagination-info">Showing 0 to 0 of 0 results</span>
                </div>
                <div class="flex-1 flex justify-between sm:justify-end">
                    <button id="prev-page" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                        Previous
                    </button>
                    <button id="next-page" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                        Next
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Order Details Modal -->
<div id="order-details-modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50">
    <div class="bg-white rounded-lg max-w-2xl w-full overflow-hidden shadow-xl transform transition-all">
        <div class="bg-gray-100 px-6 py-4 flex justify-between items-center">
            <h3 class="text-lg font-bold text-gray-900" id="modal-order-id">Order Details</h3>
            <button class="text-gray-400 hover:text-gray-500" id="close-modal">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="px-6 py-4" id="modal-content">
            <div class="animate-pulse">
                <div class="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
                <div class="h-4 bg-gray-200 rounded w-1/2 mb-4"></div>
                <div class="h-4 bg-gray-200 rounded w-5/6 mb-4"></div>
                <div class="h-4 bg-gray-200 rounded w-2/3 mb-4"></div>
            </div>
        </div>
        <div class="bg-gray-50 px-6 py-4 flex justify-end" id="modal-actions">
            <button class="bg-gray-500 text-white px-4 py-2 rounded mr-2 hover:bg-gray-600" id="close-modal-btn">
                Close
            </button>
        </div>
    </div>
</div>

<!-- Toast Container for dynamic notifications -->
<div id="toast-container" class="fixed bottom-4 right-4 flex flex-col gap-2 z-50"></div>

<script>
let currentPage = 1;
let totalPages = 1;
let currentOrders = [];
let allOrders = [];
let currentDeleteOrderId = null;

// Initialize the page
document.addEventListener('DOMContentLoaded', function() {
    addArchivedOrdersFilter();
    fetchOrders();
    setupEventListeners();
});

// Add checkbox for archived orders
function addArchivedOrdersFilter() {
    const filterContainer = document.querySelector('.filters');
    if (!filterContainer) return;
    
    const archivedCheckbox = document.createElement('div');
    archivedCheckbox.className = 'flex items-center ml-4';
    archivedCheckbox.innerHTML = `
        <input type="checkbox" id="showArchivedFilter" class="mr-2">
        <label for="showArchivedFilter" class="text-sm text-gray-700">Show Archived Orders</label>
    `;
    
    filterContainer.appendChild(archivedCheckbox);
    
    // Add event listener
    document.getElementById('showArchivedFilter').addEventListener('change', function() {
        fetchOrders();
    });
}

// Fetch orders from API
async function fetchOrders() {
    try {
        console.log("Fetching orders from server...");
        
        // Get filter values
        const statusFilter = document.getElementById('status-filter').value;
        const dateFilter = document.getElementById('date-filter').value;
        const paymentMethodFilter = document.getElementById('payment-method-filter').value;
        const searchTerm = document.getElementById('search').value.toLowerCase();
        const sortOption = document.getElementById('sort-orders').value;
        const showArchived = document.getElementById('showArchivedFilter')?.checked || false;
        
        // Build query string
        let queryParams = new URLSearchParams();
        if (statusFilter) queryParams.append('status', statusFilter);
        if (dateFilter) queryParams.append('date', dateFilter);
        if (paymentMethodFilter) queryParams.append('paymentMethod', paymentMethodFilter);
        if (searchTerm) queryParams.append('search', searchTerm);
        if (showArchived) queryParams.append('showArchived', 'true');
        
        // Add a cache busting param
        queryParams.append('_t', new Date().getTime());
        
        // Fetch orders from API
        const response = await fetch(`/admin/api/orders?${queryParams.toString()}`, {
            headers: {
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0'
            }
        });
        
        console.log("Server response status:", response.status);
        
        // Check if the response is ok before proceeding
        if (!response.ok) {
            throw new Error(`Server returned ${response.status}: ${response.statusText}`);
        }
        
        let orders = await response.json();
        console.log("Raw data from server:", orders);
        console.log("Number of orders returned:", orders.length);
        
        // Important: Clear the arrays first to avoid showing stale data
        allOrders = [];
        currentOrders = [];
        
        if (!Array.isArray(orders) || orders.length === 0) {
            console.log("No orders returned from server");
            displayNoOrders();
            return;
        }
        
        // Check that orders have expected structure
        if (!orders[0]._id) {
            console.error("Invalid order data structure:", orders[0]);
            throw new Error("Invalid order data structure received from server");
        }
        
        // Filter to only keep orders with BOTH "pending" payment status AND pending order status
        allOrders = orders.filter(order => {
            // Show only orders where payment is pending AND order is not marked by admin
            return order.paymentStatus === 'pending' && order.status === 'pending';
        });
        
        console.log(`Filtered to ${allOrders.length} orders after applying payment and admin status filters`);
        
        // Apply initial filtering and sorting
        filterAndDisplayOrders();
    } catch (error) {
        console.error('Error fetching orders:', error);
        document.getElementById('orders-table-body').innerHTML = `
            <tr>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-red-500" colspan="8">
                    Error loading orders: ${error.message}
                </td>
            </tr>
        `;
    }
}

// Function to display message when no orders exist
function displayNoOrders() {
    document.getElementById('orders-table-body').innerHTML = `
        <tr>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500" colspan="8">
                No orders found in the database.
            </td>
        </tr>
    `;
    
    // Update pagination info
    document.getElementById('pagination-info').textContent = `Showing 0 to 0 of 0 results`;
    
    // Disable pagination buttons
    document.getElementById('prev-page').disabled = true;
    document.getElementById('next-page').disabled = true;
}

// Set up event listeners
function setupEventListeners() {
    // Apply filters button
    document.getElementById('apply-filters').addEventListener('click', filterAndDisplayOrders);
    
    // Sort dropdown
    document.getElementById('sort-orders').addEventListener('change', filterAndDisplayOrders);
    
    // Force refresh button
    document.getElementById('force-refresh').addEventListener('click', () => {
        const notification = document.createElement('div');
        notification.className = 'fixed bottom-4 right-4 bg-blue-100 border-l-4 border-blue-500 text-blue-700 p-4 rounded shadow-md z-50';
        notification.innerHTML = `
            <div class="flex items-center">
                <i class="fas fa-sync-alt fa-spin mr-2"></i>
                <p>Forcing data refresh from database...</p>
            </div>
        `;
        document.body.appendChild(notification);
        
        // Clear any cached data
        allOrders = [];
        currentOrders = [];
        
        // Force browser to clear cache with random parameter
        const timestamp = new Date().getTime();
        const randomVal = Math.floor(Math.random() * 1000000);
        
        // Display loading state
        document.getElementById('orders-table-body').innerHTML = `
            <tr>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500" colspan="8">
                    <div class="flex items-center justify-center">
                        <i class="fas fa-spinner fa-spin mr-2"></i>
                        Forcing fresh data from database...
                    </div>
                </td>
            </tr>
        `;
        
        // Force a hard reload with cache busting
        fetch(`/admin/api/orders?nocache=${timestamp}&random=${randomVal}`, {
            headers: {
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0'
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`Server returned ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('Force refreshed data:', data);
            
            if (!Array.isArray(data) || data.length === 0) {
                notification.innerHTML = `
                    <div class="flex items-center">
                        <i class="fas fa-info-circle mr-2"></i>
                        <p>Confirmed: Database contains no orders</p>
                    </div>
                `;
                setTimeout(() => notification.remove(), 5000);
                displayNoOrders();
                return;
            }
            
            // Update the global arrays - only keep orders with BOTH "pending" payment status AND pending order status
            allOrders = data.filter(order => {
                return order.paymentStatus === 'pending' && order.status === 'pending';
            });
            
            notification.innerHTML = `
                <div class="flex items-center">
                    <i class="fas fa-check-circle mr-2"></i>
                    <p>Got fresh data: ${data.length} orders (${allOrders.length} pending orders)</p>
                </div>
            `;
            setTimeout(() => notification.remove(), 5000);
            
            // Apply initial filtering and sorting
            filterAndDisplayOrders();
        })
        .catch(error => {
            console.error('Force refresh error:', error);
            notification.innerHTML = `
                <div class="flex items-center">
                    <i class="fas fa-exclamation-circle mr-2"></i>
                    <p>Error: ${error.message}</p>
                </div>
            `;
            setTimeout(() => notification.remove(), 5000);
            
            document.getElementById('orders-table-body').innerHTML = `
                <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-red-500" colspan="8">
                        Error loading orders: ${error.message}
                    </td>
                </tr>
            `;
        });
    });
    
    // Pagination buttons
    document.getElementById('prev-page').addEventListener('click', () => {
        if (currentPage > 1) {
            currentPage--;
            displayOrders();
        }
    });
    
    document.getElementById('next-page').addEventListener('click', () => {
        if (currentPage < totalPages) {
            currentPage++;
            displayOrders();
        }
    });
    
    // Modal close buttons
    document.getElementById('close-modal').addEventListener('click', closeModal);
    document.getElementById('close-modal-btn').addEventListener('click', closeModal);
}

// Filter and sort orders
function filterAndDisplayOrders() {
    const statusFilter = document.getElementById('status-filter').value;
    const dateFilter = document.getElementById('date-filter').value;
    const paymentMethodFilter = document.getElementById('payment-method-filter').value;
    const searchTerm = document.getElementById('search').value.toLowerCase();
    const sortOption = document.getElementById('sort-orders').value;
    
    // Filter orders
    currentOrders = allOrders.filter(order => {
        // Status filter
        if (statusFilter && order.paymentStatus !== statusFilter) {
            return false;
        }
        
        // Date filter
        if (dateFilter) {
            const orderDate = new Date(order.date);
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            
            if (dateFilter === 'today' && orderDate < today) {
                return false;
            } else if (dateFilter === 'week') {
                const weekStart = new Date();
                weekStart.setDate(weekStart.getDate() - weekStart.getDay());
                weekStart.setHours(0, 0, 0, 0);
                if (orderDate < weekStart) {
                    return false;
                }
            } else if (dateFilter === 'month') {
                const monthStart = new Date();
                monthStart.setDate(1);
                monthStart.setHours(0, 0, 0, 0);
                if (orderDate < monthStart) {
                    return false;
                }
            }
        }
        
        // Payment method filter
        if (paymentMethodFilter && order.paymentMethod !== paymentMethodFilter) {
            return false;
        }
        
        // Search filter
        if (searchTerm) {
            const searchFields = [
                order._id,
                order.customerName,
                order.email,
                order.phone,
                order.service
            ];
            
            return searchFields.some(field => 
                field && field.toString().toLowerCase().includes(searchTerm)
            );
        }
        
        return true;
    });
    
    // Sort orders
    currentOrders.sort((a, b) => {
        if (sortOption === 'date-desc') {
            return new Date(b.date) - new Date(a.date);
        } else if (sortOption === 'date-asc') {
            return new Date(a.date) - new Date(b.date);
        } else if (sortOption === 'amount-desc') {
            return b.totalAmount - a.totalAmount;
        } else if (sortOption === 'amount-asc') {
            return a.totalAmount - b.totalAmount;
        }
        return 0;
    });
    
    // Reset to first page and display
    currentPage = 1;
    displayOrders();
}

// Display orders with pagination
function displayOrders() {
    const ordersPerPage = 10;
    const startIndex = (currentPage - 1) * ordersPerPage;
    const endIndex = Math.min(startIndex + ordersPerPage, currentOrders.length);
    const ordersToDisplay = currentOrders.slice(startIndex, endIndex);
    
    totalPages = Math.ceil(currentOrders.length / ordersPerPage);
    
    // Update pagination info
    document.getElementById('pagination-info').textContent = 
        `Showing ${startIndex + 1} to ${endIndex} of ${currentOrders.length} results`;
    
    // Update pagination buttons
    document.getElementById('prev-page').disabled = currentPage === 1;
    document.getElementById('next-page').disabled = currentPage === totalPages;
    
    // Update table
    const tableBody = document.getElementById('orders-table-body');
    
    if (ordersToDisplay.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500" colspan="8">
                    No orders found matching the filters.
                </td>
            </tr>
        `;
        return;
    }
    
    tableBody.innerHTML = ordersToDisplay.map(order => `
        <tr>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                ${order._id.substring(0, 8)}...
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                ${order.customerName}<br>
                <span class="text-xs text-gray-400">${order.email}</span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                ${order.service}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                ${new Date(order.date).toLocaleDateString()}<br>
                <span class="text-xs text-gray-400">${new Date(order.date).toLocaleTimeString()}</span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                AED ${(order.totalAmount !== undefined && order.totalAmount !== null) ? order.totalAmount.toFixed(2) : '0.00'}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                ${getPaymentMethodText(order.paymentMethod)}
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                    ${order.paymentStatus === 'paid' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}">
                    ${order.paymentStatus}
                </span>
                <br>
                <span class="px-2 mt-1 inline-flex text-xs leading-5 font-semibold rounded-full 
                    ${order.status === 'completed' ? 'bg-blue-100 text-blue-800' : 
                      order.status === 'confirmed' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}">
                    ${order.status}
                </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                <button class="text-blue-600 hover:text-blue-900 mr-3 view-order" data-id="${order._id}" title="View Details">
                    <i class="fas fa-eye"></i>
                </button>
                ${order.status !== 'completed' && order.paymentStatus === 'paid' ? `
                <button class="text-green-600 hover:text-green-900 mr-3 complete-order" data-id="${order._id}" title="Mark as Completed">
                    <i class="fas fa-check-circle"></i>
                </button>
                ` : ''}
                ${order.paymentStatus === 'pending' && order.paymentMethod === 'cash' ? `
                <button class="text-green-600 hover:text-green-900 mr-3 mark-paid" data-id="${order._id}" title="Mark as Paid">
                    <i class="fas fa-money-bill-wave"></i>
                </button>
                ` : ''}
                ${!order.isPaid && order.status !== 'completed' ? `
                <button class="text-red-600 hover:text-red-900 mr-3 delete-order" data-id="${order._id}" title="Delete Order">
                    <i class="fas fa-archive"></i>
                </button>
                ` : ''}
                <button class="text-indigo-600 hover:text-indigo-900 send-email" data-id="${order._id}" title="Send Email">
                    <i class="fas fa-envelope"></i>
                </button>
            </td>
        </tr>
    `).join('');
    
    // Add event listeners to action buttons
    document.querySelectorAll('.view-order').forEach(button => {
        button.addEventListener('click', () => viewOrderDetails(button.dataset.id));
    });
    
    document.querySelectorAll('.mark-paid').forEach(button => {
        button.addEventListener('click', () => markOrderAsPaid(button.dataset.id));
    });

    document.querySelectorAll('.complete-order').forEach(button => {
        button.addEventListener('click', () => markOrderAsCompleted(button.dataset.id));
    });

    document.querySelectorAll('.delete-order').forEach(button => {
        button.addEventListener('click', () => openDeleteOrderModal(button.dataset.id));
    });
    
    document.querySelectorAll('.send-email').forEach(button => {
        button.addEventListener('click', () => sendReminderEmail(button.dataset.id));
    });
}

// View order details
async function viewOrderDetails(orderId) {
    try {
        const response = await fetch(`/admin/api/orders/${orderId}`);
        const order = await response.json();
        
        const modalContent = document.getElementById('modal-content');
        document.getElementById('modal-order-id').textContent = `Order #${orderId.substring(0, 8)}...`;
        
        modalContent.innerHTML = `
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <h4 class="font-medium text-gray-800">Customer Information</h4>
                    <p class="mt-2">
                        <span class="block font-medium">Name:</span>
                        ${order.customerName}
                    </p>
                    <p class="mt-2">
                        <span class="block font-medium">Email:</span>
                        ${order.email}
                    </p>
                    <p class="mt-2">
                        <span class="block font-medium">Phone:</span>
                        ${order.phone}
                    </p>
                    <p class="mt-2">
                        <span class="block font-medium">Address:</span>
                        ${order.address}
                    </p>
                </div>
                
                <div>
                    <h4 class="font-medium text-gray-800">Order Information</h4>
                    <p class="mt-2">
                        <span class="block font-medium">Service:</span>
                        ${order.service}
                    </p>
                    <p class="mt-2">
                        <span class="block font-medium">Date & Time:</span>
                        ${new Date(order.date).toLocaleDateString()} ${new Date(order.date).toLocaleTimeString()}
                    </p>
                    <p class="mt-2">
                        <span class="block font-medium">Total Amount:</span>
                        AED ${(order.totalAmount !== undefined && order.totalAmount !== null) ? order.totalAmount.toFixed(2) : '0.00'}
                    </p>
                    <p class="mt-2">
                        <span class="block font-medium">Payment Method:</span>
                        ${getPaymentMethodText(order.paymentMethod)}
                    </p>
                    <p class="mt-2">
                        <span class="block font-medium">Payment Status:</span>
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                            ${order.paymentStatus === 'paid' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}">
                            ${order.paymentStatus}
                        </span>
                    </p>
                    ${order.paymentId ? `
                    <p class="mt-2">
                        <span class="block font-medium">Payment ID:</span>
                        ${order.paymentId}
                    </p>
                    ` : ''}
                </div>
            </div>
            
            ${order.message ? `
            <div class="mt-4">
                <h4 class="font-medium text-gray-800">Additional Notes</h4>
                <p class="mt-2 text-gray-600">${order.message}</p>
            </div>
            ` : ''}
        `;
        
        // Update modal actions
        const modalActions = document.getElementById('modal-actions');
        
        if (order.paymentStatus === 'pending' && order.paymentMethod === 'cash') {
            modalActions.innerHTML = `
                <button class="bg-green-600 text-white px-4 py-2 rounded mr-2 hover:bg-green-700" 
                    id="modal-mark-paid" data-id="${order._id}">
                    Mark as Paid
                </button>
                <button class="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600" id="close-modal-btn">
                    Close
                </button>
            `;
            
            document.getElementById('modal-mark-paid').addEventListener('click', function() {
                markOrderAsPaid(this.dataset.id);
            });
            
            document.getElementById('close-modal-btn').addEventListener('click', closeModal);
        } else {
            modalActions.innerHTML = `
                <button class="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600" id="close-modal-btn">
                    Close
                </button>
            `;
            
            document.getElementById('close-modal-btn').addEventListener('click', closeModal);
        }
        
        // Show modal
        document.getElementById('order-details-modal').classList.remove('hidden');
    } catch (error) {
        console.error('Error fetching order details:', error);
        alert('Error loading order details. Please try again.');
    }
}

// Mark order as paid
async function markOrderAsPaid(orderId) {
    if (!confirm('Are you sure you want to mark this order as paid?')) {
        return;
    }
    
    try {
        const response = await fetch(`/admin/cash-payments/${orderId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ status: 'paid' }),
        });
        
        if (response.ok) {
            // Show success notification instead of alert
            const notification = document.createElement('div');
            notification.className = 'fixed bottom-4 right-4 bg-green-100 border-l-4 border-green-500 text-green-700 p-4 rounded shadow-md z-50';
            notification.innerHTML = `
                <div class="flex items-center">
                    <i class="fas fa-check-circle mr-2"></i>
                    <p>Order has been marked as paid successfully.</p>
                </div>
            `;
            document.body.appendChild(notification);
            
            // Remove notification after 3 seconds
            setTimeout(() => {
                notification.remove();
            }, 3000);
            
            closeModal();
            
            // Update the order in our arrays
            const orderInArray = allOrders.find(order => order._id === orderId);
            if (orderInArray) {
                orderInArray.paymentStatus = 'paid';
            }
            
            // Refresh the display
            filterAndDisplayOrders();
        } else {
            const errorNotification = document.createElement('div');
            errorNotification.className = 'fixed bottom-4 right-4 bg-red-100 border-l-4 border-red-500 text-red-700 p-4 rounded shadow-md z-50';
            errorNotification.innerHTML = `
                <div class="flex items-center">
                    <i class="fas fa-exclamation-circle mr-2"></i>
                    <p>Error updating payment status. Please try again.</p>
                </div>
            `;
            document.body.appendChild(errorNotification);
            
            setTimeout(() => {
                errorNotification.remove();
            }, 5000);
        }
    } catch (error) {
        console.error('Error marking order as paid:', error);
        const errorNotification = document.createElement('div');
        errorNotification.className = 'fixed bottom-4 right-4 bg-red-100 border-l-4 border-red-500 text-red-700 p-4 rounded shadow-md z-50';
        errorNotification.innerHTML = `
            <div class="flex items-center">
                <i class="fas fa-exclamation-circle mr-2"></i>
                <p>Error updating payment status: ${error.message || 'Unknown error'}</p>
            </div>
        `;
        document.body.appendChild(errorNotification);
        
        setTimeout(() => {
            errorNotification.remove();
        }, 5000);
    }
}

// Send reminder email
async function sendReminderEmail(orderId) {
    if (!confirm('Are you sure you want to send a reminder email for this order?')) {
        return;
    }
    
    try {
        const response = await fetch(`/admin/api/orders/${orderId}/send-reminder`, {
            method: 'POST',
        });
        
        const result = await response.json();
        
        if (response.ok) {
            alert(`Reminder email has been sent successfully to ${result.email || 'customer'}.`);
        } else {
            // Show more detailed error message
            alert(`Error sending reminder email: ${result.message || 'Unknown error'}. ${result.error || ''}`);
        }
    } catch (error) {
        console.error('Error sending reminder email:', error);
        alert('Error sending reminder email. Please try again or check server logs.');
    }
}

// Close the modal
function closeModal() {
    document.getElementById('order-details-modal').classList.add('hidden');
}

// Helper function to get payment method text
function getPaymentMethodText(method) {
    switch (method) {
        case 'card':
            return 'Credit/Debit Card';
        case 'cash':
            return 'Cash on Service';
        case 'bank_transfer':
            return 'Bank Transfer';
        default:
            return method;
    }
}

// Mark order as completed
async function markOrderAsCompleted(orderId) {
    if (!confirm('Are you sure you want to mark this order as completed?')) {
        return;
    }
    
    try {
        // Find the button that was clicked and show loading state
        const completeButton = Array.from(document.querySelectorAll('.complete-order'))
            .find(btn => btn.dataset.id === orderId);
            
        if (completeButton) {
            const originalHTML = completeButton.innerHTML;
            completeButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            completeButton.disabled = true;
        }
        
        const response = await fetch(`/admin/api/orders/${orderId}/complete`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0'
            }
        });
        
        // Parse response
        let responseData;
        try {
            responseData = await response.json();
        } catch (parseError) {
            console.error('Error parsing response:', parseError);
            responseData = { message: 'Could not parse server response' };
        }
        
        // Check if successful
        if (response.ok) {
            console.log('Order completion successful:', responseData);
            
            // Get the order details to check payment status
            const orderDetails = responseData.order || {};
            
            // If order is both completed and paid, remove it from the UI and data arrays
            if (orderDetails.paymentStatus === 'paid' || orderDetails.paymentStatus === 'cod_paid') {
                console.log("Order is now completed and paid, removing from view:", orderId);
                // Remove the order from the UI immediately
                removeOrderFromUI(orderId);
                
                // Also remove from our data arrays
                allOrders = allOrders.filter(order => order._id !== orderId);
                currentOrders = currentOrders.filter(order => order._id !== orderId);
                
                // Show success notification
                showToast('Order marked as completed and removed from the list.', 'success');
                
                // Re-display orders with the updated filtered list
                displayOrders();
            } else {
                // If order is completed but not paid, update its status in the UI
                const orderRow = document.getElementById(`order-row-${orderId}`);
                if (orderRow) {
                    const statusCell = orderRow.querySelector('td:nth-child(7)');
                    if (statusCell) {
                        // Update the status display
                        const statusSpans = statusCell.querySelectorAll('span');
                        if (statusSpans.length > 1) {
                            statusSpans[1].className = 'px-2 mt-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800';
                            statusSpans[1].textContent = 'completed';
                        }
                    }
                }
                
                // Show success notification
                showToast('Order marked as completed', 'success');
            }
        } else {
            // Detailed error message
            console.error('Error response from server:', responseData);
            
            // Show error notification
            showToast(`Failed to mark order as completed: ${responseData.message || 'Error updating order status. Please try again.'}`, 'error');
            
            // Reset button if it exists
            if (completeButton) {
                completeButton.innerHTML = '<i class="fas fa-check-circle"></i>';
                completeButton.disabled = false;
            }
        }
    } catch (error) {
        console.error('Error marking order as completed:', error);
        
        // Show error notification
        showToast(`Failed to mark order as completed: ${error.message || 'Unknown error'}. Please try again.`, 'error');
    }
}

// Helper function to remove an order from the UI
function removeOrderFromUI(orderId) {
    // Find and remove the order row
    const orderRow = Array.from(document.querySelectorAll('tr')).find(row => {
        const actionButtons = row.querySelectorAll('button');
        return Array.from(actionButtons).some(btn => btn.dataset.id === orderId);
    });
    
    if (orderRow) {
        // Find any detailed row that might follow it
        const nextRow = orderRow.nextElementSibling;
        if (nextRow && nextRow.classList.contains('payment-details')) {
            nextRow.remove();
        }
        
        // Fade out animation
        orderRow.style.transition = 'opacity 0.5s ease';
        orderRow.style.opacity = '0';
        
        // Remove after animation
        setTimeout(() => {
            orderRow.remove();
            
            // Update pagination info and table
            const remainingRows = document.querySelectorAll('#orders-table-body tr').length;
            if (remainingRows === 0) {
                document.getElementById('orders-table-body').innerHTML = `
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500" colspan="8">
                            No orders found needing attention.
                        </td>
                    </tr>
                `;
            }
            
            // Update pagination
            document.getElementById('pagination-info').textContent = 
                `Showing 1 to ${currentOrders.length} of ${currentOrders.length} results`;
            
        }, 500);
    }
}

// Modify the openDeleteOrderModal function to only accept orderId
function openDeleteOrderModal(orderId) {
    currentDeleteOrderId = orderId;
    deleteOrder();
}

// Update the deleteOrder function to use showToast
function deleteOrder() {
    if (!currentDeleteOrderId) return;
    
    // Find the order in the current orders
    const orderToDelete = currentOrders.find(order => order._id === currentDeleteOrderId);
    if (!orderToDelete) return;
    
    // Get the delete button element
    const deleteBtn = document.querySelector(`.delete-order[data-id="${currentDeleteOrderId}"]`);
    if (deleteBtn) {
        // Show loading state
        deleteBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        deleteBtn.disabled = true;
    }
    
    // Send request to archive the order
    fetch(`/admin/api/orders/${currentDeleteOrderId}`, {
        method: 'DELETE',
        headers: {
            'Content-Type': 'application/json'
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('Failed to archive order');
        }
        return response.json();
    })
    .then(data => {
        // Show success notification
        showToast('Order archived successfully', 'success');
        
        // Remove the order from the UI with fade effect
        const orderRow = document.getElementById(`order-row-${currentDeleteOrderId}`);
        if (orderRow) {
            orderRow.style.transition = "opacity 0.5s ease";
            orderRow.style.opacity = "0";
            
            setTimeout(() => {
                orderRow.remove();
                
                // Update the arrays
                allOrders = allOrders.filter(order => order._id !== currentDeleteOrderId);
                currentOrders = currentOrders.filter(order => order._id !== currentDeleteOrderId);
                
                // Update order count
                updateOrderCount();
                
                // Show "no orders" message if needed
                const orderTableBody = document.getElementById('orders-table-body');
                if (currentOrders.length === 0) {
                    orderTableBody.innerHTML = `
                        <tr>
                            <td colspan="8" class="px-6 py-4 text-center text-gray-500">
                                No orders match the current filters.
                            </td>
                        </tr>
                    `;
                }

                // Update pagination info
                const paginationInfo = document.getElementById('pagination-info');
                if (paginationInfo) {
                    paginationInfo.textContent = 
                        `Showing 1 to ${currentOrders.length} of ${currentOrders.length} results`;
                }
            }, 500);
        }
        
        // Reset the current delete order ID
        currentDeleteOrderId = null;
    })
    .catch(error => {
        console.error('Error archiving order:', error);
        showToast(`Failed to archive order: ${error.message}`, 'error');
        
        // Reset button state if it exists
        if (deleteBtn) {
            deleteBtn.innerHTML = '<i class="fas fa-trash-alt"></i>';
            deleteBtn.disabled = false;
        }
        
        // Reset the current delete order ID
        currentDeleteOrderId = null;
    });
}

// Helper function to update order count
function updateOrderCount() {
    // Update total orders count if present
    const totalOrdersElement = document.getElementById('totalOrders');
    if (totalOrdersElement) {
        const currentCount = parseInt(totalOrdersElement.textContent, 10);
        if (!isNaN(currentCount)) {
            totalOrdersElement.textContent = currentCount - 1;
        }
    }
    
    // Update display count
    const paginationInfoElement = document.getElementById('pagination-info');
    if (paginationInfoElement) {
        paginationInfoElement.textContent = `Showing 1 to ${currentOrders.length} of ${currentOrders.length} results`;
    }
}

// Toast notification function
function showToast(message, type = 'success') {
    const toast = document.createElement('div');
    toast.className = type === 'success' 
        ? 'bg-green-100 border-l-4 border-green-500 text-green-700 p-4 rounded shadow-md mb-2'
        : 'bg-red-100 border-l-4 border-red-500 text-red-700 p-4 rounded shadow-md mb-2';
    
    toast.innerHTML = `
        <div class="flex items-center">
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} mr-2"></i>
            <p>${message}</p>
        </div>
    `;
    
    const container = document.getElementById('toast-container');
    container.appendChild(toast);
    
    // Remove after 3 seconds
    setTimeout(() => {
        toast.style.opacity = '0';
        toast.style.transition = 'opacity 0.5s ease';
        setTimeout(() => toast.remove(), 500);
    }, 3000);
}
</script>

<%- include('../partials/footer') %> 