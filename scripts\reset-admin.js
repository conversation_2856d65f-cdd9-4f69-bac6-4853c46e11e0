const mongoose = require('mongoose');
const User = require('../models/User');
const bcrypt = require('bcryptjs');

// Connect to database
mongoose.connect('mongodb://localhost:27017/junkexpert')
  .then(async () => {
    console.log('Connected to MongoDB');
    
    try {
      // Find all admin users
      const admins = await User.find({ role: 'admin' });
      console.log('Found admin users:', admins.length);
      
      if (admins.length === 0) {
        console.log('No admin users found. Create one first using create-admin.js');
        return;
      }
      
      // Reset password for all admin users
      const salt = await bcrypt.genSalt(10);
      const hashedPassword = await bcrypt.hash('admin123', salt);
      
      for (const admin of admins) {
        admin.password = hashedPassword;
        await admin.save();
        console.log(`Reset password for admin: ${admin.name} (${admin.email})`);
      }
      
      console.log('\nPassword reset successful for all admin users');
      console.log('New password: admin123');
      
    } catch (error) {
      console.error('Error resetting admin passwords:', error);
    } finally {
      mongoose.disconnect();
      console.log('Disconnected from MongoDB');
    }
  })
  .catch(err => {
    console.error('Error connecting to MongoDB:', err);
  }); 