/**
 * AOS (Animate On Scroll) initialization
 * Configures animations to prevent repetition
 */

document.addEventListener('DOMContentLoaded', function() {
  // Initialize AOS with configuration to run animations only once
  AOS.init({
    once: true,          // Animation only happens once
    offset: 100,         // Offset (in px) from the original trigger point
    delay: 0,            // Values from 0 to 3000, with step 50ms
    duration: 800,       // Values from 0 to 3000, with step 50ms
    easing: 'ease-out',  // Default easing for AOS animations
    mirror: false,       // Whether elements should animate out while scrolling past them
    anchorPlacement: 'top-bottom' // Defines which position of the element regarding to window should trigger the animation
  });
  
  // Handle page transitions if needed
  window.addEventListener('load', function() {
    AOS.refresh();
  });
  
  // Refresh AOS on orientation changes and other events that might affect layout
  window.addEventListener('resize', function() {
    AOS.refresh();
  });
}); 