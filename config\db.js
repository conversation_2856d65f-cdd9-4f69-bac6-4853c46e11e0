const mongoose = require('mongoose');

// Global cache for MongoDB connection
let cachedDb = null;  
 
const connectDB = async () => { 
    // If connection exists, reuse it 
    if (cachedDb) {
        console.log('Using existing database connection');
        return cachedDb;
    }

    try {
        // Connection options optimized for serverless
        const opts = {
            useNewUrlParser: true,
            useUnifiedTopology: true,
            bufferCommands: false,
            serverSelectionTimeoutMS: 15000, // Increased timeout
            socketTimeoutMS: 55000, // Prevent timeout during long operations
            family: 4 // Force IPv4
        };

        // Connect to MongoDB
        const conn = await mongoose.connect(process.env.MONGO_URI, opts);
        
        console.log(`MongoDB Connected: ${conn.connection.host}`);
        
        // Cache the connection
        cachedDb = conn;
        
        return conn;
    } catch (error) {
        console.error(`Error connecting to MongoDB: ${error.message}`);
        // Don't exit process in serverless environment
        throw error;
    }
};

module.exports = connectDB;
