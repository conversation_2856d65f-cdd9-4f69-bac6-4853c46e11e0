<%- include('partials/header') %>

<!-- Booking Page Schema -->
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "WebPage",
    "name": "Book Junk Removal Service Online | Schedule Furniture Removal in UAE",
    "description": "Book professional junk removal service online. Easy scheduling, instant quotes, same-day service available. Furniture removal, appliance disposal in UAE.",
    "url": "https://www.junksexpert.com/booking",
    "breadcrumb": {
        "@type": "BreadcrumbList",
        "itemListElement": [
            {
                "@type": "ListItem",
                "position": 1,
                "name": "Home",
                "item": "https://www.junksexpert.com/"
            },
            {
                "@type": "ListItem",
                "position": 2,
                "name": "Book Service",
                "item": "https://www.junksexpert.com/booking"
            }
        ]
    },
    "mainEntity": {
        "@type": "Service",
        "name": "Online Junk Removal Booking",
        "description": "Easy online booking system for professional junk removal services",
        "provider": {
            "@type": "LocalBusiness",
            "name": "JunkExperts",
            "telephone": "+971569257614",
            "address": {
                "@type": "PostalAddress",
                "streetAddress": "22nd St, Al Quoz",
                "addressLocality": "Dubai",
                "addressCountry": "AE"
            }
        },
        "areaServed": "United Arab Emirates"
    }
}
</script>

<style>
    /* Responsive fixes */
    @media (max-width: 640px) {
        .service-item label {
            flex-wrap: wrap;
        }

        .service-item .text-right {
            margin-top: 8px;
            width: 100%;
            text-align: left;
        }
    }

    /* Payment method styling */
    .payment-method-input:checked + .payment-method-label {
        border-color: #f97316;
        background-color: #fff7ed;
        box-shadow: 0 0 0 2px #f97316;
    }

    /* Line-clamp utility */
    .line-clamp-2 {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    /* Service item styling */
    .service-item {
        transition: all 0.2s ease;
    }

    .service-item:hover {
        border-color: #f97316;
    }

    /* Order summary */
    #order-summary {
        display: none;
    }

    /* Email verification */
    #submit-warning {
        color: #e53e3e;
    }
</style>

    <!-- Booking Form Section -->
    <section class="pt-32 pb-16 bg-gray-50" style="background-image: url('/image8.jpg'); background-size: cover; background-position: center; background-attachment: fixed; background-blend-mode: overlay; background-color: rgba(249, 250, 251, 0.9);">
        <div class="container mx-auto px-4 sm:px-6">
            <div class="max-w-4xl mx-auto bg-white rounded-lg shadow-md overflow-hidden">
                <div class="flex flex-col md:flex-row">
                    <div class="w-full md:w-1/3 bg-orange-500 text-white p-8 py-12 relative" data-aos="fade-right">
                        <!-- Background image for sidebar -->
                        <div class="absolute inset-0 bg-gradient-to-b from-orange-600 to-orange-500 opacity-90 z-0"></div>
                        <div class="absolute inset-0 bg-cover bg-center opacity-20 z-0"></div>

                        <div class="relative z-10">
                            <h2 class="text-3xl font-bold mb-6">Book Your Service</h2>
                            <p class="mb-6">Schedule your junk removal service with our easy booking form.</p>

                            <div class="mb-8">
                                <h3 class="font-bold text-xl mb-3">Why Choose Us:</h3>
                                <ul class="space-y-2">
                                    <li class="flex items-center">
                                        <i class="fas fa-check-circle mr-2"></i>
                                        <span>Fast, reliable service</span>
                                    </li>
                                    <li class="flex items-center">
                                        <i class="fas fa-check-circle mr-2"></i>
                                        <span>Professional team</span>
                                    </li>
                                    <li class="flex items-center">
                                        <i class="fas fa-check-circle mr-2"></i>
                                        <span>Eco-friendly disposal</span>
                                    </li>
                                    <li class="flex items-center">
                                        <i class="fas fa-check-circle mr-2"></i>
                                        <span>100% satisfaction guarantee</span>
                                    </li>
                                </ul>
                            </div>

                            <div>
                                <h3 class="font-bold text-xl mb-3">Need Help?</h3>
                                <p class="mb-2">Call us for immediate assistance:</p>
                                <a href="mailto:<EMAIL>" class="flex items-center text-white hover:text-gray-200">
                                    <i class="fas fa-envelope mr-2"></i>
                                    <EMAIL>
                                </a>
                                <a href="tel:+971569257614" class="flex items-center text-white hover:text-gray-200">
                                    <i class="fas fa-phone mr-2"></i>
                                    +971 56 925 7614
                                </a>
                            </div>
                        </div>
                    </div>

                    <div class="w-full md:w-2/3 p-6 sm:p-8" data-aos="fade-left">
                        <h3 class="text-2xl font-bold mb-6">Your Information</h3>

                        <form action="/booking" method="POST">
                            <!-- Personal Information -->
                            <div class="mb-6">
                                <label class="block text-gray-700 mb-2" for="name">Name *</label>
                                <input type="text" id="name" name="name" class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500" required>
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                                <div>
                                    <label class="block text-gray-700 mb-2" for="email">Email *</label>
                                    <input type="email" id="email" name="email" class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500" required>
                                    <div class="mt-2">
                                        <p class="text-sm text-gray-700 mb-2">Verify your email:</p>
                                        <div class="space-y-2">
                                            <!-- Email Verification Code Option -->
                                            <div class="mb-2">
                                                <button type="button" id="send-code-btn" class="w-full px-3 py-2 border border-orange-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-orange-50 focus:outline-none">
                                                    <i class="fas fa-envelope mr-2"></i>
                                                    Send Verification Code
                                                </button>
                                            </div>

                                            <!-- Verification Code Input (hidden initially) -->
                                            <div id="verification-code-section" class="hidden">
                                                <!-- Code input -->
                                                <div class="mb-2">
                                                    <input type="text" id="verification-code" placeholder="Enter 6-digit code" class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500">
                                                </div>

                                                <!-- Action buttons -->
                                                <div class="grid grid-cols-2 gap-2 mb-2">
                                                    <button type="button" id="verify-code-btn" class="w-full px-3 py-2 bg-orange-500 text-white rounded-md shadow-sm text-sm font-medium hover:bg-orange-600 focus:outline-none">
                                                        Verify
                                                    </button>
                                                    <button type="button" id="cancel-verification-btn" class="w-full px-3 py-2 bg-gray-200 text-gray-700 rounded-md shadow-sm text-sm font-medium hover:bg-gray-300 focus:outline-none">
                                                        Cancel
                                                    </button>
                                                </div>

                                                <!-- Help text -->
                                                <p class="text-xs text-gray-500">
                                                    <i class="fas fa-info-circle mr-1"></i> Didn't receive the code? Please check that your email address is correct.
                                                </p>
                                            </div>

                                        </div>
                                    </div>
                                    <div id="email-validation-message" class="mt-1 text-sm hidden"></div>
                                </div>
                                <div>
                                    <label class="block text-gray-700 mb-2" for="phone">Phone Number *</label>
                                    <input type="tel" id="phone" name="phone" class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500" required>
                                </div>
                            </div>

                            <!-- Service Selection -->
                            <div class="mb-6">
                                <label class="block text-gray-700 mb-2" for="services">Services *</label>
                                <div class="grid grid-cols-1 gap-3" id="services-container">
                                    <% services.forEach(service => { %>
                                    <div class="border rounded-lg p-4 hover:shadow-md transition service-item">
                                        <div class="flex items-start">
                                            <div class="flex-shrink-0 mr-4">
                                                <input type="checkbox" id="service-<%= service._id %>" name="selectedServices" value="<%= service._id %>"
                                                    data-name="<%= service.name %>" data-price="<%= service.price %>" class="service-checkbox mt-1"
                                                    <%= selectedService && selectedService._id.toString() === service._id.toString() ? 'checked' : '' %>>
                                            </div>
                                            <div class="flex-grow min-w-0">
                                                <label for="service-<%= service._id %>" class="flex justify-between cursor-pointer flex-wrap">
                                                    <div class="flex items-start flex-grow pr-2">
                                                        <div class="w-12 h-12 sm:w-16 sm:h-16 rounded-lg overflow-hidden mr-3 flex-shrink-0">
                                                            <img src="<%= service.image %>" alt="<%= service.name %>" class="w-full h-full object-cover">
                                                        </div>
                                                        <div class="min-w-0 flex-grow">
                                                            <span class="font-medium block"><%= service.name %></span>
                                                            <p class="text-gray-600 text-sm mt-1 line-clamp-2"><%= service.description.substring(0, 100) %>...</p>
                                                        </div>
                                                    </div>
                                                    <div class="text-right flex-shrink-0 mt-2 sm:mt-0">
                                                        <span class="font-bold text-orange-500">AED <%= service.price.toFixed(2) %></span>
                                                    </div>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    <% }); %>
                                </div>
                                <!-- Hidden field for total amount -->
                                <input type="hidden" id="totalAmount" name="totalAmount" value="<%= selectedService ? selectedService.price : '' %>">
                                <!-- Hidden field for selected services data -->
                                <input type="hidden" id="selectedServicesData" name="selectedServicesData" value="">
                            </div>

                            <!-- Service Details -->
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                                <div>
                                    <label class="block text-gray-700 mb-2" for="date">Preferred Date & Time *</label>
                                    <div class="relative">
                                        <div class="absolute left-3 top-1/2 transform -translate-y-1/2 text-orange-500">
                                            <i class="fas fa-calendar-alt"></i>
                                        </div>
                                        <input type="datetime-local" id="date" name="date" class="w-full pl-10 px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500" required>
                                    </div>
                                </div>
                                <div>
                                    <label class="block text-gray-700 mb-2" for="address">Address *</label>
                                    <div class="relative">
                                        <div class="absolute left-3 top-1/2 transform -translate-y-1/2 text-orange-500">
                                            <i class="fas fa-map-marker-alt"></i>
                                        </div>
                                        <input type="text" id="address" name="address" class="w-full pl-10 px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500" required>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-6">
                                <label class="block text-gray-700 mb-2" for="message">Additional Details</label>
                                <div class="relative">
                                    <div class="absolute left-3 top-3 text-orange-500">
                                        <i class="fas fa-comment-alt"></i>
                                    </div>
                                    <textarea id="message" name="message" rows="3" class="w-full pl-10 px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500"></textarea>
                                </div>
                            </div>

                           <!-- Payment Method -->
<div class="mb-8">
    <h4 class="font-bold mb-3">Payment Method *</h4>
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 items-stretch">
        <div class="relative">
            <input type="radio" id="cash" name="paymentMethod" value="cash" class="absolute opacity-0 payment-method-input" checked>
            <label for="cash" class="block border rounded-lg p-3 sm:p-4 text-center cursor-pointer transition hover:border-orange-500 payment-method-label h-full flex flex-col items-center justify-center">
                <div class="mb-2 flex items-center justify-center">
                    <div class="h-10 w-10 bg-green-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-money-bill-wave text-green-600 text-xl"></i>
                    </div>
                </div>
                <span class="block font-medium">Cash On Service</span>
                <span class="text-sm text-gray-500 mt-1">Pay when we arrive</span>
            </label>
        </div>
        <div class="relative">
            <input type="radio" id="card" name="paymentMethod" value="card" class="absolute opacity-0 payment-method-input">
            <label for="card" class="block border rounded-lg p-3 sm:p-4 text-center cursor-pointer transition hover:border-orange-500 payment-method-label h-full flex flex-col items-center justify-center">
                <div class="mb-2 flex items-center justify-center">
                    <div class="h-10 w-10 bg-blue-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-credit-card text-blue-600 text-xl"></i>
                    </div>
                </div>
                <span class="block font-medium">Credit/Debit Card</span>
                <span class="text-sm text-gray-500 mt-1">Secure online payment</span>
            </label>
        </div>
    </div>
</div>


                            <!-- Order Summary -->
                            <div class="mb-8 p-4 bg-gray-50 rounded-lg border border-gray-200 shadow-sm" id="order-summary">
                                <h4 class="font-bold mb-3 text-lg">Order Summary</h4>
                                <div id="selected-services-summary" class="border-b pb-2 mb-2">
                                    <p class="text-sm text-gray-500 mb-2">Selected Services:</p>
                                    <div id="services-list" class="max-h-[300px] overflow-y-auto">
                                        <!-- Services will be listed here -->
                                        <div class="text-center text-gray-500 py-2">No services selected</div>
                                    </div>
                                </div>
                                <div class="flex justify-between font-bold text-lg mt-4">
                                    <span>Total Amount:</span>
                                    <span id="summary-total">AED 0.00</span>
                                </div>
                            </div>

                            <!-- Submit Button -->
                            <div>
                                <button type="submit" id="submit-booking" class="w-full bg-gradient-to-r from-orange-500 to-orange-600 text-white py-3 px-6 rounded-lg hover:from-orange-600 hover:to-orange-700 transition focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-opacity-50 shadow-lg">
                                    <i class="fas fa-calendar-check mr-2"></i> Complete Booking
                                </button>
                                <div id="submit-warning" class="mt-2 text-red-600 text-center hidden">
                                    Please verify your email address before submitting
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Service Guarantee -->
    <section class="py-16 bg-white">
        <div class="container mx-auto px-6">
            <div class="max-w-4xl mx-auto text-center" data-aos="fade-up">
                <h2 class="text-3xl font-bold mb-6">Our Service Guarantee</h2>
                <p class="text-lg text-gray-600 mb-12">
                    We're committed to providing exceptional service. If you're not satisfied, we'll make it right.
                </p>

                <div class="grid md:grid-cols-3 gap-8">
                    <div class="bg-white p-6 rounded-xl shadow-lg transform transition-all duration-300 hover:-translate-y-2">
                        <div class="w-16 h-16 rounded-full bg-orange-100 flex items-center justify-center text-orange-500 mx-auto mb-6">
                            <i class="fas fa-clock text-2xl"></i>
                        </div>
                        <div class="h-32 overflow-hidden mb-4 rounded-lg">
                            <img src="/image5.jpg" alt="On-time service" class="w-full h-full object-cover">
                        </div>
                        <h3 class="text-xl font-bold mb-2">On-Time Service</h3>
                        <p class="text-gray-600">We value your time and always arrive within the scheduled window.</p>
                    </div>
                    <div class="bg-white p-6 rounded-xl shadow-lg transform transition-all duration-300 hover:-translate-y-2">
                        <div class="w-16 h-16 rounded-full bg-orange-100 flex items-center justify-center text-orange-500 mx-auto mb-6">
                            <i class="fas fa-users text-2xl"></i>
                        </div>
                        <div class="h-32 overflow-hidden mb-4 rounded-lg">
                            <img src="/images/team.jpg" alt="Professional team" class="w-full h-full object-cover">
                        </div>
                        <h3 class="text-xl font-bold mb-2">Professional Team</h3>
                        <p class="text-gray-600">Our staff is trained, insured, and committed to excellent service.</p>
                    </div>
                    <div class="bg-white p-6 rounded-xl shadow-lg transform transition-all duration-300 hover:-translate-y-2">
                        <div class="w-16 h-16 rounded-full bg-orange-100 flex items-center justify-center text-orange-500 mx-auto mb-6">
                            <i class="fas fa-shield-alt text-2xl"></i>
                        </div>
                        <div class="h-32 overflow-hidden mb-4 rounded-lg">
                            <img src="/image7.jpg" alt="Satisfaction guaranteed" class="w-full h-full object-cover">
                        </div>
                        <h3 class="text-xl font-bold mb-2">Satisfaction Guaranteed</h3>
                        <p class="text-gray-600">If you're not happy with our service, we'll fix it at no extra cost.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Load Google Sign-In API -->
    <script src="https://accounts.google.com/gsi/client" async defer></script>

    <!-- JavaScript for Payment Method Selection -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // FORM VALIDATION
            // 1. Set minimum date for date picker to current date/time
            const dateInput = document.getElementById('date');
            const now = new Date();

            // Format date as YYYY-MM-DDThh:mm
            const year = now.getFullYear();
            const month = String(now.getMonth() + 1).padStart(2, '0');
            const day = String(now.getDate()).padStart(2, '0');
            const hours = String(now.getHours()).padStart(2, '0');
            const minutes = String(now.getMinutes()).padStart(2, '0');

            const minDateTime = `${year}-${month}-${day}T${hours}:${minutes}`;
            dateInput.min = minDateTime;

            // If date is empty, suggest a time slot 24 hours from now
            if (!dateInput.value) {
                const tomorrow = new Date(now);
                tomorrow.setDate(tomorrow.getDate() + 1);

                const tomorrowYear = tomorrow.getFullYear();
                const tomorrowMonth = String(tomorrow.getMonth() + 1).padStart(2, '0');
                const tomorrowDay = String(tomorrow.getDate()).padStart(2, '0');
                const tomorrowHours = String(tomorrow.getHours()).padStart(2, '0');
                const tomorrowMinutes = String(tomorrow.getMinutes()).padStart(2, '0');

                const suggestedDateTime = `${tomorrowYear}-${tomorrowMonth}-${tomorrowDay}T${tomorrowHours}:${tomorrowMinutes}`;
                dateInput.value = suggestedDateTime;
            }

            // Payment method selection
            const paymentInputs = document.querySelectorAll('.payment-method-input');

            // Function to update payment method styles
            function updatePaymentMethodStyles() {
                paymentInputs.forEach(input => {
                    const label = input.nextElementSibling;
                    if (input.checked) {
                        // Style is now handled via CSS
                    }
                });
            }

            // Add change event listeners to payment method radio buttons
            paymentInputs.forEach(input => {
                input.addEventListener('change', updatePaymentMethodStyles);
            });

            // Initialize the styles
            updatePaymentMethodStyles();

            // Email validation
            const emailInput = document.getElementById('email');
            const sendCodeBtn = document.getElementById('send-code-btn');
            const verifyCodeBtn = document.getElementById('verify-code-btn');
            const cancelVerificationBtn = document.getElementById('cancel-verification-btn');
            const verificationCodeSection = document.getElementById('verification-code-section');
            const verificationCodeInput = document.getElementById('verification-code');
            const emailValidationMessage = document.getElementById('email-validation-message');
            const submitButton = document.getElementById('submit-booking');
            const submitWarning = document.getElementById('submit-warning');
            const bookingForm = document.querySelector('form[action="/booking"]');

            // Disable submit button initially
            submitButton.disabled = true;

            // Check if email is already verified (from localStorage)
            const verifiedEmail = localStorage.getItem('verified_email');
            const currentEmail = emailInput.value.trim().toLowerCase();

            if (verifiedEmail && currentEmail && verifiedEmail === currentEmail) {
                // Email is already verified
                emailValidationMessage.classList.remove('hidden');
                emailValidationMessage.textContent = '✓ Email verified';
                emailValidationMessage.className = 'mt-1 text-sm text-green-600';
                emailInput.classList.add('border-green-500');
                submitButton.disabled = false;
            }

            // Check if email is already verified or needs verification
            emailInput.addEventListener('input', function() {
                const email = emailInput.value.trim();

                // Check if email is already verified
                const verifiedEmail = localStorage.getItem('verified_email');
                const currentEmail = email.toLowerCase();

                if (verifiedEmail && verifiedEmail === currentEmail) {
                    // Still using verified email
                    emailValidationMessage.classList.remove('hidden');
                    emailValidationMessage.textContent = '✓ Email verified';
                    emailValidationMessage.className = 'mt-1 text-sm text-green-600';
                    emailInput.classList.remove('border-red-500');
                    emailInput.classList.add('border-green-500');
                    submitButton.disabled = false;
                    updateSubmitButtonState(document.querySelectorAll('.service-checkbox:checked').length > 0);
                } else if (email) {
                    // Email changed, needs verification again
                    emailValidationMessage.classList.remove('hidden');
                    emailValidationMessage.textContent = 'Email needs verification';
                    emailValidationMessage.className = 'mt-1 text-sm text-yellow-600';
                    emailInput.classList.remove('border-green-500', 'border-red-500');
                    emailInput.classList.add('border-yellow-300');
                    submitButton.disabled = true;
                }
            });

            // Email code verification functionality
            sendCodeBtn.addEventListener('click', function() {
                const email = emailInput.value.trim();

                if (!email) {
                    emailValidationMessage.classList.remove('hidden');
                    emailValidationMessage.textContent = 'Please enter your email first';
                    emailValidationMessage.className = 'mt-1 text-sm text-red-600';
                    return;
                }

                if (!email.match(/^[^\s@]+@[^\s@]+\.[^\s@]+$/)) {
                    emailValidationMessage.classList.remove('hidden');
                    emailValidationMessage.textContent = 'Please enter a valid email address';
                    emailValidationMessage.className = 'mt-1 text-sm text-red-600';
                    return;
                }

                // Make email field read-only during verification
                emailInput.readOnly = true;

                // Show verification code section
                verificationCodeSection.classList.remove('hidden');

                // Update UI to show code is being sent
                sendCodeBtn.disabled = true;
                sendCodeBtn.textContent = 'Sending...';
                emailValidationMessage.classList.remove('hidden');
                emailValidationMessage.textContent = 'Sending verification code...';
                emailValidationMessage.className = 'mt-1 text-sm text-gray-600';

                // Send verification code through the API
                fetch('/auth/verify-email', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ email })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        emailValidationMessage.textContent = 'Verification code sent! Check your email';
                        emailValidationMessage.className = 'mt-1 text-sm text-green-600';
                    } else {
                        emailValidationMessage.textContent = data.message || 'Failed to send verification code';
                        emailValidationMessage.className = 'mt-1 text-sm text-red-600';
                    }

                    // Allow sending again in 60 seconds
                    setTimeout(() => {
                        sendCodeBtn.disabled = false;
                        sendCodeBtn.textContent = 'Resend Code';
                    }, 60000);
                })
                .catch(error => {
                    console.error('Error sending verification code:', error);
                    emailValidationMessage.textContent = 'Error sending verification code. Please try again.';
                    emailValidationMessage.className = 'mt-1 text-sm text-red-600';
                    sendCodeBtn.disabled = false;
                    sendCodeBtn.textContent = 'Send Verification Code';
                });
            });

            // Verify code
            verifyCodeBtn.addEventListener('click', function() {
                const email = emailInput.value.trim();
                const code = verificationCodeInput.value.trim();

                if (!code) {
                    emailValidationMessage.textContent = 'Please enter the verification code';
                    emailValidationMessage.className = 'mt-1 text-sm text-red-600';
                    return;
                }

                // Update UI during verification
                verifyCodeBtn.disabled = true;
                verifyCodeBtn.textContent = 'Verifying...';
                emailValidationMessage.textContent = 'Verifying code...';
                emailValidationMessage.className = 'mt-1 text-sm text-gray-600';

                // Verify code through the API
                fetch('/auth/verify-code', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ email, code })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.verified) {
                        // Success - store verified email and update UI
                        localStorage.setItem('verified_email', email);
                        emailValidationMessage.textContent = '✓ Email verified successfully';
                        emailValidationMessage.className = 'mt-1 text-sm text-green-600';
                        emailInput.classList.add('border-green-500');
                        verificationCodeSection.classList.add('hidden');

                        // Reset the send code button to original state
                        sendCodeBtn.disabled = false;
                        sendCodeBtn.textContent = 'Send Verification Code';

                        // Enable submit button
                        submitButton.disabled = false;
                        updateSubmitButtonState(document.querySelectorAll('.service-checkbox:checked').length > 0);
                    } else {
                        // Invalid code
                        emailValidationMessage.textContent = data.message || 'Invalid verification code. Try again.';
                        emailValidationMessage.className = 'mt-1 text-sm text-red-600';
                        verifyCodeBtn.disabled = false;
                        verifyCodeBtn.textContent = 'Verify';
                    }
                })
                .catch(error => {
                    console.error('Error verifying code:', error);
                    emailValidationMessage.textContent = 'Error verifying code. Please try again.';
                    emailValidationMessage.className = 'mt-1 text-sm text-red-600';
                    verifyCodeBtn.disabled = false;
                    verifyCodeBtn.textContent = 'Verify';
                });
            });

            // Cancel verification process
            cancelVerificationBtn.addEventListener('click', function() {
                // Reset verification UI
                verificationCodeSection.classList.add('hidden');
                verificationCodeInput.value = '';

                // Enable email editing
                emailInput.readOnly = false;
                emailInput.classList.remove('border-green-500', 'border-red-500', 'border-yellow-300');

                // Update message
                emailValidationMessage.classList.remove('hidden');
                emailValidationMessage.textContent = 'Verification cancelled. You can edit your email.';
                emailValidationMessage.className = 'mt-1 text-sm text-gray-600';

                // Reset send code button
                sendCodeBtn.disabled = false;
                sendCodeBtn.textContent = 'Send Verification Code';

                // Update submit button state
                submitButton.disabled = true;
                updateSubmitButtonState(document.querySelectorAll('.service-checkbox:checked').length > 0);
            });

            // Multiple service selection handling
            const serviceCheckboxes = document.querySelectorAll('.service-checkbox');
            const totalAmountInput = document.getElementById('totalAmount');
            const selectedServicesDataInput = document.getElementById('selectedServicesData');
            const summaryTotal = document.getElementById('summary-total');
            const servicesList = document.getElementById('services-list');

            // Function to update order summary
            function updateOrderSummary() {
                let totalAmount = 0;
                const selectedServices = [];

                // Clear the existing services list
                servicesList.innerHTML = '';

                serviceCheckboxes.forEach(checkbox => {
                    if (checkbox.checked) {
                        const serviceName = checkbox.dataset.name;
                        const servicePrice = parseFloat(checkbox.dataset.price);
                        const serviceId = checkbox.value;

                        // Get the service image URL from the img tag within the service item
                        const serviceImageEl = checkbox.closest('.service-item').querySelector('img');
                        const serviceImage = serviceImageEl ? serviceImageEl.src : '';

                        // Add to selected services array
                        selectedServices.push({
                            id: serviceId,
                            name: serviceName,
                            price: servicePrice,
                            image: serviceImage
                        });

                        totalAmount += servicePrice;

                        // Create service item in summary
                        const serviceItem = document.createElement('div');
                        serviceItem.className = 'flex justify-between items-center mb-2 pb-2 border-b border-gray-100';
                        serviceItem.innerHTML = `
                            <div class="flex items-center min-w-0 flex-grow pr-2">
                                <div class="w-8 h-8 rounded-md overflow-hidden mr-2 flex-shrink-0">
                                    <img src="${serviceImage}" alt="${serviceName}" class="w-full h-full object-cover">
                                </div>
                                <span class="text-gray-700 truncate">${serviceName}</span>
                            </div>
                            <div class="flex-shrink-0 whitespace-nowrap">
                                <span class="font-medium text-orange-500">AED ${servicePrice.toFixed(2)}</span>
                            </div>
                        `;
                        servicesList.appendChild(serviceItem);
                    }
                });

                // Update the summary
                if (selectedServices.length > 0) {
                    document.getElementById('order-summary').style.display = 'block';
                } else {
                    servicesList.innerHTML = '<div class="text-center text-gray-500 py-2">No services selected</div>';
                    document.getElementById('order-summary').style.display = 'none';
                }

                // Update total and hidden fields
                summaryTotal.textContent = `AED ${totalAmount.toFixed(2)}`;
                totalAmountInput.value = totalAmount;
                selectedServicesDataInput.value = JSON.stringify(selectedServices);

                // Enable/disable submit button based on service selection and email verification
                updateSubmitButtonState(selectedServices.length > 0);
            }

            // Function to update submit button state
            function updateSubmitButtonState(hasSelectedServices) {
                const verifiedEmail = localStorage.getItem('verified_email');
                const currentEmail = emailInput.value.trim().toLowerCase();
                const isEmailVerified = verifiedEmail && currentEmail && verifiedEmail === currentEmail;

                if (hasSelectedServices && isEmailVerified) {
                    submitButton.disabled = false;
                    submitWarning.classList.add('hidden');
                } else if (!hasSelectedServices) {
                    submitButton.disabled = true;
                    submitWarning.textContent = 'Please select at least one service';
                    submitWarning.classList.remove('hidden');
                } else if (!isEmailVerified) {
                    submitButton.disabled = true;
                    submitWarning.textContent = 'Please verify your email address';
                    submitWarning.classList.remove('hidden');
                }
            }

            // Add event listeners to service checkboxes
            serviceCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', updateOrderSummary);

                // Add hover effect to parent container
                const serviceItem = checkbox.closest('.service-item');
                if (serviceItem) {
                    serviceItem.addEventListener('mouseenter', () => {
                        serviceItem.classList.add('bg-orange-50');
                    });
                    serviceItem.addEventListener('mouseleave', () => {
                        serviceItem.classList.remove('bg-orange-50');
                    });
                }
            });

            // Initialize order summary
            updateOrderSummary();

            // Form validation before submission
            function validateForm() {
                const name = document.getElementById('name').value.trim();
                const phone = document.getElementById('phone').value.trim();
                const date = document.getElementById('date').value;
                const address = document.getElementById('address').value.trim();
                const selectedServices = document.querySelectorAll('.service-checkbox:checked');

                if (!name) {
                    showFormError('Please enter your name');
                    return false;
                }

                if (!phone) {
                    showFormError('Please enter your phone number');
                    return false;
                }

                if (!date) {
                    showFormError('Please select a date and time');
                    return false;
                }

                // Validate that date is not in the past
                const selectedDate = new Date(date);
                const currentDate = new Date();
                if (selectedDate < currentDate) {
                    showFormError('Please select a future date and time');
                    return false;
                }

                if (!address) {
                    showFormError('Please enter your address');
                    return false;
                }

                // Check if any service is selected
                if (selectedServices.length === 0) {
                    showFormError('Please select at least one service');
                    return false;
                }

                // Verify email
                const verifiedEmail = localStorage.getItem('verified_email');
                const currentEmail = emailInput.value.trim().toLowerCase();

                if (!verifiedEmail || verifiedEmail !== currentEmail) {
                    showFormError('Please verify your email address');
                    return false;
                }

                return true;
            }

            function showFormError(message) {
                submitWarning.textContent = message;
                submitWarning.classList.remove('hidden');
                submitButton.disabled = false;
                submitButton.innerHTML = 'Complete Booking';
            }

            // Modify form submission to include selected services
            bookingForm.addEventListener('submit', function(event) {
                event.preventDefault();

                // Validate form
                if (!validateForm()) {
                    return;
                }

                // Show loading state
                submitButton.disabled = true;
                submitButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i> Processing...';
                submitWarning.classList.add('hidden');

                // Get form data
                const formData = new FormData(bookingForm);
                const formObject = {};
                formData.forEach((value, key) => {
                    // Handle multiple selected services
                    if(key === 'selectedServices') {
                        if(!formObject[key]) {
                            formObject[key] = [];
                        }
                        formObject[key].push(value);
                    } else {
                        formObject[key] = value;
                    }
                });

                // Get payment method
                const paymentMethod = document.querySelector('input[name="paymentMethod"]:checked').value;
                console.log("Selected payment method:", paymentMethod);

                // Submit form via AJAX
                fetch('/booking', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify(formObject)
                })
                .then(response => {
                    if (!response.ok) {
                        return response.json().then(data => {
                            throw new Error(data.message || 'An error occurred during submission');
                        });
                    }
                    return response.json();
                })
                .then(data => {
                    console.log("Response data:", data);
                    if (data.success) {
                        // For credit card payments, redirect to payment page if provided
                        if (paymentMethod === 'card' && data.redirect) {
                            console.log("Redirecting to payment page:", data.redirect);
                            window.location.href = data.redirect;
                        }
                        // For other payment methods or if no specific redirect
                        else if (data.redirect) {
                            console.log("Redirecting to:", data.redirect);
                            window.location.href = data.redirect;
                        } else {
                            // Fallback to order confirmation page
                            console.log("Redirecting to order confirmation page");
                            window.location.href = `/order-confirmation/${data.order._id}`;
                        }
                    } else {
                        // Show error message
                        throw new Error(data.message || 'Something went wrong. Please try again.');
                    }
                })
                .catch(error => {
                    console.error('Error submitting form:', error);
                    showFormError(error.message || 'Error submitting form. Please try again.');

                    // Reset submit button
                    submitButton.disabled = false;
                    submitButton.innerHTML = '<i class="fas fa-calendar-check mr-2"></i> Complete Booking';
                });
            });
        });
    </script>

<%- include('partials/footer') %>