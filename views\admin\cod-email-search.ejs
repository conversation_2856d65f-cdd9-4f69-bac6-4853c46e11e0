<%- include('../partials/header') %>

<div class="pt-24 pb-16 bg-gray-50">
    <div class="container mx-auto px-4">
        <!-- Page Header -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-4">
                <div>
                    <h1 class="text-2xl font-bold text-gray-800">Cash on Delivery Payment Management</h1>
                    <p class="text-gray-600">Search customers by email to manage cash on delivery payments</p>
                </div>
                <div class="mt-4 md:mt-0">
                <a href="/admin/dashboard" class="text-blue-600 hover:text-blue-800">
                    <i class="fas fa-arrow-left mr-1"></i> Back to Dashboard
                </a>
            </div>
            </div>
        </div>

        <!-- Email Search Section -->
        <div class="bg-white rounded-lg shadow-md overflow-hidden mb-6">
            <div class="bg-gray-50 border-b border-gray-200 px-6 py-4">
                <h2 class="text-lg font-bold text-gray-800">Search Customer by Email</h2>
            </div>
            <div class="p-6">
                <form id="emailSearchForm" class="flex flex-col md:flex-row gap-4 items-end">
                    <div class="flex-1">
                        <label for="customerEmail" class="block text-sm font-medium text-gray-700 mb-1">Customer Email</label>
                        <input type="email" id="customerEmail" class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-orange-500 focus:border-orange-500" placeholder="<EMAIL>" required>
                    </div>
                    <div class="flex gap-2">
                        <button type="submit" class="bg-orange-500 hover:bg-orange-600 text-white py-2 px-4 rounded-md transition flex items-center">
                            <i class="fas fa-search mr-2"></i> Search Orders
                        </button>
                        <button type="button" id="clearResults" class="bg-gray-200 hover:bg-gray-300 text-gray-700 py-2 px-4 rounded-md transition flex items-center">
                            <i class="fas fa-times mr-2"></i> Clear
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Search Results Section (initially hidden) -->
        <div id="searchResultsSection" class="bg-white rounded-lg shadow-md overflow-hidden mb-6 hidden">
            <div class="bg-gray-50 border-b border-gray-200 px-6 py-4 flex justify-between items-center">
                <h2 class="text-lg font-bold text-gray-800">Search Results</h2>
                <span id="resultCount" class="bg-orange-500 text-white px-3 py-1 rounded-full text-sm">0 Orders</span>
            </div>
            <div class="p-6">
                <!-- No results message -->
                <div id="noResultsMessage" class="bg-blue-50 text-blue-700 p-4 rounded-md mb-4 hidden">
                    <div class="flex">
                        <i class="fas fa-info-circle text-xl mr-3 mt-0.5"></i>
                        <p>No cash on delivery orders found for this email address.</p>
                    </div>
                </div>

                <!-- Search results table -->
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200" id="searchResultsTable">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Order ID</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Service</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <!-- Search results will be populated here -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

<!-- Success Alert -->
<div id="successAlert" class="hidden fixed top-16 right-4 bg-green-100 border-l-4 border-green-500 text-green-700 p-4 rounded shadow-md z-50">
    <div class="flex items-center">
        <i class="fas fa-check-circle mr-2 text-green-500"></i>
        <span>Payment updated successfully!</span>
    </div>
</div>

<!-- Error Alert -->
<div id="errorAlert" class="hidden fixed top-16 right-4 bg-red-100 border-l-4 border-red-500 text-red-700 p-4 rounded shadow-md z-50">
    <div class="flex items-center">
        <i class="fas fa-exclamation-circle mr-2 text-red-500"></i>
        <span id="errorMessage">An error occurred</span>
    </div>
</div>

<!-- Toast container for notifications -->
<div id="toast-container" class="position-fixed top-0 end-0 p-3" style="z-index: 1080;"></div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const successAlert = document.getElementById('successAlert');
    const errorAlert = document.getElementById('errorAlert');

    // Helper function to update the payment status in the UI
    function updateRowWithNewPaymentStatus(orderRow, paymentStatus) {
        if (!orderRow) return;
        
        // Update status cell
        const statusCell = orderRow.querySelector('td:nth-child(6)');
        if (statusCell) {
            const statusClass = paymentStatus === 'cod_paid' || paymentStatus === 'paid' ? 
                'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800';
                
            const statusText = paymentStatus === 'cod_paid' || paymentStatus === 'paid' ? 
                'Paid (COD)' : 'Pending (COD)';
                
            statusCell.innerHTML = `
                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${statusClass}">${statusText}</span>
            `;
        }
        
        // Update action buttons - hide "Update" button for paid orders
        if (paymentStatus === 'cod_paid' || paymentStatus === 'paid') {
            const actionCell = orderRow.querySelector('td:nth-child(7)');
            if (actionCell) {
                const updateBtn = actionCell.querySelector('.update-payment-btn');
                if (updateBtn) {
                    updateBtn.style.display = 'none';
                    actionCell.innerHTML = `
                        <span class="text-green-500"><i class="fas fa-check-circle mr-1"></i> Payment Completed</span>
                    `;
                }
            }
        }
    }

    function showSuccess(message) {
        successAlert.querySelector('span').textContent = message || 'Payment updated successfully!';
        successAlert.classList.remove('hidden');
        
        setTimeout(() => {
            successAlert.classList.add('hidden');
        }, 3000);
    }
    
    function showError(message) {
        document.getElementById('errorMessage').textContent = message || 'An error occurred';
        errorAlert.classList.remove('hidden');
        
        setTimeout(() => {
            errorAlert.classList.add('hidden');
        }, 3000);
    }

    function showToast(message, type = 'success') {
        const toastContainer = document.getElementById('toast-container');
        
        // Create toast element
        const toast = document.createElement('div');
        toast.className = type === 'success' 
            ? 'bg-green-100 border-l-4 border-green-500 text-green-700 p-4 rounded shadow-md mb-2'
            : 'bg-red-100 border-l-4 border-red-500 text-red-700 p-4 rounded shadow-md mb-2';
        
        toast.innerHTML = `
            <div class="flex items-center">
                <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} mr-2"></i>
                <p>${message}</p>
            </div>
        `;
        
        const container = document.getElementById('toast-container');
        container.appendChild(toast);
        
        // Remove after 3 seconds
        setTimeout(() => {
            toast.style.opacity = '0';
            toast.style.transition = 'opacity 0.5s ease';
            setTimeout(() => toast.remove(), 500);
        }, 3000);
    }

    // Handle email search form submission
    document.getElementById('emailSearchForm').addEventListener('submit', function(e) {
        e.preventDefault();
        const email = document.getElementById('customerEmail').value.trim();
        
        if (!email) {
            showError('Please enter a customer email');
            return;
        }
        
        // Show loading indicator
        document.getElementById('searchResultsSection').classList.remove('hidden');
        const tbody = document.querySelector('#searchResultsTable tbody');
        tbody.innerHTML = `
            <tr>
                <td colspan="7" class="px-6 py-4 text-center">
                    <div class="flex justify-center items-center">
                        <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-orange-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <span>Searching for orders...</span>
                    </div>
                </td>
            </tr>
        `;
        
        // Clear any previous results
        document.getElementById('noResultsMessage').classList.add('hidden');
        
        // Add timestamp to prevent caching
        const timestamp = new Date().getTime();
        
        // Fetch order data for the email - directly fetch ALL orders
        fetch(`/admin/api/customer-orders?email=${encodeURIComponent(email)}&t=${timestamp}`, {
            headers: {
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0'
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            console.log('Fetched data:', data);
            
            // Update result count
            const allOrders = data.orders || [];
            
            // Filter to only include cash/COD orders - be more flexible with payment method matching
            // AND exclude orders that are both completed and paid
            const cashOrders = allOrders.filter(order => {
                // First check if it's a cash/COD order
                const isCashOrder = order.paymentMethod === 'cash' || 
                       order.paymentMethod === 'cod' || 
                       order.paymentMethod === 'Cash on Delivery' || 
                       order.paymentStatus === 'cod_pending' || 
                       order.paymentStatus === 'cod_paid';
                
                // Then exclude completed and paid orders
                const isCompletedAndPaid = (order.status === 'completed' && 
                                          (order.paymentStatus === 'paid' || order.paymentStatus === 'cod_paid'));
                
                // Return orders that are cash orders but NOT completed and paid
                return isCashOrder && !isCompletedAndPaid;
            });
            
            document.getElementById('resultCount').textContent = `${cashOrders.length} Orders`;
            
            // Clear search results table
            tbody.innerHTML = '';
            
            // Show customer info if there are orders
            if (cashOrders && cashOrders.length > 0) {
                // Populate search results table with cash orders
                cashOrders.forEach(order => {
                    console.log('Processing order:', order);
                    const statusClass = getStatusClass(order.paymentStatus);
                    const statusText = getStatusText(order.paymentStatus);
                    
                    const row = document.createElement('tr');
                    row.id = `order-row-${order._id}`;
                    row.innerHTML = `
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${order._id}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            ${order.customerName || order.customer?.name || 'N/A'}<br>
                            <span class="text-xs text-gray-400">${order.phone || order.customer?.phone || 'N/A'}</span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${order.service || order.serviceName || 'N/A'}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">AED ${((order.totalAmount || order.amount || 0).toFixed(2))}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${formatDate(order.date || order.scheduledDate || order.createdAt)}</td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${statusClass}">${statusText}</span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <button class="update-payment-btn bg-orange-500 hover:bg-orange-600 text-white py-1 px-3 rounded-md text-sm" 
                                    data-order-id="${order._id}">
                                <i class="fas fa-edit mr-1"></i> Update
                            </button>
                            <button class="delete-order-btn bg-red-500 hover:bg-red-600 text-white py-1 px-3 rounded-md text-sm ml-2" 
                                    data-order-id="${order._id}">
                                <i class="fas fa-trash-alt mr-1"></i> Delete
                            </button>
                        </td>
                    `;
                    tbody.appendChild(row);
                    
                    // Create edit row (initially hidden)
                    const editRow = document.createElement('tr');
                    editRow.id = `edit-row-${order._id}`;
                    editRow.className = 'bg-gray-50 hidden';
                    editRow.innerHTML = `
                        <td colspan="7" class="px-6 py-4">
                            <form class="payment-update-form grid grid-cols-1 md:grid-cols-3 gap-4" data-order-id="${order._id}">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Payment Status</label>
                                    <select name="paymentStatus" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-orange-500 focus:border-orange-500">
                                        <option value="cod_pending" ${order.paymentStatus === 'cod_pending' ? 'selected' : ''}>Pending</option>
                                        <option value="cod_paid" ${order.paymentStatus === 'cod_paid' ? 'selected' : ''}>Paid</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Payment Date</label>
                                    <input type="date" name="paymentDate" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-orange-500 focus:border-orange-500" value="${new Date().toISOString().split('T')[0]}">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Payment Notes</label>
                                    <input type="text" name="paymentNotes" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-orange-500 focus:border-orange-500" placeholder="Receipt number, etc.">
                                </div>
                                <div class="md:col-span-3 flex justify-end space-x-2 mt-2">
                                    <button type="button" class="cancel-edit-btn px-3 py-1 bg-gray-200 hover:bg-gray-300 text-gray-800 rounded transition-colors">
                                        Cancel
                                    </button>
                                    <button type="submit" class="save-payment-btn px-3 py-1 bg-green-500 hover:bg-green-600 text-white rounded transition-colors flex items-center">
                                        <span>Save Payment</span>
                                        <svg class="save-spinner hidden ml-1 animate-spin h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                        </svg>
                                    </button>
                                </div>
                            </form>
                        </td>
                    `;
                    tbody.appendChild(editRow);
                });
            } else {
                // No cash orders found
                document.getElementById('noResultsMessage').classList.remove('hidden');
                tbody.innerHTML = `
                    <tr>
                        <td colspan="7" class="px-6 py-4 text-center text-gray-500">
                            No cash on delivery orders found for this email.
                        </td>
                    </tr>
                `;
            }
        })
        .catch(error => {
            console.error('Error fetching customer orders:', error);
            tbody.innerHTML = `
                <tr>
                    <td colspan="7" class="px-6 py-4 text-center text-red-500">
                        Error fetching orders. Please try again.
                    </td>
                </tr>
            `;
        });
    });
    
    // Handle update payment button click (toggling edit row)
    document.addEventListener('click', function(e) {
        if (e.target.closest('.update-payment-btn')) {
            const btn = e.target.closest('.update-payment-btn');
            const orderId = btn.dataset.orderId;
            
            // Show edit row
            const editRow = document.getElementById(`edit-row-${orderId}`);
            if (editRow) {
                editRow.classList.toggle('hidden');
            }
        }
        
        if (e.target.closest('.cancel-edit-btn')) {
            const cancelBtn = e.target.closest('.cancel-edit-btn');
            const editRow = cancelBtn.closest('tr');
            if (editRow) {
                editRow.classList.add('hidden');
            }
        }
        
        // Handle delete button click
        if (e.target && e.target.closest('.delete-order-btn')) {
            const btn = e.target.closest('.delete-order-btn');
            const orderId = btn.getAttribute('data-order-id');
            
            // Skip confirmation and directly delete
            deleteOrder(orderId, btn);
        }
    });
    
    // Handle delete order
    function deleteOrder(orderId, btn) {
        // Show loading state
        btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        btn.disabled = true;
        
        // Send request to archive the order
        fetch(`/admin/api/orders/${orderId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Failed to archive order');
            }
            return response.json();
        })
        .then(data => {
            // Show success message
            showToast('Order archived successfully', 'success');
            
            // Find the order row
            const orderRow = btn.closest('tr');
            
            // Remove the order from the DOM with a fade effect
            orderRow.style.transition = "opacity 0.5s ease";
            orderRow.style.opacity = "0";
            
            setTimeout(() => {
                // Also remove the edit row if it exists
                const editRow = document.getElementById(`edit-row-${orderId}`);
                if (editRow) {
                    editRow.remove();
                }
                
                // Remove the order row
                orderRow.remove();
                
                // Update the result count
                const resultCount = document.getElementById('resultCount');
                if (resultCount) {
                    const currentCountText = resultCount.textContent;
                    const currentCount = parseInt(currentCountText.match(/\d+/)[0]) - 1;
                    resultCount.textContent = `${currentCount} Orders`;
                }
                
                // If no orders left, show no results message
                const remainingRows = document.querySelectorAll('#searchResultsTable tbody tr:not([id^="edit-row"])').length;
                if (remainingRows === 0) {
                    document.getElementById('noResultsMessage').classList.remove('hidden');
                    document.querySelector('#searchResultsTable tbody').innerHTML = `
                        <tr>
                            <td colspan="7" class="px-6 py-4 text-center text-gray-500">
                                No cash on delivery orders found for this email.
                            </td>
                        </tr>
                    `;
                }
            }, 500);
        })
        .catch(error => {
            console.error('Error archiving order:', error);
            
            // Show error message
            showToast(`Failed to archive order: ${error.message}`, 'error');
            
            // Reset the delete button
            btn.innerHTML = '<i class="fas fa-trash-alt mr-1"></i> Delete';
            btn.disabled = false;
        });
    }
    
    // Handle payment form submission
    document.addEventListener('submit', function(e) {
        const form = e.target.closest('.payment-update-form');
        if (!form) return;
        
        e.preventDefault();
        
        const orderId = form.dataset.orderId;
        const paymentStatus = form.querySelector('[name="paymentStatus"]').value;
        const paymentDate = form.querySelector('[name="paymentDate"]').value;
        const paymentNotes = form.querySelector('[name="paymentNotes"]').value;
        
        // Show loading state
        const saveBtn = form.querySelector('.save-payment-btn');
        const spinner = form.querySelector('.save-spinner');
        
        saveBtn.disabled = true;
        saveBtn.querySelector('span').textContent = 'Saving...';
        spinner.classList.remove('hidden');
        
        // Create data to send
        const data = {
            paymentStatus,
            paymentDate,
            notes: paymentNotes
        };
        
        // Send update request
        fetch(`/admin/api/order/${orderId}/payment`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(result => {
            if (result.success) {
                // Hide edit row
                form.closest('tr').classList.add('hidden');
                
                // Show success message
                showToast('Payment updated successfully', 'success');
                
                // Get the order details
                const orderRow = document.getElementById(`order-row-${orderId}`);
                const paymentStatusAfterUpdate = paymentStatus;
                
                // Check if order is both completed and paid, then remove it from display
                if (paymentStatusAfterUpdate === 'cod_paid' || paymentStatusAfterUpdate === 'paid') {
                    // Check if the order status is completed - need to fetch the current status
                    fetch(`/admin/api/orders/${orderId}`)
                        .then(response => response.json())
                        .then(orderData => {
                            if (orderData.status === 'completed' || orderData.orderStatus === 'completed') {
                                console.log("Order is completed and paid, removing from display:", orderId);
                                
                                // Remove both rows from the UI with animation
                                if (orderRow) {
                                    orderRow.style.transition = 'opacity 0.5s ease';
                                    orderRow.style.opacity = '0';
                                    
                                    // Remove after animation
                                    setTimeout(() => {
                                        orderRow.remove();
                                        // Remove related edit row if exists
                                        const editRow = document.getElementById(`edit-row-${orderId}`);
                                        if (editRow) editRow.remove();
                                        
                                        // Update any counters
                                        const tableRows = document.querySelectorAll('#searchResultsTable tbody tr:not(.hidden)');
                                        const resultCount = document.getElementById('resultCount');
                                        resultCount.textContent = `${Math.floor(tableRows.length/2)} Orders`;
                                        
                                        // Show "no results" message if there are no orders left
                                        if (tableRows.length === 0) {
                                            document.getElementById('noResultsMessage').classList.remove('hidden');
                                            document.querySelector('#searchResultsTable tbody').innerHTML = `
                                                <tr>
                                                    <td colspan="7" class="px-6 py-4 text-center text-gray-500">
                                                        No cash on delivery orders found for this email.
                                                    </td>
                                                </tr>
                                            `;
                                        }
                                    }, 500);
                                }
                                
                                return; // Exit early as we're removing the row
                            }
                            
                            // If we get here, the order is paid but not completed yet - update the UI
                            updateRowWithNewPaymentStatus(orderRow, paymentStatusAfterUpdate);
                        })
                        .catch(error => {
                            console.error('Error checking order status:', error);
                            // Fall back to just updating the payment status in the UI
                            updateRowWithNewPaymentStatus(orderRow, paymentStatusAfterUpdate);
                        });
                } else {
                    // Just update the UI with new payment status
                    updateRowWithNewPaymentStatus(orderRow, paymentStatusAfterUpdate);
                }
                
                // No need to do a full refresh anymore since we handle UI updates properly
            } else {
                throw new Error(result.error || 'Failed to update payment');
            }
        })
        .catch(error => {
            console.error('Error updating payment:', error);
            showToast(error.message || 'Failed to update payment', 'error');
            
            // Reset button state
            saveBtn.disabled = false;
            saveBtn.querySelector('span').textContent = 'Save Payment';
            spinner.classList.add('hidden');
        });
    });
    
    // Function to update search results with fresh data
    function updateSearchResults(data) {
        const tbody = document.querySelector('#searchResultsTable tbody');
        
        // Filter the orders to exclude completed and paid ones
        const filteredOrders = data.orders.filter(order => {
            // Exclude orders that are both completed and paid
            const isCompletedAndPaid = (order.status === 'completed' && 
                                       (order.paymentStatus === 'paid' || order.paymentStatus === 'cod_paid'));
            
            // Return orders that are NOT completed and paid
            return !isCompletedAndPaid;
        });
        
        // Update result count
        document.getElementById('resultCount').textContent = `${filteredOrders.length} Orders`;
        
        // Clear search results table
        tbody.innerHTML = '';
        
        // Show customer info if there are orders
        if (filteredOrders && filteredOrders.length > 0) {
            // Populate search results table with cash orders
            const cashOrders = filteredOrders.filter(order => 
                order.paymentMethod === 'cash' || 
                order.paymentMethod === 'cod' || 
                order.paymentMethod === 'Cash on Delivery' || 
                order.paymentStatus === 'cod_pending' || 
                order.paymentStatus === 'cod_paid'
            );
            
            if (cashOrders.length > 0) {
                cashOrders.forEach(order => {
                    const statusClass = getStatusClass(order.paymentStatus);
                    const statusText = getStatusText(order.paymentStatus);
                    
                    const row = document.createElement('tr');
                    row.id = `order-row-${order._id}`;
                    row.innerHTML = `
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${order._id}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            ${order.customerName || order.customer?.name || 'N/A'}<br>
                            <span class="text-xs text-gray-400">${order.phone || order.customer?.phone || 'N/A'}</span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${order.service || order.serviceName || 'N/A'}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">AED ${((order.totalAmount || order.amount || 0).toFixed(2))}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${formatDate(order.date || order.scheduledDate || order.createdAt)}</td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${statusClass}">${statusText}</span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <button class="update-payment-btn bg-orange-500 hover:bg-orange-600 text-white py-1 px-3 rounded-md text-sm" 
                                    data-order-id="${order._id}">
                                <i class="fas fa-edit mr-1"></i> Update
                            </button>
                            <button class="delete-order-btn bg-red-500 hover:bg-red-600 text-white py-1 px-3 rounded-md text-sm ml-2" 
                                    data-order-id="${order._id}">
                                <i class="fas fa-trash-alt mr-1"></i> Delete
                            </button>
                        </td>
                    `;
                    tbody.appendChild(row);
                    
                    // Create edit row (initially hidden)
                    const editRow = document.createElement('tr');
                    editRow.id = `edit-row-${order._id}`;
                    editRow.className = 'bg-gray-50 hidden';
                    editRow.innerHTML = `
                        <td colspan="7" class="px-6 py-4">
                            <form class="payment-update-form grid grid-cols-1 md:grid-cols-3 gap-4" data-order-id="${order._id}">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Payment Status</label>
                                    <select name="paymentStatus" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-orange-500 focus:border-orange-500">
                                        <option value="cod_pending" ${order.paymentStatus === 'cod_pending' ? 'selected' : ''}>Pending</option>
                                        <option value="cod_paid" ${order.paymentStatus === 'cod_paid' ? 'selected' : ''}>Paid</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Payment Date</label>
                                    <input type="date" name="paymentDate" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-orange-500 focus:border-orange-500" value="${new Date().toISOString().split('T')[0]}">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Payment Notes</label>
                                    <input type="text" name="paymentNotes" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-orange-500 focus:border-orange-500" placeholder="Receipt number, etc.">
                                </div>
                                <div class="md:col-span-3 flex justify-end space-x-2 mt-2">
                                    <button type="button" class="cancel-edit-btn px-3 py-1 bg-gray-200 hover:bg-gray-300 text-gray-800 rounded transition-colors">
                                        Cancel
                                    </button>
                                    <button type="submit" class="save-payment-btn px-3 py-1 bg-green-500 hover:bg-green-600 text-white rounded transition-colors flex items-center">
                                        <span>Save Payment</span>
                                        <svg class="save-spinner hidden ml-1 animate-spin h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                        </svg>
                                    </button>
                                </div>
                            </form>
                        </td>
                    `;
                    tbody.appendChild(editRow);
                });
            } else {
                // No cash orders found
                document.getElementById('noResultsMessage').classList.remove('hidden');
                tbody.innerHTML = `
                    <tr>
                        <td colspan="7" class="px-6 py-4 text-center text-gray-500">
                            No cash on delivery orders found.
                        </td>
                    </tr>
                `;
            }
        } else {
            // No orders found
            document.getElementById('noResultsMessage').classList.remove('hidden');
            tbody.innerHTML = `
                <tr>
                    <td colspan="7" class="px-6 py-4 text-center text-gray-500">
                        No orders found for this email.
                    </td>
                </tr>
            `;
        }
    }
    
    // Handle clear results button
    document.getElementById('clearResults').addEventListener('click', function() {
        // Clear form
        document.getElementById('emailSearchForm').reset();
        
        // Hide results section
        document.getElementById('searchResultsSection').classList.add('hidden');
        document.getElementById('noResultsMessage').classList.add('hidden');
    });
    
    // Helper function to format dates
    function formatDate(dateString) {
        if (!dateString) return 'N/A';
        const date = new Date(dateString);
        return date.toLocaleDateString();
    }
    
    // Helper function to get status CSS class
    function getStatusClass(status) {
        switch(status) {
            case 'cod_paid':
            case 'paid':
                return 'bg-green-100 text-green-800';
            case 'cod_pending':
            case 'pending':
                return 'bg-yellow-100 text-yellow-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    }
    
    // Helper function to get status display text
    function getStatusText(status) {
        switch(status) {
            case 'cod_paid':
                return 'Paid (COD)';
            case 'paid':
                return 'Paid';
            case 'cod_pending':
                return 'Pending (COD)';
            case 'pending':
                return 'Pending';
            default:
                return status || 'Unknown';
        }
    }
});
</script>

<%- include('../partials/footer') %> 