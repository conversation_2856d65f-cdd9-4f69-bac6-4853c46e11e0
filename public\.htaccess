# JunkExperts - <PERSON>O Redirect Configuration
# This file fixes the "Page with redirect" issue in Google Search Console

RewriteEngine On

# Force HTTPS - Redirect all HTTP traffic to HTTPS
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://www.junksexpert.com/$1 [R=301,L]

# Force WWW - Redirect non-www to www
RewriteCond %{HTTP_HOST} ^junksexpert\.com [NC]
RewriteRule ^(.*)$ https://www.junksexpert.com/$1 [R=301,L]

# Security Headers for better SEO
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
</IfModule>

# Compression for better performance
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Cache Control for better performance
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
</IfModule>
