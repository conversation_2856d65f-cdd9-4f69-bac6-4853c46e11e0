/**
 * Cache control middleware for static assets
 */

const setCacheControl = (req, res, next) => {
  // Path being requested
  const path = req.path;
  
  // Set different cache times based on file type
  if (path.match(/\.(jpg|jpeg|png|gif|webp)$/i)) {
    // Images - cache for 1 week (604800 seconds)
    res.setHeader('Cache-Control', 'public, max-age=604800, immutable');
  } else if (path.match(/\.(css|js)$/i)) {
    // CSS and JS - cache for 1 day (86400 seconds)
    res.setHeader('Cache-Control', 'public, max-age=86400');
  } else if (path.match(/\.(woff|woff2|ttf|eot)$/i)) {
    // Fonts - cache for 1 week (604800 seconds)
    res.setHeader('Cache-Control', 'public, max-age=604800, immutable');
  } else if (path.match(/\.(ico|svg)$/i)) {
    // Icons and SVGs - cache for 1 day (86400 seconds)
    res.setHeader('Cache-Control', 'public, max-age=86400');
  } else {
    // Everything else - cache for 5 minutes (300 seconds)
    res.setHeader('Cache-Control', 'public, max-age=300');
  }
  
  next();
};

module.exports = setCacheControl; 