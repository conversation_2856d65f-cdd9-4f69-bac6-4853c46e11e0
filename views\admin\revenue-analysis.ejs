<%- include('../partials/header') %>

<div class="pt-24 pb-16 bg-gray-50">
    <div class="container mx-auto px-4">
        
        <!-- Page Header -->
        <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
            <div>
                <h1 class="text-2xl font-bold text-gray-800">Revenue Analysis</h1>
                <p class="text-gray-600">Track and analyze your business revenue</p>
            </div>
            <div class="mt-4 md:mt-0">
                <a href="/admin/dashboard" class="text-blue-600 hover:text-blue-800">
                    <i class="fas fa-arrow-left mr-1"></i> Back to Dashboard
                </a>
            </div>
        </div>
        
        <!-- Date Range Filter -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-lg font-bold text-gray-800 mb-4">Select Time Period</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label for="date-range" class="block text-sm font-medium text-gray-700 mb-1">Date Range</label>
                    <select id="date-range" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                        <option value="all_time">All Time</option>
                        <option value="this_month" selected>This Month</option>
                        <option value="last_month">Last Month</option>
                        <option value="this_quarter">This Quarter</option>
                        <option value="last_quarter">Last Quarter</option>
                        <option value="this_year">This Year</option>
                        <option value="last_year">Last Year</option>
                        <option value="custom">Custom Range</option>
                    </select>
                </div>
                
                <div id="custom-date-container" class="hidden md:col-span-3 grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="start-date" class="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
                        <input type="date" id="start-date" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                    </div>
                    <div>
                        <label for="end-date" class="block text-sm font-medium text-gray-700 mb-1">End Date</label>
                        <input type="date" id="end-date" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                    </div>
                </div>
            </div>
            
            <div class="mt-4 flex justify-end">
                <button id="apply-date-range" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50">
                    Apply
                </button>
            </div>
        </div>
        
        <!-- Overview Stats -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            <div class="bg-white rounded-lg shadow-md p-6">
                <h3 class="text-sm font-medium text-gray-500 mb-2">Total Revenue</h3>
                <p class="text-3xl font-bold text-gray-900" id="total-revenue">AED 0.00</p>
                <div class="mt-2 flex items-center text-sm">
                    <span id="revenue-change" class="text-green-600">
                        <i class="fas fa-arrow-up mr-1"></i> 0%
                    </span>
                    <span class="text-gray-500 ml-2" id="period-comparison">vs previous period</span>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow-md p-6">
                <h3 class="text-sm font-medium text-gray-500 mb-2">Completed Orders</h3>
                <p class="text-3xl font-bold text-gray-900" id="completed-orders">0</p>
                <div class="mt-2 flex items-center text-sm">
                    <span id="orders-change" class="text-green-600">
                        <i class="fas fa-arrow-up mr-1"></i> 0%
                    </span>
                    <span class="text-gray-500 ml-2">vs previous period</span>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow-md p-6">
                <h3 class="text-sm font-medium text-gray-500 mb-2">Average Order Value</h3>
                <p class="text-3xl font-bold text-gray-900" id="avg-order-value">AED 0.00</p>
                <div class="mt-2 flex items-center text-sm">
                    <span id="avg-value-change" class="text-green-600">
                        <i class="fas fa-arrow-up mr-1"></i> 0%
                    </span>
                    <span class="text-gray-500 ml-2">vs previous period</span>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow-md p-6">
                <h3 class="text-sm font-medium text-gray-500 mb-2">Payment Collection Rate</h3>
                <p class="text-3xl font-bold text-gray-900" id="payment-rate">0%</p>
                <div class="mt-2 flex items-center text-sm">
                    <span id="payment-rate-change" class="text-green-600">
                        <i class="fas fa-arrow-up mr-1"></i> 0%
                    </span>
                    <span class="text-gray-500 ml-2">vs previous period</span>
                </div>
            </div>
        </div>
        
        <!-- Charts -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-lg font-bold text-gray-800 mb-4">Monthly Revenue</h2>
                <div class="h-80">
                    <canvas id="monthly-revenue-chart"></canvas>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-lg font-bold text-gray-800 mb-4">Revenue by Service</h2>
                <div class="h-80">
                    <canvas id="service-revenue-chart"></canvas>
                </div>
            </div>
        </div>
        
        <!-- More Charts -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-lg font-bold text-gray-800 mb-4">Revenue by Category</h2>
                <div class="h-80">
                    <canvas id="category-revenue-chart"></canvas>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-lg font-bold text-gray-800 mb-4">Payment Methods</h2>
                <div class="h-80">
                    <canvas id="payment-methods-chart"></canvas>
                </div>
            </div>
        </div>
        
        <!-- Revenue Table -->
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <div class="p-6 border-b">
                <h2 class="text-lg font-bold text-gray-800">Revenue by Service</h2>
            </div>
            
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Service
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Category
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Orders
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Revenue
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Avg. Order Value
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200" id="revenue-table-body">
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500" colspan="5">
                                Loading revenue data...
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
let revenueData = {};
let previousPeriodData = {};
let currentDateRange = 'this_month';
let startDate = null;
let endDate = null;

// Initialize the page
document.addEventListener('DOMContentLoaded', function() {
    setupDateControls();
    fetchRevenueData();
});

// Setup date range controls
function setupDateControls() {
    const dateRangeSelect = document.getElementById('date-range');
    const customDateContainer = document.getElementById('custom-date-container');
    const applyButton = document.getElementById('apply-date-range');
    
    // Set default dates
    const today = new Date();
    const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
    
    document.getElementById('start-date').valueAsDate = firstDayOfMonth;
    document.getElementById('end-date').valueAsDate = today;
    
    // Show/hide custom date inputs
    dateRangeSelect.addEventListener('change', function() {
        if (this.value === 'custom') {
            customDateContainer.classList.remove('hidden');
        } else {
            customDateContainer.classList.add('hidden');
        }
    });
    
    // Apply button
    applyButton.addEventListener('click', function() {
        currentDateRange = dateRangeSelect.value;
        
        if (currentDateRange === 'custom') {
            startDate = document.getElementById('start-date').value;
            endDate = document.getElementById('end-date').value;
        } else {
            startDate = null;
            endDate = null;
        }
        
        fetchRevenueData();
    });
}

// Fetch revenue analysis data
async function fetchRevenueData() {
    try {
        // Construct query params
        let url = '/admin/api/revenue-analysis';
        const params = new URLSearchParams();
        
        params.append('dateRange', currentDateRange);
        
        if (startDate && endDate) {
            params.append('startDate', startDate);
            params.append('endDate', endDate);
        }
        
        url += `?${params.toString()}`;
        
        const response = await fetch(url);
        
        if (!response.ok) {
            throw new Error('Failed to fetch revenue data');
        }
        
        const data = await response.json();
        revenueData = data.currentPeriod;
        previousPeriodData = data.previousPeriod;
        
        // Update UI with revenue data
        updateOverviewStats();
        renderCharts();
        renderRevenueTable();
        
    } catch (error) {
        console.error('Error fetching revenue data:', error);
        alert('Error loading revenue data. Please try again.');
    }
}

// Update overview statistics
function updateOverviewStats() {
    // Format numbers
    const formatter = new Intl.NumberFormat('en-AE', {
        style: 'currency',
        currency: 'AED',
        minimumFractionDigits: 2
    });
    
    // Calculate percentage changes
    const revenueChange = calculatePercentageChange(
        previousPeriodData.totalRevenue || 0,
        revenueData.totalRevenue || 0
    );
    
    const ordersChange = calculatePercentageChange(
        previousPeriodData.completedOrders || 0,
        revenueData.completedOrders || 0
    );
    
    const avgOrderChange = calculatePercentageChange(
        previousPeriodData.averageOrderValue || 0,
        revenueData.averageOrderValue || 0
    );
    
    const paymentRateChange = calculatePercentageChange(
        previousPeriodData.paymentCollectionRate || 0,
        revenueData.paymentCollectionRate || 0
    );
    
    // Update DOM
    document.getElementById('total-revenue').textContent = formatter.format(revenueData.totalRevenue || 0);
    document.getElementById('completed-orders').textContent = revenueData.completedOrders || 0;
    document.getElementById('avg-order-value').textContent = formatter.format(revenueData.averageOrderValue || 0);
    document.getElementById('payment-rate').textContent = `${Math.round(revenueData.paymentCollectionRate || 0)}%`;
    
    // Update change indicators
    updateChangeIndicator('revenue-change', revenueChange);
    updateChangeIndicator('orders-change', ordersChange);
    updateChangeIndicator('avg-value-change', avgOrderChange);
    updateChangeIndicator('payment-rate-change', paymentRateChange);
    
    // Update period comparison text
    document.getElementById('period-comparison').textContent = `vs ${getPreviousPeriodLabel()}`;
}

// Render charts
function renderCharts() {
    renderMonthlyRevenueChart();
    renderServiceRevenueChart();
    renderCategoryRevenueChart();
    renderPaymentMethodsChart();
}

// Render monthly revenue chart
function renderMonthlyRevenueChart() {
    const ctx = document.getElementById('monthly-revenue-chart').getContext('2d');
    
    // Check if chart already exists and destroy it
    if (window.monthlyRevenueChart) {
        window.monthlyRevenueChart.destroy();
    }
    
    // Prepare data
    const labels = revenueData.monthlyRevenue ? Object.keys(revenueData.monthlyRevenue) : [];
    const data = revenueData.monthlyRevenue ? Object.values(revenueData.monthlyRevenue) : [];
    
    window.monthlyRevenueChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: labels,
            datasets: [{
                label: 'Revenue',
                data: data,
                backgroundColor: 'rgba(59, 130, 246, 0.5)',
                borderColor: 'rgb(59, 130, 246)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return 'AED ' + value;
                        }
                    }
                }
            },
            plugins: {
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return 'Revenue: AED ' + context.raw.toFixed(2);
                        }
                    }
                }
            }
        }
    });
}

// Render service revenue chart
function renderServiceRevenueChart() {
    const ctx = document.getElementById('service-revenue-chart').getContext('2d');
    
    // Check if chart already exists and destroy it
    if (window.serviceRevenueChart) {
        window.serviceRevenueChart.destroy();
    }
    
    // Prepare data
    const services = revenueData.serviceRevenue ? Object.keys(revenueData.serviceRevenue) : [];
    const data = revenueData.serviceRevenue ? Object.values(revenueData.serviceRevenue) : [];
    
    // Generate colors
    const colors = generateChartColors(services.length);
    
    window.serviceRevenueChart = new Chart(ctx, {
        type: 'pie',
        data: {
            labels: services,
            datasets: [{
                data: data,
                backgroundColor: colors,
                hoverOffset: 4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const value = context.raw;
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = Math.round((value / total) * 100);
                            return `${context.label}: AED ${value.toFixed(2)} (${percentage}%)`;
                        }
                    }
                }
            }
        }
    });
}

// Render category revenue chart
function renderCategoryRevenueChart() {
    const ctx = document.getElementById('category-revenue-chart').getContext('2d');
    
    // Check if chart already exists and destroy it
    if (window.categoryRevenueChart) {
        window.categoryRevenueChart.destroy();
    }
    
    // Prepare data
    const categories = revenueData.categoryRevenue ? Object.keys(revenueData.categoryRevenue) : [];
    const data = revenueData.categoryRevenue ? Object.values(revenueData.categoryRevenue) : [];
    
    // Generate colors
    const colors = generateChartColors(categories.length);
    
    window.categoryRevenueChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: categories,
            datasets: [{
                data: data,
                backgroundColor: colors,
                hoverOffset: 4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const value = context.raw;
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = Math.round((value / total) * 100);
                            return `${context.label}: AED ${value.toFixed(2)} (${percentage}%)`;
                        }
                    }
                }
            }
        }
    });
}

// Render payment methods chart
function renderPaymentMethodsChart() {
    const ctx = document.getElementById('payment-methods-chart').getContext('2d');
    
    // Check if chart already exists and destroy it
    if (window.paymentMethodsChart) {
        window.paymentMethodsChart.destroy();
    }
    
    // Prepare data
    const methods = revenueData.paymentMethods ? Object.keys(revenueData.paymentMethods) : [];
    const data = revenueData.paymentMethods ? Object.values(revenueData.paymentMethods) : [];
    
    // Format payment method labels
    const formattedMethods = methods.map(method => {
        switch (method) {
            case 'card': return 'Credit/Debit Card';
            case 'cash': return 'Cash on Service';
            case 'bank_transfer': return 'Bank Transfer';
            default: return method;
        }
    });
    
    // Generate colors
    const colors = [
        'rgba(59, 130, 246, 0.7)',  // Blue for card
        'rgba(16, 185, 129, 0.7)',  // Green for cash
        'rgba(245, 158, 11, 0.7)'   // Amber for bank_transfer
    ];
    
    window.paymentMethodsChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: formattedMethods,
            datasets: [{
                label: 'Revenue by Payment Method',
                data: data,
                backgroundColor: colors,
                borderColor: colors.map(c => c.replace('0.7', '1')),
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            indexAxis: 'y',
            scales: {
                x: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return 'AED ' + value;
                        }
                    }
                }
            },
            plugins: {
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const value = context.raw;
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = Math.round((value / total) * 100);
                            return `Revenue: AED ${value.toFixed(2)} (${percentage}%)`;
                        }
                    }
                }
            }
        }
    });
}

// Render revenue table
function renderRevenueTable() {
    const tableBody = document.getElementById('revenue-table-body');
    
    if (!revenueData.serviceDetails || revenueData.serviceDetails.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500" colspan="5">
                    No revenue data available for the selected period.
                </td>
            </tr>
        `;
        return;
    }
    
    // Sort services by revenue (highest first)
    const sortedServices = [...revenueData.serviceDetails].sort((a, b) => b.revenue - a.revenue);
    
    tableBody.innerHTML = sortedServices.map(service => `
        <tr>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                ${service.name}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                ${service.category || 'N/A'}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                ${service.orders}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                AED ${service.revenue.toFixed(2)}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                AED ${service.averageOrderValue.toFixed(2)}
            </td>
        </tr>
    `).join('');
}

// Helper function to calculate percentage change
function calculatePercentageChange(previous, current) {
    if (previous === 0) return 100; // If previous was 0, treat as 100% increase
    return ((current - previous) / previous) * 100;
}

// Helper function to update change indicator
function updateChangeIndicator(elementId, percentageChange) {
    const element = document.getElementById(elementId);
    
    if (percentageChange > 0) {
        element.classList.remove('text-red-600');
        element.classList.add('text-green-600');
        element.innerHTML = `<i class="fas fa-arrow-up mr-1"></i> ${Math.abs(percentageChange).toFixed(1)}%`;
    } else if (percentageChange < 0) {
        element.classList.remove('text-green-600');
        element.classList.add('text-red-600');
        element.innerHTML = `<i class="fas fa-arrow-down mr-1"></i> ${Math.abs(percentageChange).toFixed(1)}%`;
    } else {
        element.classList.remove('text-green-600', 'text-red-600');
        element.classList.add('text-gray-600');
        element.innerHTML = `0.0%`;
    }
}

// Helper function to get previous period label
function getPreviousPeriodLabel() {
    switch (currentDateRange) {
        case 'this_month':
            return 'last month';
        case 'last_month':
            return 'two months ago';
        case 'this_quarter':
            return 'last quarter';
        case 'last_quarter':
            return 'two quarters ago';
        case 'this_year':
            return 'last year';
        case 'last_year':
            return 'two years ago';
        case 'custom':
            return 'previous period';
        default:
            return 'previous period';
    }
}

// Generate chart colors
function generateChartColors(count) {
    const baseColors = [
        'rgba(59, 130, 246, 0.7)',   // Blue
        'rgba(16, 185, 129, 0.7)',   // Green
        'rgba(245, 158, 11, 0.7)',   // Amber
        'rgba(239, 68, 68, 0.7)',    // Red
        'rgba(139, 92, 246, 0.7)',   // Purple
        'rgba(249, 115, 22, 0.7)',   // Orange
        'rgba(20, 184, 166, 0.7)',   // Teal
        'rgba(236, 72, 153, 0.7)',   // Pink
    ];
    
    if (count <= baseColors.length) {
        return baseColors.slice(0, count);
    }
    
    // If we need more colors, generate them
    const colors = [...baseColors];
    
    for (let i = baseColors.length; i < count; i++) {
        const r = Math.floor(Math.random() * 200 + 50);
        const g = Math.floor(Math.random() * 200 + 50);
        const b = Math.floor(Math.random() * 200 + 50);
        colors.push(`rgba(${r}, ${g}, ${b}, 0.7)`);
    }
    
    return colors;
}
</script>

<%- include('../partials/footer') %> 