// sseo.js - Simple SEO Helper for Node.js (EJS projects)

/**
 * Advanced SEO Helper for JunkExperts Website
 * Expert-level SEO optimization with comprehensive meta tags, structured data, and performance enhancements
 * Main Keyword Focus: "Junks Expert" and related junk removal services in UAE
 */

// Top 15 Keywords for JunkExperts Website
const TOP_KEYWORDS = [
    'junks expert',
    'junk removal UAE',
    'furniture removal Dubai',
    'appliance disposal Abu Dhabi',
    'eco-friendly junk removal',
    'same day junk removal',
    'professional junk removal services',
    'sofa removal UAE',
    'bed removal service',
    'wardrobe disposal Dubai',
    'home furniture removal',
    'junk removal company UAE',
    'furniture disposal Dubai',
    'junk removal Al Quoz',
    'UAE junk removal experts'
];

// Advanced Meta Tags Generator
function generateAdvancedMetaTags({
    title, 
    description, 
    keywords, 
    url, 
    image, 
    type = 'website',
    locale = 'en_AE',
    siteName = 'JunkExperts UAE',
    twitterHandle = '@JunkExpertsUAE'
}) {
    const keywordString = keywords || TOP_KEYWORDS.join(', ');
    
    return `
    <!-- Primary Meta Tags -->
    <title>${title}</title>
    <meta name="title" content="${title}">
    <meta name="description" content="${description}">
    <meta name="keywords" content="${keywordString}">
    <meta name="robots" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1">
    <meta name="author" content="JunkExperts UAE">
    <meta name="language" content="en">
    <meta name="revisit-after" content="7 days">
    <meta name="distribution" content="global">
    <meta name="rating" content="general">
    <link rel="canonical" href="${url}">
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="${type}">
    <meta property="og:url" content="${url}">
    <meta property="og:title" content="${title}">
    <meta property="og:description" content="${description}">
    <meta property="og:image" content="${image}">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">
    <meta property="og:site_name" content="${siteName}">
    <meta property="og:locale" content="${locale}">
    
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="${url}">
    <meta property="twitter:title" content="${title}">
    <meta property="twitter:description" content="${description}">
    <meta property="twitter:image" content="${image}">
    <meta property="twitter:site" content="${twitterHandle}">
    
    <!-- Geographic Meta Tags for UAE -->
    <meta name="geo.region" content="AE-DU">
    <meta name="geo.placename" content="Dubai">
    <meta name="geo.position" content="25.1230494;55.2214023">
    <meta name="ICBM" content="25.1230494, 55.2214023">
    
    <!-- Mobile Optimization -->
    <meta name="theme-color" content="#f97316">
    <meta name="msapplication-TileColor" content="#f97316">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="JunkExperts">
    <meta name="format-detection" content="telephone=yes">
    <meta name="format-detection" content="address=yes">
    
    <!-- Performance Optimization -->
    <link rel="dns-prefetch" href="//fonts.googleapis.com">
    <link rel="dns-prefetch" href="//cdnjs.cloudflare.com">
    <link rel="dns-prefetch" href="//unpkg.com">
    <link rel="dns-prefetch" href="//maps.google.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    `;
}

// Enhanced LocalBusiness Schema
function generateEnhancedLocalBusinessSchema({
    name = 'JunkExperts',
    description = 'Professional junk removal and furniture disposal services in UAE',
    url = 'https://www.junksexpert.com',
    telephone = '+971569257614',
    email = '<EMAIL>',
    address = {
        streetAddress: '22nd St, Al Quoz',
        addressLocality: 'Dubai',
        addressCountry: 'AE'
    },
    additionalData = {}
}) {
    const schema = {
        "@context": "https://schema.org",
        "@type": "LocalBusiness",
        "name": name,
        "description": description,
        "url": url,
        "telephone": telephone,
        "email": email,
        "address": {
            "@type": "PostalAddress",
            ...address
        },
        "geo": {
            "@type": "GeoCoordinates",
            "latitude": "25.1230494",
            "longitude": "55.2214023"
        },
        "openingHours": [
            "Mo-Sa 08:00-20:00",
            "Su 09:00-17:00"
        ],
        "serviceArea": {
            "@type": "State",
            "name": "United Arab Emirates"
        },
        "priceRange": "AED 199 - AED 2000",
        "image": "https://www.junksexpert.com/images/junkexperts-logo.jpg",
        "sameAs": [
            "https://www.facebook.com/share/1AjgWj7njS/",
            "https://www.instagram.com/expertjunk.com5/"
        ],
        "aggregateRating": {
            "@type": "AggregateRating",
            "ratingValue": "4.8",
            "reviewCount": "150"
        },
        "hasOfferCatalog": {
            "@type": "OfferCatalog",
            "name": "Junk Removal Services",
            "itemListElement": [
                {
                    "@type": "Offer",
                    "itemOffered": {
                        "@type": "Service",
                        "name": "Furniture Removal"
                    }
                },
                {
                    "@type": "Offer",
                    "itemOffered": {
                        "@type": "Service",
                        "name": "Appliance Disposal"
                    }
                }
            ]
        },
        ...additionalData
    };
    
    return `<script type="application/ld+json">
${JSON.stringify(schema, null, 2)}
</script>`;
}

// Service Schema Generator
function generateServiceSchema({
    serviceName,
    serviceDescription,
    serviceUrl,
    serviceType = 'Service',
    areaServed = 'United Arab Emirates'
}) {
    const schema = {
        "@context": "https://schema.org",
        "@type": serviceType,
        "name": serviceName,
        "description": serviceDescription,
        "url": serviceUrl,
        "provider": {
            "@type": "LocalBusiness",
            "name": "JunkExperts",
            "telephone": "+971569257614",
            "url": "https://www.junksexpert.com"
        },
        "areaServed": areaServed,
        "serviceType": "Junk Removal",
        "category": "Waste Management"
    };
    
    return `<script type="application/ld+json">
${JSON.stringify(schema, null, 2)}
</script>`;
}

// FAQ Schema Generator
function generateFAQSchema(faqs) {
    const schema = {
        "@context": "https://schema.org",
        "@type": "FAQPage",
        "mainEntity": faqs.map(faq => ({
            "@type": "Question",
            "name": faq.question,
            "acceptedAnswer": {
                "@type": "Answer",
                "text": faq.answer
            }
        }))
    };
    
    return `<script type="application/ld+json">
${JSON.stringify(schema, null, 2)}
</script>`;
}

// Breadcrumb Schema Generator
function generateBreadcrumbSchema(breadcrumbs) {
    const schema = {
        "@context": "https://schema.org",
        "@type": "BreadcrumbList",
        "itemListElement": breadcrumbs.map((crumb, index) => ({
            "@type": "ListItem",
            "position": index + 1,
            "name": crumb.name,
            "item": crumb.url
        }))
    };
    
    return `<script type="application/ld+json">
${JSON.stringify(schema, null, 2)}
</script>`;
}

// Review Schema Generator
function generateReviewSchema(reviews) {
    const schema = {
        "@context": "https://schema.org",
        "@type": "Organization",
        "name": "JunkExperts",
        "review": reviews.map(review => ({
            "@type": "Review",
            "author": {
                "@type": "Person",
                "name": review.author
            },
            "reviewRating": {
                "@type": "Rating",
                "ratingValue": review.rating,
                "bestRating": "5"
            },
            "reviewBody": review.text
        }))
    };
    
    return `<script type="application/ld+json">
${JSON.stringify(schema, null, 2)}
</script>`;
}

// SEO Performance Optimization
function generatePerformanceOptimizations() {
    return `
    <!-- Critical Resource Hints -->
    <link rel="preload" href="/css/critical.css" as="style">
    <link rel="preload" href="/fonts/main-font.woff2" as="font" type="font/woff2" crossorigin>
    
    <!-- Lazy Loading Script -->
    <script>
        // Intersection Observer for lazy loading
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        imageObserver.unobserve(img);
                    }
                });
            });
            
            document.querySelectorAll('img[data-src]').forEach(img => {
                imageObserver.observe(img);
            });
        }
    </script>
    `;
}

module.exports = {
    TOP_KEYWORDS,
    generateAdvancedMetaTags,
    generateEnhancedLocalBusinessSchema,
    generateServiceSchema,
    generateFAQSchema,
    generateBreadcrumbSchema,
    generateReviewSchema,
    generatePerformanceOptimizations,
    // Legacy functions for backward compatibility
    generateMetaTags: generateAdvancedMetaTags,
    generateLocalBusinessSchema: generateEnhancedLocalBusinessSchema
};