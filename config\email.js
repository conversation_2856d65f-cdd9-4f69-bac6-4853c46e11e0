const nodemailer = require('nodemailer');

// Create a transporter object using Brevo SMTP
const transporter = nodemailer.createTransport({
  host: process.env.BREVO_SMTP_HOST || 'smtp-relay.brevo.com',
  port: process.env.BREVO_SMTP_PORT || 587,
  secure: false, // true for 465, false for other ports
  auth: {
    user: process.env.BREVO_SMTP_USER || '<EMAIL>',
    pass: process.env.BREVO_SMTP_PASS || 'MGWqtS5g4LRkw1Tz',
  },
  debug: true, // Enable debugging
});

// Verify connection at startup
transporter.verify(function (error, success) {
  if (error) {
    console.error('SMTP Connection Error:', error);
  } else {
    console.log('SMTP Server is ready to send emails');
  }
});

// Function to send order confirmation to admin
const sendOrderNotificationToAdmin = async (order) => {
  // Make sure admin email is defined
  const adminEmail = process.env.ADMIN_EMAIL || '<EMAIL>';
  console.log(`Attempting to send order notification to admin: ${adminEmail}`);
  
  try {
    const mailOptions = {
      from: `"JunkExperts" <${process.env.EMAIL_FROM || '<EMAIL>'}>`,
      to: adminEmail,
      subject: `New Order Received - #${order._id}`,
      html: `
        <h1>New Order Received</h1>
        <p><strong>Order ID:</strong> ${order._id}</p>
        <p><strong>Customer:</strong> ${order.customerName}</p>
        <p><strong>Email:</strong> ${order.email}</p>
        <p><strong>Phone:</strong> ${order.phone}</p>
        <p><strong>Service:</strong> ${order.service}</p>
        <p><strong>Date:</strong> ${new Date(order.date).toLocaleString()}</p>
        <p><strong>Address:</strong> ${order.address}</p>
        <p><strong>Total Amount:</strong> AED ${order.totalAmount.toFixed(2)}</p>
        <p><strong>Payment Status:</strong> ${order.paymentStatus}</p>
        <p><strong>Payment Method:</strong> ${getPaymentMethodText(order.paymentMethod)}</p>
        <p><strong>Message:</strong> ${order.message || 'No message provided'}</p>
      `,
    };

    console.log('Sending email with options:', JSON.stringify(mailOptions, null, 2));
    const info = await transporter.sendMail(mailOptions);

    console.log('Email sent to admin successfully:', info.messageId);
    return true;
  } catch (error) {
    console.error('Error sending email to admin:', error);
    console.error('Error details:', JSON.stringify(error, null, 2));
    return false;
  }
};

// Function to send order confirmation to customer
const sendOrderConfirmationToCustomer = async (order) => {
  console.log(`Attempting to send order confirmation to customer: ${order.email}`);
  
  try {
    const mailOptions = {
      from: `"JunkExperts" <${process.env.EMAIL_FROM || '<EMAIL>'}>`,
      to: order.email,
      subject: `Order Confirmation - JunkExperts #${order._id}`,
      html: `
        <h1>Thank You for Your Order!</h1>
        <p>Dear ${order.customerName},</p>
        <p>Your order has been successfully placed with JunkExperts.</p>
        <p><strong>Order ID:</strong> ${order._id}</p>
        <p><strong>Service:</strong> ${order.service}</p>
        <p><strong>Date:</strong> ${new Date(order.date).toLocaleString()}</p>
        <p><strong>Address:</strong> ${order.address}</p>
        <p><strong>Total Amount:</strong> AED ${order.totalAmount.toFixed(2)}</p>
        <p><strong>Payment Status:</strong> ${order.paymentStatus}</p>
        <p><strong>Payment Method:</strong> ${getPaymentMethodText(order.paymentMethod)}</p>
        <p>Your order has been confirmed and our team is preparing for your service.</p>
        <p>If you have any questions or need to reschedule, please contact us at +971 56 925 7614.</p>
        <p>Thank you for choosing JunkExperts!</p>
      `,
    };

    const info = await transporter.sendMail(mailOptions);
    console.log('Email sent to customer successfully:', info.messageId);
    return true;
  } catch (error) {
    console.error('Error sending email to customer:', error);
    console.error('Error details:', JSON.stringify(error, null, 2));
    return false;
  }
};

// Function to send payment confirmation to customer
const sendPaymentConfirmationEmail = async (order) => {
  console.log(`Attempting to send payment confirmation to customer: ${order.email}`);
  
  try {
    const mailOptions = {
      from: `"JunkExperts" <${process.env.EMAIL_FROM || '<EMAIL>'}>`,
      to: order.email,
      subject: `Payment Confirmation - JunkExperts #${order._id}`,
      html: `
        <h1>Payment Confirmation</h1>
        <p>Dear ${order.customerName},</p>
        <p>We're pleased to confirm that your payment for order #${order._id} has been successfully processed.</p>
        <p><strong>Payment Details:</strong></p>
        <ul>
          <li><strong>Amount:</strong> AED ${order.totalAmount.toFixed(2)}</li>
          <li><strong>Payment Method:</strong> ${getPaymentMethodText(order.paymentMethod)}</li>
          <li><strong>Date:</strong> ${new Date().toLocaleString()}</li>
        </ul>
        <p><strong>Service Details:</strong></p>
        <ul>
          <li><strong>Service:</strong> ${order.service}</li>
          <li><strong>Scheduled Date:</strong> ${new Date(order.date).toLocaleString()}</li>
          <li><strong>Address:</strong> ${order.address}</li>
        </ul>
        <p>Your booking is now confirmed. Our team will be at your location on the scheduled date and time.</p>
        <p>If you have any questions or need to reschedule, please contact us at +971 56 925 7614.</p>
        <p>Thank you for choosing JunkExperts!</p>
      `,
    };

    const info = await transporter.sendMail(mailOptions);
    console.log('Payment confirmation email sent to customer successfully:', info.messageId);
    return true;
  } catch (error) {
    console.error('Error sending payment confirmation email:', error);
    console.error('Error details:', JSON.stringify(error, null, 2));
    return false;
  }
};

// Function to send payment notification to admin
const sendPaymentNotificationToAdmin = async (order) => {
  const adminEmail = process.env.ADMIN_EMAIL || '<EMAIL>';
  console.log(`Attempting to send payment notification to admin: ${adminEmail}`);
  
  try {
    const mailOptions = {
      from: `"JunkExperts" <${process.env.EMAIL_FROM || '<EMAIL>'}>`,
      to: adminEmail,
      subject: `Payment Received - Order #${order._id}`,
      html: `
        <h1>Payment Received</h1>
        <p><strong>Order ID:</strong> ${order._id}</p>
        <p><strong>Customer:</strong> ${order.customerName}</p>
        <p><strong>Email:</strong> ${order.email}</p>
        <p><strong>Payment Amount:</strong> AED ${order.totalAmount.toFixed(2)}</p>
        <p><strong>Payment Method:</strong> ${getPaymentMethodText(order.paymentMethod)}</p>
        <p><strong>Payment Date:</strong> ${new Date().toLocaleString()}</p>
        <p><strong>Service:</strong> ${order.service}</p>
        <p><strong>Scheduled Date:</strong> ${new Date(order.date).toLocaleString()}</p>
      `,
    };

    const info = await transporter.sendMail(mailOptions);
    console.log('Payment notification email sent to admin successfully:', info.messageId);
    return true;
  } catch (error) {
    console.error('Error sending payment notification email to admin:', error);
    console.error('Error details:', JSON.stringify(error, null, 2));
    return false;
  }
};

// Function to send contact form submission to admin
const sendContactFormToAdmin = async (formData) => {
  const adminEmail = process.env.ADMIN_EMAIL || '<EMAIL>';
  console.log(`Attempting to send contact form to admin: ${adminEmail}`);
  
  try {
    const mailOptions = {
      from: `"JunkExperts Website" <${process.env.EMAIL_FROM || '<EMAIL>'}>`,
      to: adminEmail,
      subject: `New Contact Form Submission - ${formData.subject || 'General Inquiry'}`,
      html: `
        <h1>New Contact Form Submission</h1>
        <p><strong>Name:</strong> ${formData.name}</p>
        <p><strong>Email:</strong> ${formData.email}</p>
        <p><strong>Phone:</strong> ${formData.phone || 'Not provided'}</p>
        <p><strong>Subject:</strong> ${formData.subject || 'General Inquiry'}</p>
        <p><strong>Message:</strong></p>
        <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px;">
          ${formData.message}
        </div>
        <p><strong>Submission Date:</strong> ${new Date().toLocaleString()}</p>
      `,
    };

    const info = await transporter.sendMail(mailOptions);
    console.log('Contact form email sent to admin successfully:', info.messageId);
    return true;
  } catch (error) {
    console.error('Error sending contact form email to admin:', error);
    console.error('Error details:', JSON.stringify(error, null, 2));
    return false;
  }
};

// Function to send email verification code
const sendVerificationEmail = async (email, code) => {
  console.log(`Attempting to send verification code to: ${email}`);
  
  try {
    const mailOptions = {
      from: `"JunkExperts" <${process.env.EMAIL_FROM || '<EMAIL>'}>`,
      to: email,
      subject: `Your Email Verification Code - JunkExperts`,
      html: `
        <h1>Email Verification</h1>
        <p>Your verification code is:</p>
        <div style="background-color: #f5f5f5; padding: 15px; text-align: center; font-size: 24px; letter-spacing: 5px; font-weight: bold; margin: 20px 0; border-radius: 5px;">
          ${code}
        </div>
        <p>This code will expire in 10 minutes.</p>
        <p>If you didn't request this code, you can safely ignore this email.</p>
        <p>Thank you for choosing JunkExperts!</p>
      `,
    };

    const info = await transporter.sendMail(mailOptions);
    console.log('Verification email sent successfully:', info.messageId);
    return true;
  } catch (error) {
    console.error('Error sending verification email:', error);
    console.error('Error details:', JSON.stringify(error, null, 2));
    return false;
  }
};

// Helper function to get payment method text
const getPaymentMethodText = (method) => {
  switch (method) {
    case 'card':
      return 'Credit/Debit Card';
    case 'cash':
      return 'Cash on Service';
    case 'bank_transfer':
      return 'Bank Transfer';
    default:
      return method;
  } 
};      
  
// Function to send refund confirmation to customer
const sendRefundConfirmation = async (order) => {
  console.log(`Attempting to send refund confirmation to customer: ${order.email || order.customer.email}`);
  
  try {
    const customerEmail = order.email || order.customer.email;
    const customerName = order.customerName || order.customer.name;
    const refundAmount = order.refundDetails.amount;
    const refundReason = order.refundDetails.reason;
    const refundDate = new Date(order.refundDetails.date).toLocaleString();
     
    const mailOptions = {
      from: `"JunkExperts" <${process.env.EMAIL_FROM || '<EMAIL>'}>`,
      to: customerEmail,
      subject: `Refund Confirmation - JunkExperts Order #${order._id}`,
      html: `
        <h1>Refund Confirmation</h1>
        <p>Dear ${customerName},</p>
        <p>We're writing to confirm that a refund has been processed for your order #${order._id}.</p>
        <p><strong>Refund Details:</strong></p>
        <ul>
          <li><strong>Refund Amount:</strong> ₹${refundAmount.toFixed(2)}</li>
          <li><strong>Refund Date:</strong> ${refundDate}</li>
          <li><strong>Original Payment Method:</strong> ${getPaymentMethodText(order.paymentMethod)}</li>
          <li><strong>Reason for Refund:</strong> ${refundReason}</li>
        </ul>
        <p>If you paid by card, please allow 5-7 business days for the refund to appear in your account, depending on your bank's processing times.</p>
        <p>If you have any questions about this refund, please contact our customer service team at +971 56 925 7614.</p>
        <p>Thank you for your understanding.</p>
        <p>Best regards,<br>JunkExperts Team</p>
      `,
    };

    const info = await transporter.sendMail(mailOptions);
    console.log('Refund confirmation email sent to customer successfully:', info.messageId);
    
    // Also notify admin about the refund
    await sendRefundNotificationToAdmin(order);
    
    return true;
  } catch (error) {
    console.error('Error sending refund confirmation email:', error);
    console.error('Error details:', JSON.stringify(error, null, 2));
    return false;
  }
};

// Function to send refund notification to admin
const sendRefundNotificationToAdmin = async (order) => {
  const adminEmail = process.env.ADMIN_EMAIL || '<EMAIL>';
  console.log(`Attempting to send refund notification to admin: ${adminEmail}`);
  
  try {
    const refundAmount = order.refundDetails.amount;
    const refundReason = order.refundDetails.reason;
    const refundDate = new Date(order.refundDetails.date).toLocaleString();
    const customerName = order.customerName || order.customer.name;
    const customerEmail = order.email || order.customer.email;
    
    const mailOptions = {
      from: `"JunkExperts" <${process.env.EMAIL_FROM || '<EMAIL>'}>`,
      to: adminEmail,
      subject: `Refund Processed - Order #${order._id}`,
      html: `
        <h1>Refund Processed</h1>
        <p><strong>Order ID:</strong> ${order._id}</p>
        <p><strong>Customer:</strong> ${customerName}</p>
        <p><strong>Email:</strong> ${customerEmail}</p>
        <p><strong>Refund Amount:</strong> ₹${refundAmount.toFixed(2)}</p>
        <p><strong>Original Total:</strong> ₹${order.totalAmount.toFixed(2)}</p>
        <p><strong>Payment Method:</strong> ${getPaymentMethodText(order.paymentMethod)}</p>
        <p><strong>Refund Date:</strong> ${refundDate}</p>
        <p><strong>Reason for Refund:</strong> ${refundReason}</p>
        <p><strong>Processed By:</strong> ${order.refundDetails.processedBy || 'Admin'}</p>
      `,
    };

    const info = await transporter.sendMail(mailOptions);
    console.log('Refund notification email sent to admin successfully:', info.messageId);
    return true;
  } catch (error) {
    console.error('Error sending refund notification email to admin:', error);
    console.error('Error details:', JSON.stringify(error, null, 2));
    return false;
  }
};

module.exports = {
  sendOrderNotificationToAdmin,
  sendOrderConfirmationToCustomer,
  sendPaymentConfirmationEmail,
  sendPaymentNotificationToAdmin,
  sendContactFormToAdmin,
  sendVerificationEmail,
  sendRefundConfirmation
};   