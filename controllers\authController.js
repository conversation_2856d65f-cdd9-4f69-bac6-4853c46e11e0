const User = require('../models/User');
const bcrypt = require('bcryptjs');
const crypto = require('crypto');
const { sendVerificationEmail } = require('../config/email');

// In-memory store for verification codes (in production, use Redis or similar)
const verificationCodes = new Map();

// Register a new user
exports.register = async (req, res) => {
  try {
    const { name, email, password, phone, address } = req.body;
    
    // Check if user already exists
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      req.session.error = 'User with this email already exists';
      return res.redirect('/auth/register');
    }
    
    // Create new user
    const user = new User({
      name,
      email,
      password,
      phone: phone || '+971 00 000 0000', // Default phone if not provided
      address: address || 'UAE', // Default address if not provided
    });
    
    await user.save();
    
    req.session.success = 'Registration successful! Please log in.';
    res.redirect('/auth/login');
  } catch (error) {
    console.error('Registration error:', error);
    req.session.error = 'Failed to register user';
    res.redirect('/auth/register');
  }
};

// Login user
exports.login = async (req, res) => {
  try {
    const { email, password } = req.body;
    
    // Find the user in the database
    const user = await User.findOne({ email }).select('+password');
    
    // If no user found or password doesn't match
    if (!user || !(await bcrypt.compare(password, user.password))) {
      req.flash('error', 'Invalid email or password');
      return res.redirect('/auth/login');
    }

    // Get platform info for specialized handling
    const hostname = req.hostname || '';
    const host = req.get('host') || '';
    const isRender = hostname.includes('onrender.com') || host.includes('onrender.com');
    const isVercel = hostname.includes('vercel.app') || host.includes('vercel.app');
    
    console.log(`Login attempt on platform: ${isRender ? 'Render' : isVercel ? 'Vercel' : 'Other'} for user ${user.email} (${user.role})`);

    // For Vercel, we need special session handling
    if (isVercel) {
      try {
        // Regenerate session with Promise for better async handling
        await new Promise((resolve, reject) => {
          req.session.regenerate(err => {
            if (err) {
              console.error('Vercel login - Error regenerating session:', err);
              reject(err);
            } else {
              resolve();
            }
          });
        });
        
        // Set strong admin cookie for Vercel as primary auth mechanism
        if (user.role === 'admin') {
          const vercelCookieOptions = {
            httpOnly: true,
            secure: true,
            sameSite: 'lax',
            maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days - shorter for security
            path: '/'
          };
          
          // For Vercel, set a more reliable cookie value from env or a fallback
          const cookieValue = process.env.ADMIN_SECRET || process.env.ADMIN_COOKIE_VALUE || 'vercel-admin-key';
          res.cookie('admin-auth', cookieValue, vercelCookieOptions);
          console.log('Vercel admin login: Set admin cookie with special options');
        }
        
        // Set session data
        req.session.isAuthenticated = true;
        req.session.userId = user._id.toString();
        req.session.userRole = user.role;
        req.session.userName = user.name;
        req.session.loginTime = Date.now();
        req.session.vercelAuth = true; // Flag specifically for Vercel logins
        
        // Save session with explicit Promise for better reliability
        await new Promise((resolve, reject) => {
          req.session.save(err => {
            if (err) {
              console.error('Vercel login - Error saving session:', err);
              reject(err);
            } else {
              console.log('Vercel login - Session saved successfully');
              resolve();
            }
          });
        });
        
        // For admin on Vercel, redirect with special handling
        if (user.role === 'admin') {
          console.log('Vercel admin login successful - redirecting to dashboard');
          return res.redirect('/admin/dashboard?platform=vercel');
        } else {
          return res.redirect('/dashboard');
        }
      } catch (vercelErr) {
        console.error('Vercel-specific login error:', vercelErr);
        req.flash('error', 'Login failed on Vercel platform. Please try again.');
        return res.redirect('/auth/login');
      }
    } else {
      // Standard login flow for non-Vercel platforms
      
      // Regenerate session to prevent session fixation
      await new Promise((resolve, reject) => {
        req.session.regenerate(err => {
          if (err) reject(err);
          else resolve();
        });
      });
      
      // User is authenticated, set session data
      req.session.isAuthenticated = true;
      req.session.userId = user._id.toString();
      req.session.userRole = user.role;
      req.session.userName = user.name;
      req.session.loginTime = Date.now();
      
      // For admins, create a persistent cookie that can be used as a backup auth method
      if (user.role === 'admin') {
        console.log(`Admin login on platform: ${isRender ? 'Render' : 'Other'}`);
        
        // Create a persistent admin cookie
        const cookieOptions = {
          httpOnly: true,
          secure: process.env.NODE_ENV === 'production',
          sameSite: 'lax',
          maxAge: 30 * 24 * 60 * 60 * 1000 // 30 days
        };
        
        // Set the admin cookie
        res.cookie('admin-auth', process.env.ADMIN_SECRET || 'admin-secret-key', cookieOptions);
      }
      
      // Redirect based on role
      if (user.role === 'admin') {
        res.redirect('/admin/dashboard');
      } else {
        res.redirect('/dashboard');
      }
    }
  } catch (error) {
    console.error('Login error:', error);
    req.flash('error', 'An error occurred during login');
    res.redirect('/auth/login');
  }
};

// Logout user
exports.logout = (req, res) => {
  try {
    console.log('Logout requested');
    
    // Clear the admin auth cookie with various domain options to ensure it's removed
    const hostname = req.hostname || '';
    const host = req.get('host') || '';
    const isVercel = hostname.includes('vercel.app') || host.includes('vercel.app');
    const isRender = hostname.includes('onrender.com') || host.includes('onrender.com');
    
    // Log platform information
    console.log(`Logout on platform: ${isRender ? 'Render' : isVercel ? 'Vercel' : 'Other'}, hostname: ${hostname}`);
    
    // Clear cookies with basic options first
    res.clearCookie('admin-auth', { path: '/' });
    res.clearCookie('connect.sid', { path: '/' });
    res.clearCookie('junkexpert.sid', { path: '/' });
    
    // For Vercel, we need more thorough cookie clearing
    if (isVercel) {
      console.log('Clearing cookies with Vercel-specific options');
      
      // Try multiple domain variations to ensure cookie removal
      const cookieOptions = {
        path: '/',
        httpOnly: true,
        secure: true,
        sameSite: 'lax'
      };
      
      // Clear with domain
      res.clearCookie('admin-auth', { 
        ...cookieOptions,
        domain: hostname
      });
      
      // Clear with explicit path but no domain
      res.clearCookie('admin-auth', { 
        ...cookieOptions,
        path: '/'
      });
      
      // Clear with no domain (fallback)
      res.clearCookie('admin-auth');
    }
    
    // For Render, clear with specific options
    if (isRender) {
      console.log('Clearing cookies with Render-specific options');
      res.clearCookie('admin-auth', { 
        path: '/',
        domain: hostname,
        httpOnly: true,
        secure: true
      });
    }
    
    // Destroy the session
    req.session.destroy((err) => {
      if (err) {
        console.error('Error destroying session during logout:', err);
      }
      
      console.log('User logged out successfully');
      res.redirect('/auth/login?logout=success');
    });
  } catch (error) {
    console.error('Logout error:', error);
    // Still try to redirect even if error occurs
    res.redirect('/auth/login?error=logout_failed');
  }
};

// Get login page
exports.getLoginPage = (req, res) => {
  res.render('auth/login', {
    title: 'Login | JunkExperts'
  });
};

// Get register page
exports.getRegisterPage = (req, res) => {
  res.render('auth/register', {
    title: 'Register | JunkExperts'
  });
};

// Create initial admin user (for setup)
exports.createAdminUser = async () => {
  // Add retry mechanism
  let retries = 3;
  let success = false;
  let error;
  
  while (retries > 0 && !success) {
    try {
      // Check if admin user already exists
      const adminExists = await User.findOne({ role: 'admin' });
      if (adminExists) {
        console.log('Admin user already exists');
        return true;
      }
      
      // Create admin user
      const admin = new User({
        name: 'Admin',
        email: '<EMAIL>',
        password: 'admin123',  // This should be changed after first login
        phone: '+971 55 000 0000', // Default admin phone
        address: 'Dubai, UAE', // Default admin address
        role: 'admin'
      });
      
      await admin.save();
      console.log('Admin user created successfully');
      success = true;
    } catch (err) {
      error = err;
      console.error(`Error creating admin user (retries left: ${retries-1}):`, err.message);
      retries--;
      // Wait before retrying
      if (retries > 0) {
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }
  }
  
  if (!success) {
    throw new Error(`Failed to create admin user after multiple attempts: ${error.message}`);
  }
  
  return success;
};

// Special direct access for Render environments
exports.renderAdminLogin = async (req, res) => {
  try {
    // Find admin user
    const User = require('../models/User');
    const admin = await User.findOne({ role: 'admin' });
    
    if (!admin) {
      console.error('Render admin login failed: No admin user found');
      return res.status(404).render('error', {
        title: 'Error',
        error: 'No admin user found in the system'
      });
    }
    
    console.log('Render admin login - creating session for admin:', admin._id);
    
    // Regenerate session
    req.session.regenerate(async function(err) {
      if (err) {
        console.error('Render admin login - Error regenerating session:', err);
        return res.status(500).render('error', {
          title: 'Error',
          error: 'Session could not be created. Please try again.'
        });
      }
      
      // Set admin cookie as backup authentication mechanism
      res.cookie('admin-auth', process.env.ADMIN_COOKIE_VALUE || 'render-admin-access', {
        maxAge: 24 * 60 * 60 * 1000, // 24 hours
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
        path: '/'
      });
      
      // Set session data
      req.session.isAuthenticated = true;
      req.session.userId = admin._id.toString();
      req.session.userRole = 'admin';
      req.session.userName = admin.name || 'Admin';
      
      // Save and wait for completion
      try {
        await new Promise((resolve, reject) => {
          req.session.save(function(err) {
            if (err) {
              console.error('Render admin login - Error saving session:', err);
              reject(err);
            } else {
              resolve();
            }
          });
        });
        
        console.log('Render admin login - Session saved successfully:', {
          id: req.sessionID,
          isAuthenticated: req.session.isAuthenticated,
          userId: req.session.userId,
          userRole: req.session.userRole
        });
        
        // Redirect to admin dashboard with direct flag
        res.redirect('/admin/dashboard?direct=true');
      } catch (saveErr) {
        console.error('Render admin login - Error in session save:', saveErr);
        return res.render('error', {
          title: 'Error',
          error: 'Could not complete login process. Please try again.'
        });
      }
    });
  } catch (error) {
    console.error('Render admin login - Error:', error);
    res.status(500).render('error', {
      title: 'Error',
      error: 'Failed to log in. Please try again.'
    });
  }
};

// Direct admin login for production troubleshooting
exports.directAdminLogin = async (req, res) => {
  try {
    // Find admin user
    const admin = await User.findOne({ role: 'admin' });
    
    if (!admin) {
      console.error('Direct admin login failed: No admin user found');
      return res.status(404).json({ error: 'No admin user found' });
    }
    
    // Regenerate session
    req.session.regenerate(async function(err) {
      if (err) {
        console.error('Error regenerating session:', err);
        return res.status(500).json({ error: 'Session regeneration failed' });
      }
      
      // Set session data
      req.session.isAuthenticated = true;
      req.session.userId = admin._id.toString();
      req.session.userRole = 'admin';
      req.session.userName = admin.name || 'Admin';
      
      // Set admin cookie as backup
      res.cookie('admin-auth', 'admin-logged-in', {
        maxAge: 24 * 60 * 60 * 1000, // 24 hours
        httpOnly: true,
        secure: false, // Set to false for local testing
        sameSite: 'lax',
        path: '/'
      });
      
      // Save session
      await new Promise((resolve, reject) => {
        req.session.save(function(err) {
          if (err) {
            console.error('Error saving session:', err);
            reject(err);
          } else {
            resolve();
          }
        });
      });
      
      console.log('Direct admin login successful:', {
        id: admin._id,
        role: admin.role,
        sessionID: req.sessionID
      });
      
      // Redirect to admin dashboard
      res.redirect('/admin/dashboard');
    });
  } catch (error) {
    console.error('Direct admin login error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

// Send verification code
exports.sendVerificationCode = async (req, res) => {
  try {
    const { email } = req.body;
    
    if (!email) {
      return res.status(400).json({ success: false, message: 'Email is required' });
    }
    
    // Generate a 6-digit code
    const verificationCode = Math.floor(100000 + Math.random() * 900000).toString();
    
    // Store the code with expiration (10 minutes)
    verificationCodes.set(email.toLowerCase(), {
      code: verificationCode,
      expiresAt: Date.now() + 10 * 60 * 1000
    });
    
    // Send the code via email
    await sendVerificationEmail(email, verificationCode);
    
    res.status(200).json({ 
      success: true, 
      message: 'Verification code sent to your email' 
    });
  } catch (error) {
    console.error('Error sending verification code:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Failed to send verification code' 
    });
  }
};

// Verify the code
exports.verifyEmailCode = (req, res) => {
  try {
    const { email, code } = req.body;
    
    if (!email || !code) {
      return res.status(400).json({ 
        success: false, 
        message: 'Email and code are required' 
      });
    }
    
    const lowerEmail = email.toLowerCase();
    const storedVerification = verificationCodes.get(lowerEmail);
    
    if (!storedVerification) {
      return res.status(400).json({ 
        success: false, 
        message: 'No verification code found for this email' 
      });
    }
    
    // Check if code has expired
    if (Date.now() > storedVerification.expiresAt) {
      verificationCodes.delete(lowerEmail);
      return res.status(400).json({ 
        success: false, 
        message: 'Verification code has expired' 
      });
    }
    
    // Check if code matches
    if (storedVerification.code !== code) {
      return res.status(400).json({ 
        success: false, 
        message: 'Invalid verification code' 
      });
    }
    
    // Code is valid - clean up
    verificationCodes.delete(lowerEmail);
    
    res.status(200).json({ 
      success: true, 
      message: 'Email verified successfully',
      verified: true,
      email: lowerEmail
    });
  } catch (error) {
    console.error('Error verifying code:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Failed to verify code' 
    });
  }
}; 