const express = require('express');
const router = express.Router();
const adminController = require('../controllers/adminController');
const { isAdmin, isAuthenticated } = require('../middleware/auth');

// Import the normalize function from adminController
const { normalizeOrderFields } = adminController;

// Root admin route - redirect to dashboard
router.get('/', isAuthenticated, isAdmin, (req, res) => {
  res.redirect('/admin/dashboard');
});

// Admin Dashboard
router.get('/dashboard', isAuthenticated, isAdmin, adminController.getDashboard);
router.get('/api/dashboard-stats', isAuthenticated, isAdmin, adminController.getDashboardStats);
router.get('/api/recent-orders', isAuthenticated, isAdmin, adminController.getRecentOrders);

// Admin Orders Management
router.get('/orders', isAuthenticated, isAdmin, adminController.getOrders);
router.get('/api/orders', isAuthenticated, isAdmin, adminController.getOrdersData);
router.get('/api/orders/:id', isAuthenticated, isAdmin, adminController.getOrderData);
router.post('/orders/:id/pay', isAuthenticated, isAdmin, adminController.markOrderAsPaid);
router.post('/api/orders/:id/complete', isAuthenticated, isAdmin, adminController.markOrderAsCompleted);
router.delete('/api/orders/:id', isAuthenticated, isAdmin, adminController.deleteOrder);
router.post('/orders/:id/archive', isAuthenticated, isAdmin, adminController.deleteOrder);
router.post('/api/orders/:id/send-reminder', isAuthenticated, isAdmin, adminController.sendOrderEmail);

// Admin Completed Orders
router.get('/completed-orders', isAuthenticated, isAdmin, adminController.getCompletedOrders);
router.get('/api/completed-orders', isAuthenticated, isAdmin, adminController.getCompletedOrdersData);

// Admin Revenue Analysis
router.get('/revenue-analysis', isAuthenticated, isAdmin, adminController.getDashboard);
router.get('/api/revenue-analysis', isAuthenticated, isAdmin, adminController.getRevenueAnalysis);

// Admin User Management
router.get('/users', isAuthenticated, isAdmin, adminController.getUsers);
router.get('/api/users', isAuthenticated, isAdmin, adminController.getUsersData);
router.post('/api/users', isAuthenticated, isAdmin, adminController.createUser);
router.put('/api/users/:id', isAuthenticated, isAdmin, adminController.updateUser);
router.delete('/api/users/:id', isAuthenticated, isAdmin, adminController.deleteUser);
router.post('/api/users/:userId/reset-password', isAuthenticated, isAdmin, adminController.resetAdminPassword);

// Admin Services Management
router.get('/services', isAuthenticated, isAdmin, adminController.getServices);
router.get('/api/services', isAuthenticated, isAdmin, adminController.getServicesData);
router.post('/api/services', isAuthenticated, isAdmin, adminController.createService);
router.put('/api/services/:id', isAuthenticated, isAdmin, adminController.updateService);
router.delete('/api/services/:id', isAuthenticated, isAdmin, adminController.deleteService);

// Special route for production admin access that bypasses normal auth
router.get('/production-access', async (req, res) => {
  if (process.env.NODE_ENV === 'production') {
    try {
      // Set admin cookie for backup auth mechanism
      res.cookie('admin-auth', process.env.ADMIN_COOKIE_VALUE || 'production-admin-access', {
        maxAge: 24 * 60 * 60 * 1000, // 24 hours
        httpOnly: true,
        secure: true,
        sameSite: 'lax',
        path: '/'
      });
      
      // Redirect to dashboard with direct access flag
      res.redirect('/admin/dashboard?direct=true');
    } catch (error) {
      console.error('Production access error:', error);
      res.status(500).send('Server error during emergency production access');
    }
  } else {
    res.status(403).send('This route is only available in production');
  }
});

// Direct access to dashboard in production (emergency override)
router.get('/direct-dashboard', async (req, res) => {
  if (process.env.NODE_ENV === 'production') {
    try {
      // Find admin user
      const User = require('../models/User');
      const Order = require('../models/Order');
      const Service = require('../models/Service');
      
      // Set admin cookie for future requests
      res.cookie('admin-auth', 'production-admin-direct', {
        maxAge: 24 * 60 * 60 * 1000, // 24 hours
        httpOnly: true,
        secure: true,
        sameSite: 'lax',
        path: '/'
      });
      
      // Find admin for auth
      const admin = await User.findOne({ role: 'admin' });
      if (!admin) {
        return res.status(404).send('No admin found - please set up admin account first');
      }
      
      // Get stats for dashboard
      const totalOrders = await Order.countDocuments();
      const pendingOrders = await Order.countDocuments({ status: 'pending' });
      const completedOrders = await Order.countDocuments({ status: 'completed' });
      const totalServices = await Service.countDocuments();
      const totalUsers = await User.countDocuments();
      
      // Get recent orders
      const recentOrders = await Order.find()
        .sort({ createdAt: -1 })
        .limit(5)
        .lean();
      
      // Calculate revenue
      const paidOrders = await Order.find({ paymentStatus: 'paid' });
      const totalRevenue = paidOrders.reduce((sum, order) => sum + order.totalAmount, 0);
      
      // Get services by category
      const services = await Service.find().lean();
      
      // Group services by category
      const servicesByCategory = {};
      services.forEach(service => {
        if (!service.category) {
          service.category = 'Uncategorized';
        }
        
        if (!servicesByCategory[service.category]) {
          servicesByCategory[service.category] = 0;
        }
        servicesByCategory[service.category]++;
      });
      
      // Render dashboard with admin user
      res.render('admin/dashboard', {
        title: 'Admin Dashboard | JunkExperts',
        user: {
          isAuthenticated: true,
          id: admin._id.toString(),
          role: 'admin',
          name: admin.name
        },
        stats: {
          totalOrders,
          pendingOrders,
          completedOrders,
          totalServices,
          totalUsers,
          totalRevenue
        },
        recentOrders,
        servicesByCategory,
        moment: require('moment')
      });
    } catch (error) {
      console.error('Direct dashboard access error:', error);
      res.status(500).send('Error: ' + error.message);
    }
  } else {
    res.status(403).send('This route is only available in production');
  }
});

// Revenue and payment management routes
router.get('/api/revenue-analysis', isAuthenticated, isAdmin, adminController.getRevenueAnalysis);
router.get('/api/cod-payment/:orderId', isAuthenticated, isAdmin, adminController.getCODPaymentDetails);
router.put('/api/order/:orderId/payment', isAuthenticated, isAdmin, adminController.updateOrderPayment);

// COD Payments page
router.get('/cod-payments', isAuthenticated, isAdmin, (req, res) => {
  res.render('admin/cod-payments', {
    title: 'COD Payments | Admin Dashboard',
    user: req.session.user
  });
});

// Admin Revenue Tracking
router.get('/revenue', isAuthenticated, isAdmin, (req, res) => {
  res.render('admin/revenue', {
    title: 'Revenue Tracking | Admin Dashboard',
    user: {
      id: req.session.userId,
      name: req.session.userName,
      role: req.session.userRole,
      isAuthenticated: req.session.isAuthenticated
    }
  });
});

// Utility route to normalize all orders (admin only, development only)
router.get('/utils/normalize-orders', isAuthenticated, isAdmin, async (req, res) => {
  if (process.env.NODE_ENV === 'production' && !req.query.force) {
    return res.status(403).json({ 
      message: 'This utility is disabled in production. Add ?force=true to override.' 
    });
  }

  try {
    const Order = require('../models/Order');
    const orders = await Order.find();
    const results = {
      total: orders.length,
      updated: 0,
      failed: 0,
      details: []
    };

    for (const order of orders) {
      try {
        // Get normalized fields
        const normalized = normalizeOrderFields(order);
        
        // Apply consistent field values
        order.amount = normalized.amount;
        order.totalAmount = normalized.totalAmount;
        order.paymentMethod = normalized.paymentMethod || 'cod';
        order.date = normalized.date;
        order.scheduledDate = normalized.scheduledDate;
        order.customerName = normalized.customerName;
        
        // Set payment status consistently
        if (normalized.isPaid) {
          order.paymentStatus = 'paid';
        }
        
        await order.save();
        results.updated++;
        results.details.push({ id: order._id, status: 'updated' });
      } catch (error) {
        console.error(`Error normalizing order ${order._id}:`, error);
        results.failed++;
        results.details.push({ id: order._id, status: 'failed', error: error.message });
      }
    }

    res.json({ message: 'Orders normalized', results });
  } catch (error) {
    console.error('Error normalizing orders:', error);
    res.status(500).json({ message: 'Failed to normalize orders', error: error.message });
  }
});

// COD Email Search page
router.get('/cod-email-search', isAuthenticated, isAdmin, adminController.getCODEmailSearch);
router.get('/api/customer-orders', isAuthenticated, isAdmin, adminController.getCustomerOrdersByEmail);

// Refund Management
router.get('/refund-management', isAuthenticated, isAdmin, adminController.getRefundManagement);
router.post('/refund-management/search', isAuthenticated, isAdmin, adminController.searchOrderForRefund);
router.post('/refund-management/process', isAuthenticated, isAdmin, adminController.processRefund);

module.exports = router;