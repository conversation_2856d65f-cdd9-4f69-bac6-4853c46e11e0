<%- include('../partials/header') %>

<div class="pt-24 pb-16 bg-gray-50">
    <div class="container mx-auto px-4 max-w-6xl">
        <div class="flex justify-between items-center mb-8">
            <h1 class="text-3xl font-bold text-gray-800">Refund Management</h1>
            <a href="/admin/dashboard" class="flex items-center text-blue-600 hover:text-blue-800 transition">
                <i class="fas fa-arrow-left mr-2"></i> Back to Dashboard
            </a>
        </div>
        
        <div class="bg-white shadow-md rounded-lg p-6 mb-8 border border-gray-100">
            <h2 class="text-xl font-semibold mb-6 text-gray-800 border-b pb-3">Search Order for Refund</h2>
            <form action="/admin/refund-management/search" method="POST" class="grid gap-4 md:flex md:items-center">
              <div class="flex-grow">
                <label for="orderId" class="block text-sm font-medium text-gray-700 mb-2">Order ID</label>
                <div class="flex flex-col md:flex-row md:items-center gap-4">
                  <div class="flex-grow">
                    <input type="text" id="orderId" name="orderId" required
                      class="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition"
                      placeholder="Enter order ID" value="<%= searchQuery || '' %>">
                    <p class="mt-1 text-sm text-gray-500">Enter the complete order ID to search</p>
                  </div>
                  <button type="submit"
                    class="w-full md:w-auto flex items-center justify-center px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition shadow-sm self-start mt-0">
                    <i class="fas fa-search mr-2"></i> Search
                  </button>
                </div>
              </div>
            </form>
          </div>
          
        
        <% if (searchResults && searchResults._id) { %>
            <div class="bg-white shadow-md rounded-lg overflow-hidden border border-gray-100">
                <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
                    <h2 class="text-xl font-semibold text-gray-800">Order #<%= searchResults._id.toString().substring(0, 8).toUpperCase() %></h2>
                </div>
                
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
                        <!-- Order Information Card -->
                        <div class="bg-blue-50 rounded-lg p-5 border border-blue-100 shadow-sm relative overflow-hidden">
                            <!-- Blue highlight corner triangle -->
                            <div class="absolute top-0 right-0 w-16 h-16 overflow-hidden">
                                <div class="absolute transform rotate-45 bg-blue-500 text-white font-bold py-1 right-[-35px] top-[32px] w-[170px] text-center shadow-md">
                                    Order Info
                                </div>
                            </div>
                            <h3 class="text-lg font-medium mb-4 text-blue-800 border-b border-blue-200 pb-2">
                                <i class="fas fa-info-circle mr-2"></i> Order Information
                            </h3>
                            <div class="space-y-3">
                                <p><span class="font-semibold text-gray-700">Order ID:</span> <span class="font-mono"><%= searchResults._id %></span></p>
                                <p><span class="font-semibold text-gray-700">Order Date:</span> <%= new Date(searchResults.createdAt).toLocaleString() %></p>
                                <p>
                                    <span class="font-semibold text-gray-700">Status:</span> 
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= searchResults.status === 'completed' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' %>">
                                        <%= searchResults.status %>
                                    </span>
                                </p>
                                <p><span class="font-semibold text-gray-700">Total Amount:</span> <span class="text-lg font-semibold">₹<%= searchResults.totalAmount.toFixed(2) %></span></p>
                            </div>
                        </div>

                        <!-- Customer Information Card -->
                        <div class="bg-purple-50 rounded-lg p-5 border border-purple-100 shadow-sm">
                            <h3 class="text-lg font-medium mb-4 text-purple-800 border-b border-purple-200 pb-2">
                                <i class="fas fa-user mr-2"></i> Customer Information
                            </h3>
                            <div class="space-y-3">
                                <% if (searchResults.customer) { %>
                                    <p><span class="font-semibold text-gray-700">Name:</span> <%= searchResults.customer.name %></p>
                                    <p><span class="font-semibold text-gray-700">Email:</span> <%= searchResults.customer.email %></p>
                                    <p><span class="font-semibold text-gray-700">Phone:</span> <%= searchResults.customer.phone %></p>
                                <% } else if (searchResults.customerName) { %>
                                    <p><span class="font-semibold text-gray-700">Name:</span> <%= searchResults.customerName %></p>
                                    <p><span class="font-semibold text-gray-700">Email:</span> <%= searchResults.email || 'N/A' %></p>
                                    <p><span class="font-semibold text-gray-700">Phone:</span> <%= searchResults.phone || 'N/A' %></p>
                                <% } else { %>
                                    <p class="text-gray-600 italic">No customer information available</p>
                                <% } %>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Payment Information Card -->
                    <div class="bg-indigo-50 rounded-lg p-5 border border-indigo-100 shadow-sm mb-8">
                        <h3 class="text-lg font-medium mb-4 text-indigo-800 border-b border-indigo-200 pb-2">
                            <i class="fas fa-credit-card mr-2"></i> Payment Information
                        </h3>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                                <span class="font-semibold text-gray-700 block">Payment Method:</span>
                                <span class="inline-flex items-center mt-1">
                                    <% if (searchResults.paymentMethod === 'card') { %>
                                        <i class="fas fa-credit-card mr-2 text-indigo-600"></i> Credit/Debit Card
                                    <% } else if (searchResults.paymentMethod === 'cash') { %>
                                        <i class="fas fa-money-bill-wave mr-2 text-green-600"></i> Cash on Service
                                    <% } else { %>
                                        <%= searchResults.paymentMethod %>
                                    <% } %>
                                </span>
                            </div>
                            <div>
                                <span class="font-semibold text-gray-700 block">Payment Status:</span>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium mt-1 <%= searchResults.paymentStatus === 'paid' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' %>">
                                    <%= searchResults.paymentStatus %>
                                </span>
                            </div>
                            <% if (searchResults.paymentDetails && searchResults.paymentDetails.paymentId) { %>
                                <div>
                                    <span class="font-semibold text-gray-700 block">Payment ID:</span>
                                    <span class="font-mono text-sm mt-1 block"><%= searchResults.paymentDetails.paymentId %></span>
                                </div>
                            <% } %>
                        </div>
                    </div>
                    
                    <!-- Refund Section -->
                    <% if (searchResults.paymentStatus === 'paid' && !searchResults.refunded) { %>
                        <div class="border-t border-gray-200 pt-6">
                            <h3 class="text-xl font-medium mb-4 text-gray-800">Process Refund</h3>
                            <form action="/admin/refund-management/process" method="POST" class="space-y-6 bg-gray-50 p-6 rounded-lg border border-gray-200">
                                <input type="hidden" name="orderId" value="<%= searchResults._id %>">
                                
                                <div>
                                    <label for="refundAmount" class="block text-sm font-medium text-gray-700 mb-2">Refund Amount (₹)</label>
                                    <div class="mt-1 relative rounded-md shadow-sm">
                                        <span class="absolute inset-y-0 left-0 pl-3 flex items-center text-gray-500 text-lg">₹</span>
                                        <input type="number" id="refundAmount" name="refundAmount" required step="0.01" min="0" max="<%= searchResults.totalAmount %>"
                                            class="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500"
                                            value="<%= searchResults.totalAmount %>">
                                    </div>
                                    <p class="mt-2 text-sm text-gray-500 flex items-center">
                                        <i class="fas fa-info-circle mr-1"></i>
                                        Maximum refund amount: ₹<%= searchResults.totalAmount.toFixed(2) %>
                                    </p>
                                </div>
                                
                                <div>
                                    <label for="refundReason" class="block text-sm font-medium text-gray-700 mb-2">Refund Reason</label>
                                    <textarea id="refundReason" name="refundReason" rows="3" required
                                            class="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500"
                                            placeholder="Please explain the reason for this refund..."></textarea>
                                    <p class="mt-2 text-sm text-gray-500">This will be included in the refund confirmation email sent to the customer.</p>
                                </div>
                                
                                <div class="flex items-center justify-end pt-2">
                                    <button type="button" onclick="window.history.back()" class="px-4 py-2 bg-gray-200 text-gray-700 font-medium rounded-lg hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition mr-3">
                                        Cancel
                                    </button>
                                    <button type="submit" class="flex items-center px-5 py-2 bg-red-600 text-white font-medium rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition shadow-sm">
                                        <i class="fas fa-undo-alt mr-2"></i> Process Refund
                                    </button>
                                </div>
                            </form>
                        </div>
                    <% } else if (searchResults.refunded) { %>
                        <div class="border-t border-gray-200 pt-6">
                            <div class="bg-gray-50 border border-gray-200 text-gray-700 p-6 rounded-lg shadow-sm">
                                <div class="flex items-start">
                                    <div class="flex-shrink-0">
                                        <i class="fas fa-check-circle text-green-500 text-xl"></i>
                                    </div>
                                    <div class="ml-3">
                                        <h3 class="text-lg font-semibold text-gray-900 mb-2">This order has already been refunded</h3>
                                        <% if (searchResults.refundDetails) { %>
                                            <div class="bg-white p-4 rounded-lg border border-gray-200 mt-4 space-y-2">
                                                <p><span class="font-semibold text-gray-700">Refund ID:</span> <span class="font-mono"><%= searchResults.refundDetails.refundId || 'N/A' %></span></p>
                                                <p><span class="font-semibold text-gray-700">Refund Amount:</span> <span class="text-lg font-semibold text-red-700">₹<%= searchResults.refundDetails.amount.toFixed(2) %></span></p>
                                                <p><span class="font-semibold text-gray-700">Refund Date:</span> <%= new Date(searchResults.refundDetails.date).toLocaleString() %></p>
                                                <p><span class="font-semibold text-gray-700">Refund Reason:</span></p>
                                                <div class="bg-gray-50 p-3 rounded-lg text-gray-700">
                                                    <%= searchResults.refundDetails.reason %>
                                                </div>
                                            </div>
                                        <% } %>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <% } else { %>
                        <div class="border-t border-gray-200 pt-6">
                            <div class="bg-yellow-50 border border-yellow-200 text-yellow-800 p-6 rounded-lg shadow-sm">
                                <div class="flex items-start">
                                    <div class="flex-shrink-0">
                                        <i class="fas fa-exclamation-triangle text-yellow-500 text-xl"></i>
                                    </div>
                                    <div class="ml-3">
                                        <h3 class="text-lg font-semibold text-yellow-800 mb-2">Refund not available</h3>
                                        <p>This order is not eligible for refund because payment has not been completed.</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <% } %>
                </div>
            </div>
        <% } else if (searchQuery) { %>
            <div class="bg-yellow-50 border-l-4 border-yellow-400 text-yellow-800 p-4 rounded-md shadow-sm flex items-start">
                <i class="fas fa-search text-yellow-500 mr-3 text-lg mt-1"></i>
                <div>
                    <h3 class="font-medium">No order found</h3>
                    <p class="mt-1">No order found with the ID: <span class="font-mono font-medium"><%= searchQuery %></span></p>
                    <p class="mt-1 text-sm">Please check the Order ID and try again.</p>
                </div>
            </div>
        <% } %>
    </div>
</div>

<%- include('../partials/footer') %> 