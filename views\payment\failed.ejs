<%- include('../partials/header.ejs') %>

<!-- Hero Section with Background Image -->
<div class="relative min-h-screen bg-gradient-to-br from-slate-50 to-red-50 overflow-hidden py-16">
  <!-- Background Elements -->
  <div class="absolute inset-0 z-0">
    <img src="/image7.jpg" alt="Failed Background" class="w-full h-full object-cover opacity-10">
  </div>
  <div class="absolute top-0 right-0 w-1/2 h-64 bg-red-300 opacity-5 rounded-bl-full transform rotate-12"></div>
  <div class="absolute bottom-0 left-0 w-1/2 h-64 bg-slate-400 opacity-5 rounded-tr-full transform -rotate-12"></div>
  
  <div class="container mx-auto px-4 max-w-4xl">
    <div class="bg-white rounded-xl shadow-xl overflow-hidden relative z-10 border border-gray-100">
    
      <!-- Failed Header -->
      <div class="bg-gradient-to-r from-red-500 to-red-600 p-8 text-white text-center relative overflow-hidden">
        <div class="absolute inset-0 opacity-10">
          <svg viewBox="0 0 100 100" preserveAspectRatio="xMidYMid slice" xmlns="http://www.w3.org/2000/svg">
            <path d="M0,0 L100,0 L100,100 L0,100 Z" fill="none" stroke="#fff" stroke-width="0.5"></path>
            <path d="M25,25 L75,75 M75,25 L25,75" stroke="#fff" stroke-width="0.5"></path>
          </svg>
        </div>
        
        <div class="relative z-10 mb-6">
          <!-- Failed Icon -->
          <div class="w-24 h-24 mx-auto bg-white rounded-full flex items-center justify-center mb-4 shadow-lg">
            <i class="fas fa-exclamation-circle text-red-500 text-5xl"></i>
          </div>
          <h1 class="text-4xl font-bold mb-4">Payment Failed</h1>
          <p class="text-xl text-red-100">We encountered an issue with your payment</p>
        </div>
      </div>
      
      <!-- Error Details -->
      <div class="p-4 sm:p-8">
        <div class="bg-slate-50 rounded-xl border border-slate-200 p-4 sm:p-6 mb-6 sm:mb-8">
          <h2 class="text-lg sm:text-xl font-bold text-slate-800 mb-3 sm:mb-4 flex items-center">
            <i class="fas fa-info-circle text-red-500 mr-2"></i> What Happened?
          </h2>
          
          <p class="text-slate-600 mb-4 sm:mb-6">Your payment could not be processed due to one of the following reasons:</p>
          
          <div class="space-y-3 mb-6">
            <div class="flex items-start">
              <div class="flex-shrink-0 w-5 h-5 sm:w-6 sm:h-6 rounded-full bg-red-100 flex items-center justify-center mr-2 sm:mr-3">
                <i class="fas fa-credit-card text-red-500 text-xs"></i>
              </div>
              <p class="text-slate-600 text-sm sm:text-base">Your card was declined by your bank</p>
            </div>
            <div class="flex items-start">
              <div class="flex-shrink-0 w-5 h-5 sm:w-6 sm:h-6 rounded-full bg-red-100 flex items-center justify-center mr-2 sm:mr-3">
                <i class="fas fa-exclamation-triangle text-red-500 text-xs"></i>
              </div>
              <p class="text-slate-600 text-sm sm:text-base">Insufficient funds in your account</p>
            </div>
            <div class="flex items-start">
              <div class="flex-shrink-0 w-5 h-5 sm:w-6 sm:h-6 rounded-full bg-red-100 flex items-center justify-center mr-2 sm:mr-3">
                <i class="fas fa-shield-alt text-red-500 text-xs"></i>
              </div>
              <p class="text-slate-600 text-sm sm:text-base">Security features on your card prevented the transaction</p>
            </div>
            <div class="flex items-start">
              <div class="flex-shrink-0 w-5 h-5 sm:w-6 sm:h-6 rounded-full bg-red-100 flex items-center justify-center mr-2 sm:mr-3">
                <i class="fas fa-network-wired text-red-500 text-xs"></i>
              </div>
              <p class="text-slate-600 text-sm sm:text-base">A technical issue occurred during the payment process</p>
            </div>
          </div>
          
          <div class="bg-white p-3 sm:p-5 rounded-lg border border-slate-200 mb-4">
            <h3 class="font-semibold text-slate-800 mb-2">Error Details</h3>
            <p class="text-slate-600 text-sm">
              <%= errorMessage || 'Your payment could not be processed. Please try again or use a different payment method.' %>
            </p>
          </div>
          
          <p class="text-slate-600 text-sm">If you continue to experience issues, please contact our support team for assistance.</p>
        </div>
        
        <!-- What to Do Next -->
        <div class="flex flex-col sm:flex-row gap-6 sm:gap-8">
          <div class="w-full sm:w-2/3">
            <h2 class="text-lg sm:text-xl font-bold text-slate-800 mb-4">What to Do Next</h2>
            
            <div class="space-y-4 sm:space-y-6">
              <div class="flex">
                <div class="flex-shrink-0 w-8 h-8 sm:w-10 sm:h-10 rounded-full bg-slate-100 flex items-center justify-center mr-3 sm:mr-4">
                  <span class="text-slate-600 font-semibold text-sm sm:text-base">1</span>
                </div>
                <div>
                  <h3 class="font-semibold text-slate-800 mb-1">Try Again</h3>
                  <p class="text-slate-600 text-sm">Return to the payment page and try the transaction again. Sometimes temporary issues can be resolved with a second attempt.</p>
                </div>
              </div>
              
              <div class="flex">
                <div class="flex-shrink-0 w-8 h-8 sm:w-10 sm:h-10 rounded-full bg-slate-100 flex items-center justify-center mr-3 sm:mr-4">
                  <span class="text-slate-600 font-semibold text-sm sm:text-base">2</span>
                </div>
                <div>
                  <h3 class="font-semibold text-slate-800 mb-1">Use a Different Payment Method</h3>
                  <p class="text-slate-600 text-sm">If you have another credit card or payment option, you may want to try using that instead.</p>
                </div>
              </div>
              
              <div class="flex">
                <div class="flex-shrink-0 w-8 h-8 sm:w-10 sm:h-10 rounded-full bg-slate-100 flex items-center justify-center mr-3 sm:mr-4">
                  <span class="text-slate-600 font-semibold text-sm sm:text-base">3</span>
                </div>
                <div>
                  <h3 class="font-semibold text-slate-800 mb-1">Contact Your Bank</h3>
                  <p class="text-slate-600 text-sm">If your card was declined, you may need to contact your bank to authorize the transaction or check your account status.</p>
                </div>
              </div>
              
              <div class="flex">
                <div class="flex-shrink-0 w-8 h-8 sm:w-10 sm:h-10 rounded-full bg-slate-100 flex items-center justify-center mr-3 sm:mr-4">
                  <span class="text-slate-600 font-semibold text-sm sm:text-base">4</span>
                </div>
                <div>
                  <h3 class="font-semibold text-slate-800 mb-1">Contact Our Support Team</h3>
                  <p class="text-slate-600 text-sm">If you continue to experience issues, our customer service team is ready to assist you.</p>
                </div>
              </div>
            </div>
          </div>
          
          <div class="w-full sm:w-1/3 bg-slate-50 rounded-xl border border-slate-200 overflow-hidden">
            <div class="aspect-w-3 aspect-h-2 w-full">
              <% 
                // Get service image from either the populated service or servicesData
                let serviceImage = '/images/team.jpg'; // Default fallback image
                
                // Check if we have a populated service object with an image
                if (typeof order.service === 'object' && order.service !== null && order.service.image) {
                  serviceImage = order.service.image;
                }
                // Check if we have serviceData with image from the booking process
                else if (order.servicesData && order.servicesData.length > 0 && order.servicesData[0].image) {
                  serviceImage = order.servicesData[0].image;
                }
              %>
              <img src="<%= serviceImage %>" alt="Service Image" class="object-cover w-full h-full">
            </div>
            <div class="p-4 sm:p-6">
              <h3 class="text-lg font-bold text-slate-800 mb-3 sm:mb-4">Need Help?</h3>
              <p class="text-slate-600 text-sm mb-4 sm:mb-6">Our customer service team is available to help you resolve any payment issues.</p>
              
              <div class="space-y-3 sm:space-y-4">
                <a href="/contact" class="flex items-center text-slate-600 hover:text-slate-800">
                  <i class="fas fa-envelope text-orange-500 mr-3"></i>
                  <span>Contact Support</span>
                </a>
                <a href="tel:+971569257614" class="flex items-center text-slate-600 hover:text-slate-800">
                  <i class="fas fa-phone text-orange-500 mr-3"></i>
                  <span>+971 56 925 7614</span>
                </a>
                <a href="#" class="flex items-center text-slate-600 hover:text-slate-800">
                  <i class="fas fa-comment-dots text-orange-500 mr-3"></i>
                  <span>Live Chat</span>
                </a>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Actions -->
        <div class="flex flex-col sm:flex-row gap-3 mt-6 sm:mt-8 pt-4 border-t border-slate-200">
          <a href="/" class="flex-1 bg-white py-3 px-4 rounded-lg border border-slate-300 text-center text-slate-800 font-medium hover:bg-slate-50 transition-colors">
            <i class="fas fa-home mr-2"></i> Return to Home
          </a>
          <a href="javascript:history.back()" class="flex-1 bg-white py-3 px-4 rounded-lg border border-slate-300 text-center text-slate-800 font-medium hover:bg-slate-50 transition-colors">
            <i class="fas fa-arrow-left mr-2"></i> Go Back
          </a>
          <a href="/booking" class="flex-1 bg-gradient-to-r from-orange-500 to-orange-600 py-3 px-4 rounded-lg text-center text-white font-medium hover:from-orange-600 hover:to-orange-700 transition-colors">
            <i class="fas fa-sync-alt mr-2"></i> Try Again
          </a>
        </div>
      </div>
    </div>
    
    <!-- Alternative Payment Methods -->
    <div class="mt-8 sm:mt-12 bg-white rounded-xl p-4 sm:p-8 shadow-sm border border-gray-100 relative overflow-hidden">
      <div class="absolute top-0 right-0 w-40 h-40 bg-orange-100 rounded-full transform translate-x-16 -translate-y-16 opacity-50"></div>
      <div class="relative z-10">
        <h2 class="text-lg sm:text-xl font-bold text-slate-800 mb-4 sm:mb-6 flex items-center">
          <i class="fas fa-info-circle text-orange-500 mr-2"></i> Alternative Payment Options
        </h2>
        
        <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 sm:gap-6">
          <div class="bg-slate-50 p-4 sm:p-5 rounded-lg border border-slate-200">
            <div class="w-10 h-10 sm:w-12 sm:h-12 rounded-full bg-orange-100 flex items-center justify-center mb-3 sm:mb-4">
              <i class="fas fa-wallet text-orange-500 text-lg sm:text-xl"></i>
            </div>
            <h3 class="font-semibold text-slate-800 mb-2">Cash on Delivery</h3>
            <p class="text-slate-600 text-sm">Pay in cash when our team arrives for the service.</p>
          </div>
          
          <div class="bg-slate-50 p-4 sm:p-5 rounded-lg border border-slate-200">
            <div class="w-10 h-10 sm:w-12 sm:h-12 rounded-full bg-orange-100 flex items-center justify-center mb-3 sm:mb-4">
              <i class="fas fa-credit-card text-orange-500 text-lg sm:text-xl"></i>
            </div>
            <h3 class="font-semibold text-slate-800 mb-2">Different Card</h3>
            <p class="text-slate-600 text-sm">Try using an alternative credit or debit card for your payment.</p>
          </div>
          
          <div class="bg-slate-50 p-4 sm:p-5 rounded-lg border border-slate-200">
            <div class="w-10 h-10 sm:w-12 sm:h-12 rounded-full bg-orange-100 flex items-center justify-center mb-3 sm:mb-4">
              <i class="fas fa-money-bill-wave text-orange-500 text-lg sm:text-xl"></i>
            </div>
            <h3 class="font-semibold text-slate-800 mb-2">Bank Transfer</h3>
            <p class="text-slate-600 text-sm">Contact us for details on how to pay via direct bank transfer.</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<%- include('../partials/footer.ejs') %>

<script>
  // Retrieve and display payment error from localStorage if available
  document.addEventListener('DOMContentLoaded', function() {
    // Clear any stored errors since we don't want to show them anymore
    localStorage.removeItem('paymentError');
  });
</script> 