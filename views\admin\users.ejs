<%- include('../partials/header') %>

<div class="pt-24 pb-16 bg-gray-50">
    <div class="container mx-auto px-4">
        
        <!-- Page Header -->
        <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
            <div>
                <h1 class="text-2xl font-bold text-gray-800">Manage Users</h1>
                <p class="text-gray-600">View, edit, and manage admin account</p>
            </div>
            <div class="mt-4 md:mt-0">
                <a href="/admin/dashboard" class="text-blue-600 hover:text-blue-800">
                    <i class="fas fa-arrow-left mr-1"></i> Back to Dashboard
                </a>
            </div>
        </div>
        
        <!-- Admin Actions -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
           
        </div>
        
        <!-- Users Table -->
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <div class="flex justify-between items-center p-6 border-b">
                <h2 class="text-lg font-bold text-gray-800">All Users</h2>
                <div class="relative">
                    <input type="text" id="search-users" placeholder="Search users..." class="w-64 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50 pl-10">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="fas fa-search text-gray-400"></i>
                    </div>
                </div>
            </div>
            
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Name
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Email
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Phone
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Role
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Created
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Actions
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200" id="users-table-body">
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500" colspan="6">
                                Loading users...
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination (if needed) -->
            <div class="px-6 py-4 border-t flex items-center justify-between">
                <div class="text-sm text-gray-700">
                    <span id="pagination-info">Showing all users</span>
                </div>
                <div class="flex-1 flex justify-between sm:justify-end">
                    <button id="prev-page" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                        Previous
                    </button>
                    <button id="next-page" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                        Next
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add/Edit User Modal -->
<div id="user-modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50">
    <div class="bg-white rounded-lg max-w-md w-full overflow-hidden shadow-xl transform transition-all">
        <div class="bg-gray-100 px-6 py-4 flex justify-between items-center">
            <h3 class="text-lg font-bold text-gray-900" id="modal-title">Add New User</h3>
            <button class="text-gray-400 hover:text-gray-500" id="close-modal">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <form id="user-form">
            <div class="px-6 py-4">
                <input type="hidden" id="user-id">
                
                <div class="mb-4">
                    <label for="name" class="block text-sm font-medium text-gray-700 mb-1">Full Name *</label>
                    <input type="text" id="name" name="name" required class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                </div>
                
                <div class="mb-4">
                    <label for="email" class="block text-sm font-medium text-gray-700 mb-1">Email Address *</label>
                    <input type="email" id="email" name="email" required class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                </div>
                
                <div class="mb-4">
                    <label for="phone" class="block text-sm font-medium text-gray-700 mb-1">Phone Number *</label>
                    <input type="tel" id="phone" name="phone" required class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                </div>
                
                <div class="mb-4">
                    <label for="address" class="block text-sm font-medium text-gray-700 mb-1">Address *</label>
                    <textarea id="address" name="address" required class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50" rows="2"></textarea>
                </div>
                
                <div class="mb-4">
                    <label for="role" class="block text-sm font-medium text-gray-700 mb-1">Role *</label>
                    <select id="role" name="role" required class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                        <option value="user">User</option>
                        <option value="admin">Admin</option>
                    </select>
                </div>
                
                <div class="mb-4" id="password-field">
                    <label for="password" class="block text-sm font-medium text-gray-700 mb-1">Password *</label>
                    <input type="password" id="password" name="password" required class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                    <p class="mt-1 text-xs text-gray-500" id="password-hint">Leave blank to keep current password.</p>
                </div>
            </div>
            <div class="bg-gray-50 px-6 py-4 flex justify-end">
                <button type="button" class="bg-gray-500 text-white px-4 py-2 rounded mr-2 hover:bg-gray-600" id="cancel-btn">
                    Cancel
                </button>
                <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700" id="save-btn">
                    Save
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div id="delete-modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50">
    <div class="bg-white rounded-lg max-w-md w-full overflow-hidden shadow-xl transform transition-all">
        <div class="bg-red-100 px-6 py-4 flex justify-between items-center">
            <h3 class="text-lg font-bold text-red-900">Confirm Deletion</h3>
            <button class="text-gray-400 hover:text-gray-500" id="close-delete-modal">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="px-6 py-4">
            <p class="text-gray-700">
                Are you sure you want to delete this user? This action cannot be undone.
            </p>
            <p class="mt-2 font-medium text-gray-900" id="delete-user-name"></p>
        </div>
        <div class="bg-gray-50 px-6 py-4 flex justify-end">
            <button class="bg-gray-500 text-white px-4 py-2 rounded mr-2 hover:bg-gray-600" id="cancel-delete-btn">
                Cancel
            </button>
            <button class="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700" id="confirm-delete-btn">
                Delete User
            </button>
        </div>
    </div>
</div>

<script>
// Current logged-in user ID
const currentUserId = '<%= user.id %>';

let users = [];
let currentPage = 1;
let usersPerPage = 10;
let filteredUsers = [];

// Initialize the page
document.addEventListener('DOMContentLoaded', function() {
    fetchUsers();
    setupEventListeners();
});

// Fetch users from API
async function fetchUsers() {
    try {
        const response = await fetch('/admin/api/users');
        users = await response.json();
        filteredUsers = [...users];
        
        displayUsers();
    } catch (error) {
        console.error('Error fetching users:', error);
        document.getElementById('users-table-body').innerHTML = `
            <tr>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-red-500" colspan="6">
                    Error loading users. Please try again.
                </td>
            </tr>
        `;
    }
}

// Set up event listeners
function setupEventListeners() {
    // Add user button
    document.getElementById('add-user-btn').addEventListener('click', () => {
        openUserModal();
    });
    
    // Close modal buttons
    document.getElementById('close-modal').addEventListener('click', closeUserModal);
    document.getElementById('cancel-btn').addEventListener('click', closeUserModal);
    
    // Close delete modal buttons
    document.getElementById('close-delete-modal').addEventListener('click', closeDeleteModal);
    document.getElementById('cancel-delete-btn').addEventListener('click', closeDeleteModal);
    
    // User form submission
    document.getElementById('user-form').addEventListener('submit', handleUserFormSubmit);
    
    // Search users
    document.getElementById('search-users').addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        
        if (searchTerm) {
            filteredUsers = users.filter(user => 
                user.name.toLowerCase().includes(searchTerm) ||
                user.email.toLowerCase().includes(searchTerm) ||
                user.phone.toLowerCase().includes(searchTerm)
            );
        } else {
            filteredUsers = [...users];
        }
        
        currentPage = 1;
        displayUsers();
    });
    
    // Pagination buttons
    document.getElementById('prev-page').addEventListener('click', () => {
        if (currentPage > 1) {
            currentPage--;
            displayUsers();
        }
    });
    
    document.getElementById('next-page').addEventListener('click', () => {
        const totalPages = Math.ceil(filteredUsers.length / usersPerPage);
        if (currentPage < totalPages) {
            currentPage++;
            displayUsers();
        }
    });
}

// Display users with pagination
function displayUsers() {
    const startIndex = (currentPage - 1) * usersPerPage;
    const endIndex = Math.min(startIndex + usersPerPage, filteredUsers.length);
    const usersToDisplay = filteredUsers.slice(startIndex, endIndex);
    
    const totalPages = Math.ceil(filteredUsers.length / usersPerPage);
    
    // Update pagination info
    document.getElementById('pagination-info').textContent = 
        `Showing ${startIndex + 1} to ${endIndex} of ${filteredUsers.length} users`;
    
    // Update pagination buttons
    document.getElementById('prev-page').disabled = currentPage === 1;
    document.getElementById('next-page').disabled = currentPage === totalPages || totalPages === 0;
    
    // Update table
    const tableBody = document.getElementById('users-table-body');
    
    if (usersToDisplay.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500" colspan="6">
                    No users found.
                </td>
            </tr>
        `;
        return;
    }
    
    tableBody.innerHTML = usersToDisplay.map(user => `
        <tr>
            <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                    <div class="ml-4">
                        <div class="text-sm font-medium text-gray-900">${user.name}</div>
                        <div class="text-sm text-gray-500">${user.email}</div>
                    </div>
                </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-500">${user.phone}</div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${user.role === 'admin' ? 'bg-purple-100 text-purple-800' : 'bg-green-100 text-green-800'}">
                    ${user.role}
                </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                ${new Date(user.createdAt).toLocaleDateString()}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <button class="text-indigo-600 hover:text-indigo-900 mr-3 edit-user" data-id="${user._id}">
                    <i class="fas fa-edit"></i>
                </button>
                ${user._id !== currentUserId ? `
                <button class="text-red-600 hover:text-red-900 delete-user" data-id="${user._id}" data-name="${user.name}">
                    <i class="fas fa-trash"></i>
                </button>
                ` : ''}
            </td>
        </tr>
    `).join('');
    
    // Add event listeners to action buttons
    document.querySelectorAll('.edit-user').forEach(button => {
        button.addEventListener('click', () => editUser(button.dataset.id));
    });
    
    document.querySelectorAll('.delete-user').forEach(button => {
        button.addEventListener('click', () => {
            openDeleteModal(button.dataset.id, button.dataset.name);
        });
    });
}

// Open user modal for adding a new user
function openUserModal(userId = null) {
    const modal = document.getElementById('user-modal');
    const modalTitle = document.getElementById('modal-title');
    const form = document.getElementById('user-form');
    const passwordField = document.getElementById('password');
    const passwordHint = document.getElementById('password-hint');
    
    // Reset form
    form.reset();
    
    if (userId) {
        // Edit existing user
        const user = users.find(u => u._id === userId);
        if (!user) return;
        
        modalTitle.textContent = 'Edit User';
        document.getElementById('user-id').value = user._id;
        document.getElementById('name').value = user.name;
        document.getElementById('email').value = user.email;
        document.getElementById('phone').value = user.phone;
        document.getElementById('address').value = user.address;
        document.getElementById('role').value = user.role;
        
        // Password field is optional when editing
        passwordField.removeAttribute('required');
        passwordHint.classList.remove('hidden');
    } else {
        // Add new user
        modalTitle.textContent = 'Add New User';
        document.getElementById('user-id').value = '';
        
        // Password is required for new users
        passwordField.setAttribute('required', '');
        passwordHint.classList.add('hidden');
    }
    
    modal.classList.remove('hidden');
}

// Close user modal
function closeUserModal() {
    document.getElementById('user-modal').classList.add('hidden');
}

// Handle user form submission
async function handleUserFormSubmit(event) {
    event.preventDefault();
    
    const userId = document.getElementById('user-id').value;
    const isNewUser = !userId;
    
    // Get form data
    const userData = {
        name: document.getElementById('name').value,
        email: document.getElementById('email').value,
        phone: document.getElementById('phone').value,
        address: document.getElementById('address').value,
        role: document.getElementById('role').value
    };
    
    // Only include password if it's provided
    const password = document.getElementById('password').value;
    if (password) {
        userData.password = password;
    }
    
    try {
        let response;
        
        if (isNewUser) {
            // Create new user
            response = await fetch('/admin/api/users', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(userData),
            });
        } else {
            // Update existing user
            response = await fetch(`/admin/api/users/${userId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(userData),
            });
        }
        
        if (response.ok) {
            alert(isNewUser ? 'User created successfully.' : 'User updated successfully.');
            closeUserModal();
            
            // Refresh users
            await fetchUsers();
        } else {
            const error = await response.json();
            alert(error.message || 'Error saving user. Please try again.');
        }
    } catch (error) {
        console.error('Error saving user:', error);
        alert('Error saving user. Please try again.');
    }
}

// Edit user
function editUser(userId) {
    openUserModal(userId);
}

// Open delete confirmation modal
function openDeleteModal(userId, userName) {
    const modal = document.getElementById('delete-modal');
    document.getElementById('delete-user-name').textContent = userName;
    
    // Set up delete button event
    const deleteButton = document.getElementById('confirm-delete-btn');
    deleteButton.dataset.id = userId;
    
    deleteButton.addEventListener('click', handleDeleteUser);
    
    modal.classList.remove('hidden');
}

// Close delete modal
function closeDeleteModal() {
    document.getElementById('delete-modal').classList.add('hidden');
    
    // Remove event listener to prevent duplicates
    const deleteButton = document.getElementById('confirm-delete-btn');
    deleteButton.removeEventListener('click', handleDeleteUser);
}

// Handle user deletion
async function handleDeleteUser() {
    const userId = this.dataset.id;
    
    try {
        const response = await fetch(`/admin/api/users/${userId}`, {
            method: 'DELETE',
        });
        
        if (response.ok) {
            alert('User deleted successfully.');
            closeDeleteModal();
            
            // Refresh users
            await fetchUsers();
        } else {
            const error = await response.json();
            alert(error.message || 'Error deleting user. Please try again.');
        }
    } catch (error) {
        console.error('Error deleting user:', error);
        alert('Error deleting user. Please try again.');
    }
}
</script>

<%- include('../partials/footer') %> 