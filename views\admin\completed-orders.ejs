<%- include('../partials/header') %>

<div class="pt-24 pb-16 bg-gray-50">
    <div class="container mx-auto px-4">
        
        <!-- Page Header -->
        <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
            <div>
                <h1 class="text-2xl font-bold text-gray-800">Completed Orders</h1>
                <p class="text-gray-600">View and manage all completed orders</p>
            </div>
            <div class="mt-4 md:mt-0">
                <a href="/admin/dashboard" class="text-blue-600 hover:text-blue-800">
                    <i class="fas fa-arrow-left mr-1"></i> Back to Dashboard
                </a>
            </div>
        </div>
        
        <!-- Filters -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-lg font-bold text-gray-800 mb-4">Filter Completed Orders</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label for="date-filter" class="block text-sm font-medium text-gray-700 mb-1">Date Range</label>
                    <select id="date-filter" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                        <option value="">All Time</option>
                        <option value="today">Today</option>
                        <option value="week">This Week</option>
                        <option value="month">This Month</option>
                    </select>
                </div>
                
                <div>
                    <label for="payment-method-filter" class="block text-sm font-medium text-gray-700 mb-1">Payment Method</label>
                    <select id="payment-method-filter" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                        <option value="">All Methods</option>
                        <option value="cash">Cash on Service</option>
                        <option value="card">Credit/Debit Card</option>
                        <option value="bank_transfer">Bank Transfer</option>
                    </select>
                </div>
                
                <div>
                    <label for="payment-status-filter" class="block text-sm font-medium text-gray-700 mb-1">Payment Status</label>
                    <select id="payment-status-filter" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                        <option value="">All Statuses</option>
                        <option value="paid">Paid</option>
                        <option value="pending">Pending Payment</option>
                    </select>
                </div>
                
                <div>
                    <label for="search" class="block text-sm font-medium text-gray-700 mb-1">Search</label>
                    <input type="text" id="search" placeholder="Name, Email, Order ID..." class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                </div>
            </div>
            
            <div class="mt-4 flex justify-end">
                <button id="apply-filters" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50">
                    Apply Filters
                </button>
            </div>
        </div>
        
        <!-- Orders Table -->
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <div class="flex justify-between items-center p-6 border-b">
                <h2 class="text-lg font-bold text-gray-800">Completed Orders</h2>
                <div>
                    <select id="sort-orders" class="rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                        <option value="date-desc">Newest First</option>
                        <option value="date-asc">Oldest First</option>
                        <option value="amount-desc">Highest Amount</option>
                        <option value="amount-asc">Lowest Amount</option>
                    </select>
                </div>
            </div>
            
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Order ID
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Customer
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Service
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Date Completed
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Amount
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Payment Method
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Payment Status
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Actions
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200" id="orders-table-body">
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500" colspan="8">
                                Loading completed orders...
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            <div class="px-6 py-4 border-t flex items-center justify-between">
                <div class="text-sm text-gray-700">
                    <span id="pagination-info">Showing 0 to 0 of 0 results</span>
                </div>
                <div class="flex-1 flex justify-between sm:justify-end">
                    <button id="prev-page" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                        Previous
                    </button>
                    <button id="next-page" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                        Next
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Order Details Modal -->
<div id="order-details-modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50">
    <div class="bg-white rounded-lg max-w-2xl w-full overflow-hidden shadow-xl transform transition-all">
        <div class="bg-gray-100 px-6 py-4 flex justify-between items-center">
            <h3 class="text-lg font-bold text-gray-900" id="modal-order-id">Order Details</h3>
            <button class="text-gray-400 hover:text-gray-500" id="close-modal">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="px-6 py-4" id="modal-content">
            <div class="animate-pulse">
                <div class="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
                <div class="h-4 bg-gray-200 rounded w-1/2 mb-4"></div>
                <div class="h-4 bg-gray-200 rounded w-5/6 mb-4"></div>
                <div class="h-4 bg-gray-200 rounded w-2/3 mb-4"></div>
            </div>
        </div>
        <div class="bg-gray-50 px-6 py-4 flex justify-end" id="modal-actions">
            <button class="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600" id="close-modal-btn">
                Close
            </button>
        </div>
    </div>
</div>

<script>
let currentPage = 1;
let totalPages = 1;
let currentOrders = [];
let allOrders = [];

// Initialize the page
document.addEventListener('DOMContentLoaded', function() {
    fetchCompletedOrders();
    setupEventListeners();
});

// Fetch completed orders from API
async function fetchCompletedOrders() {
    try {
        const response = await fetch('/admin/api/completed-orders');
        allOrders = await response.json();
        
        // Apply initial filtering and sorting
        filterAndDisplayOrders();
    } catch (error) {
        console.error('Error fetching completed orders:', error);
        document.getElementById('orders-table-body').innerHTML = `
            <tr>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-red-500" colspan="8">
                    Error loading completed orders. Please try again.
                </td>
            </tr>
        `;
    }
}

// Set up event listeners
function setupEventListeners() {
    // Apply filters button
    document.getElementById('apply-filters').addEventListener('click', filterAndDisplayOrders);
    
    // Sort dropdown
    document.getElementById('sort-orders').addEventListener('change', filterAndDisplayOrders);
    
    // Pagination buttons
    document.getElementById('prev-page').addEventListener('click', () => {
        if (currentPage > 1) {
            currentPage--;
            displayOrders();
        }
    });
    
    document.getElementById('next-page').addEventListener('click', () => {
        if (currentPage < totalPages) {
            currentPage++;
            displayOrders();
        }
    });
    
    // Modal close buttons
    document.getElementById('close-modal').addEventListener('click', closeModal);
    document.getElementById('close-modal-btn').addEventListener('click', closeModal);
}

// Filter and sort orders
function filterAndDisplayOrders() {
    const dateFilter = document.getElementById('date-filter').value;
    const paymentMethodFilter = document.getElementById('payment-method-filter').value;
    const paymentStatusFilter = document.getElementById('payment-status-filter').value;
    const searchTerm = document.getElementById('search').value.toLowerCase();
    const sortOption = document.getElementById('sort-orders').value;
    
    // Filter orders
    currentOrders = allOrders.filter(order => {
        // Date filter
        if (dateFilter) {
            const orderDate = new Date(order.completedAt);
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            
            if (dateFilter === 'today' && orderDate < today) {
                return false;
            } else if (dateFilter === 'week') {
                const weekStart = new Date();
                weekStart.setDate(weekStart.getDate() - weekStart.getDay());
                weekStart.setHours(0, 0, 0, 0);
                if (orderDate < weekStart) {
                    return false;
                }
            } else if (dateFilter === 'month') {
                const monthStart = new Date();
                monthStart.setDate(1);
                monthStart.setHours(0, 0, 0, 0);
                if (orderDate < monthStart) {
                    return false;
                }
            }
        }
        
        // Payment method filter
        if (paymentMethodFilter && order.paymentMethod !== paymentMethodFilter) {
            return false;
        }
        
        // Payment status filter
        if (paymentStatusFilter) {
            const isPaid = order.paymentStatus === 'paid';
            if ((paymentStatusFilter === 'paid' && !isPaid) || 
                (paymentStatusFilter === 'pending' && isPaid)) {
                return false;
            }
        }
        
        // Search filter
        if (searchTerm) {
            const searchFields = [
                order._id,
                order.customerName,
                order.email,
                order.phone,
                order.service
            ];
            
            return searchFields.some(field => 
                field && field.toString().toLowerCase().includes(searchTerm)
            );
        }
        
        return true;
    });
    
    // Sort orders
    currentOrders.sort((a, b) => {
        if (sortOption === 'date-desc') {
            return new Date(b.completedAt) - new Date(a.completedAt);
        } else if (sortOption === 'date-asc') {
            return new Date(a.completedAt) - new Date(b.completedAt);
        } else if (sortOption === 'amount-desc') {
            return b.totalAmount - a.totalAmount;
        } else if (sortOption === 'amount-asc') {
            return a.totalAmount - b.totalAmount;
        }
        return 0;
    });
    
    // Reset to first page and display
    currentPage = 1;
    displayOrders();
}

// Display orders with pagination
function displayOrders() {
    const ordersPerPage = 10;
    const startIndex = (currentPage - 1) * ordersPerPage;
    const endIndex = Math.min(startIndex + ordersPerPage, currentOrders.length);
    const ordersToDisplay = currentOrders.slice(startIndex, endIndex);
    
    totalPages = Math.ceil(currentOrders.length / ordersPerPage);
    
    // Update pagination info
    document.getElementById('pagination-info').textContent = 
        `Showing ${startIndex + 1} to ${endIndex} of ${currentOrders.length} results`;
    
    // Update pagination buttons
    document.getElementById('prev-page').disabled = currentPage === 1;
    document.getElementById('next-page').disabled = currentPage === totalPages;
    
    // Update table
    const tableBody = document.getElementById('orders-table-body');
    
    if (ordersToDisplay.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500" colspan="8">
                    No completed orders found matching the filters.
                </td>
            </tr>
        `;
        return;
    }
    
    tableBody.innerHTML = ordersToDisplay.map(order => `
        <tr>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                ${order._id.substring(0, 8)}...
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                ${order.customerName}<br>
                <span class="text-xs text-gray-400">${order.email}</span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                ${order.service}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                ${new Date(order.completedAt).toLocaleDateString()}<br>
                <span class="text-xs text-gray-400">${new Date(order.completedAt).toLocaleTimeString()}</span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                AED ${order.totalAmount.toFixed(2)}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                ${getPaymentMethodText(order.paymentMethod)}
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                    ${order.paymentStatus === 'paid' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}">
                    ${order.paymentStatus}
                </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                <button class="text-blue-600 hover:text-blue-900 mr-3 view-order" data-id="${order._id}" title="View Details">
                    <i class="fas fa-eye"></i>
                </button>
                ${order.paymentStatus === 'pending' ? `
                <button class="text-green-600 hover:text-green-900 mr-3 mark-paid" data-id="${order._id}" title="Mark as Paid">
                    <i class="fas fa-money-bill-wave"></i>
                </button>
                ` : ''}
                <button class="text-indigo-600 hover:text-indigo-900 send-email" data-id="${order._id}" title="Send Email">
                    <i class="fas fa-envelope"></i>
                </button>
            </td>
        </tr>
    `).join('');
    
    // Add event listeners to action buttons
    document.querySelectorAll('.view-order').forEach(button => {
        button.addEventListener('click', () => viewOrderDetails(button.dataset.id));
    });
    
    document.querySelectorAll('.mark-paid').forEach(button => {
        button.addEventListener('click', () => markOrderAsPaid(button.dataset.id));
    });
    
    document.querySelectorAll('.send-email').forEach(button => {
        button.addEventListener('click', () => sendReminderEmail(button.dataset.id));
    });
}

// View order details
async function viewOrderDetails(orderId) {
    try {
        const response = await fetch(`/admin/api/orders/${orderId}`);
        const order = await response.json();
        
        const modalContent = document.getElementById('modal-content');
        document.getElementById('modal-order-id').textContent = `Order #${orderId.substring(0, 8)}...`;
        
        modalContent.innerHTML = `
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <h4 class="font-medium text-gray-800">Customer Information</h4>
                    <p class="mt-2">
                        <span class="block font-medium">Name:</span>
                        ${order.customerName}
                    </p>
                    <p class="mt-2">
                        <span class="block font-medium">Email:</span>
                        ${order.email}
                    </p>
                    <p class="mt-2">
                        <span class="block font-medium">Phone:</span>
                        ${order.phone}
                    </p>
                    <p class="mt-2">
                        <span class="block font-medium">Address:</span>
                        ${order.address}
                    </p>
                </div>
                
                <div>
                    <h4 class="font-medium text-gray-800">Order Information</h4>
                    <p class="mt-2">
                        <span class="block font-medium">Service:</span>
                        ${order.service}
                    </p>
                    <p class="mt-2">
                        <span class="block font-medium">Date & Time:</span>
                        ${new Date(order.date).toLocaleDateString()} ${new Date(order.date).toLocaleTimeString()}
                    </p>
                    <p class="mt-2">
                        <span class="block font-medium">Completed At:</span>
                        ${new Date(order.completedAt).toLocaleDateString()} ${new Date(order.completedAt).toLocaleTimeString()}
                    </p>
                    <p class="mt-2">
                        <span class="block font-medium">Total Amount:</span>
                        AED ${order.totalAmount.toFixed(2)}
                    </p>
                    <p class="mt-2">
                        <span class="block font-medium">Payment Method:</span>
                        ${getPaymentMethodText(order.paymentMethod)}
                    </p>
                    <p class="mt-2">
                        <span class="block font-medium">Payment Status:</span>
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                            ${order.isPaid ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}">
                            ${order.isPaid ? 'Paid' : 'Pending'}
                        </span>
                    </p>
                    ${order.paymentId ? `
                    <p class="mt-2">
                        <span class="block font-medium">Payment ID:</span>
                        ${order.paymentId}
                    </p>
                    ` : ''}
                </div>
            </div>
            
            ${order.message ? `
            <div class="mt-4">
                <h4 class="font-medium text-gray-800">Additional Notes</h4>
                <p class="mt-2 text-gray-600">${order.message}</p>
            </div>
            ` : ''}
        `;
        
        // Update modal actions
        const modalActions = document.getElementById('modal-actions');
        
        if (!order.isPaid) {
            modalActions.innerHTML = `
                <button class="bg-green-600 text-white px-4 py-2 rounded mr-2 hover:bg-green-700" 
                    id="modal-mark-paid" data-id="${order._id}">
                    Mark as Paid
                </button>
                <button class="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600" id="close-modal-btn">
                    Close
                </button>
            `;
            
            document.getElementById('modal-mark-paid').addEventListener('click', function() {
                markOrderAsPaid(this.dataset.id);
            });
            
            document.getElementById('close-modal-btn').addEventListener('click', closeModal);
        } else {
            modalActions.innerHTML = `
                <button class="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600" id="close-modal-btn">
                    Close
                </button>
            `;
            
            document.getElementById('close-modal-btn').addEventListener('click', closeModal);
        }
        
        // Show modal
        document.getElementById('order-details-modal').classList.remove('hidden');
    } catch (error) {
        console.error('Error fetching order details:', error);
        alert('Error loading order details. Please try again.');
    }
}

// Mark order as paid
async function markOrderAsPaid(orderId) {
    if (!confirm('Are you sure you want to mark this order as paid?')) {
        return;
    }
    
    try {
        const response = await fetch(`/admin/orders/${orderId}/pay`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ status: 'paid' }),
        });
        
        if (response.ok) {
            alert('Order has been marked as paid successfully.');
            closeModal();
            
            // Refresh orders
            await fetchCompletedOrders();
        } else {
            alert('Error updating order status. Please try again.');
        }
    } catch (error) {
        console.error('Error marking order as paid:', error);
        alert('Error updating order status. Please try again.');
    }
}

// Send reminder email
async function sendReminderEmail(orderId) {
    if (!confirm('Are you sure you want to send a reminder email for this order?')) {
        return;
    }
    
    try {
        const response = await fetch(`/admin/api/orders/${orderId}/send-reminder`, {
            method: 'POST',
        });
        
        const result = await response.json();
        
        if (response.ok) {
            alert(`Reminder email has been sent successfully to ${result.email || 'customer'}.`);
        } else {
            // Show more detailed error message
            alert(`Error sending reminder email: ${result.message || 'Unknown error'}. ${result.error || ''}`);
        }
    } catch (error) {
        console.error('Error sending reminder email:', error);
        alert('Error sending reminder email. Please try again or check server logs.');
    }
}

// Close the modal
function closeModal() {
    document.getElementById('order-details-modal').classList.add('hidden');
}

// Helper function to get payment method text
function getPaymentMethodText(method) {
    switch (method) {
        case 'card':
            return 'Credit/Debit Card';
        case 'cash':
            return 'Cash on Service';
        case 'bank_transfer':
            return 'Bank Transfer';
        default:
            return method;
    }
}
</script>

<%- include('../partials/footer') %> 