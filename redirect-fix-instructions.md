# Fix "Page with redirect" Issue - JunkExperts

## Problem Identified
Google Search Console is showing "Page with redirect" errors because your website is accessible through multiple URL variations:

- https://www.junksexpert.com (preferred)
- http://www.junksexpert.com (HTTP version)
- https://junksexpert.com (non-www version)

## Solution: Implement 301 Redirects

### Option 1: Server-Level Redirects (Recommended)

#### For Apache (.htaccess file)
Create or update your `.htaccess` file in the public directory:

```apache
# Force HTTPS and WWW
RewriteEngine On

# Redirect HTTP to HTTPS
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://www.junksexpert.com/$1 [R=301,L]

# Redirect non-www to www
RewriteCond %{HTTP_HOST} ^junksexpert\.com [NC]
RewriteRule ^(.*)$ https://www.junksexpert.com/$1 [R=301,L]

# Ensure trailing slash consistency
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_URI} !(.*)/$
RewriteRule ^(.*)$ https://www.junksexpert.com/$1/ [R=301,L]
```

#### For Nginx
Add to your server configuration:

```nginx
# Redirect HTTP to HTTPS
server {
    listen 80;
    server_name junksexpert.com www.junksexpert.com;
    return 301 https://www.junksexpert.com$request_uri;
}

# Redirect non-www HTTPS to www HTTPS
server {
    listen 443 ssl;
    server_name junksexpert.com;
    return 301 https://www.junksexpert.com$request_uri;
}
```

### Option 2: Application-Level Redirects (Node.js/Express)

Add this middleware to your main app file:

```javascript
// Force HTTPS and WWW redirects
app.use((req, res, next) => {
    const host = req.get('Host');
    const protocol = req.get('X-Forwarded-Proto') || req.protocol;
    
    // Check if we need to redirect
    if (protocol !== 'https' || !host.startsWith('www.')) {
        const redirectUrl = `https://www.junksexpert.com${req.originalUrl}`;
        return res.redirect(301, redirectUrl);
    }
    
    next();
});
```

### Option 3: Cloudflare/CDN Level (If using Cloudflare)

1. Go to Cloudflare Dashboard
2. Navigate to "Rules" → "Page Rules"
3. Create rules:
   - `http://junksexpert.com/*` → `https://www.junksexpert.com/$1` (301 redirect)
   - `https://junksexpert.com/*` → `https://www.junksexpert.com/$1` (301 redirect)

## Immediate Actions Required

1. **Choose one redirect method** based on your hosting setup
2. **Implement the redirects** 
3. **Test all URL variations** to ensure they redirect properly
4. **Wait 24-48 hours** for Google to re-crawl
5. **Check Google Search Console** for resolution

## Testing Your Redirects

After implementation, test these URLs:
- http://junksexpert.com → should redirect to https://www.junksexpert.com
- https://junksexpert.com → should redirect to https://www.junksexpert.com
- http://www.junksexpert.com → should redirect to https://www.junksexpert.com

## Expected Results

✅ All URLs will redirect to https://www.junksexpert.com
✅ Google will see one canonical version
✅ "Page with redirect" errors will be resolved
✅ Better SEO performance and indexing

## Priority: HIGH
This issue is preventing proper indexing and should be fixed immediately.
