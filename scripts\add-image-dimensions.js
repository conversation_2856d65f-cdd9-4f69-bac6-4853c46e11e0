const fs = require('fs');
const path = require('path');
const glob = require('glob');
const cheerio = require('cheerio');

// Path to the image dimensions JSON
const DIMENSIONS_FILE = 'public/image-dimensions.json';

// Check if dimensions file exists
if (!fs.existsSync(DIMENSIONS_FILE)) {
  console.error('Image dimensions file not found. Run optimize-images.js first.');
  process.exit(1);
}

// Load the dimensions data
const dimensions = JSON.parse(fs.readFileSync(DIMENSIONS_FILE, 'utf8'));

// Function to process HTML files
const processFile = (filePath) => {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const $ = cheerio.load(content);
    let modified = false;

    // Find all images without width and height attributes
    $('img').each((i, img) => {
      const $img = $(img);
      const src = $img.attr('src');
      const hasWidth = $img.attr('width') !== undefined;
      const hasHeight = $img.attr('height') !== undefined;
      
      // Skip if both dimensions are already set
      if (hasWidth && hasHeight) return;
      
      // Find the image dimensions
      let imgDimensions = null;
      
      // Try to match the src to our dimensions data
      if (dimensions[src]) {
        imgDimensions = dimensions[src];
      } else {
        // Try with and without leading slash
        const altSrc = src.startsWith('/') ? src.substring(1) : `/${src}`;
        if (dimensions[altSrc]) {
          imgDimensions = dimensions[altSrc];
        }
      }
      
      if (imgDimensions) {
        if (!hasWidth) $img.attr('width', imgDimensions.width);
        if (!hasHeight) $img.attr('height', imgDimensions.height);
        modified = true;
        console.log(`Added dimensions to ${src} in ${filePath}: ${imgDimensions.width}x${imgDimensions.height}`);
      } else {
        console.warn(`Dimensions not found for ${src} in ${filePath}`);
      }
    });
    
    // Update <picture> elements to use WebP with fallback
    $('img:not(.no-picture)').each((i, img) => {
      const $img = $(img);
      const src = $img.attr('src');
      
      // Skip if already in a picture element
      if ($img.parent().is('picture')) return;
      
      // Only process jpg/jpeg/png images
      if (!/\.(jpe?g|png)$/i.test(src)) return;
      
      // Create the webp source path
      const webpSrc = src.replace(/\.(jpe?g|png)$/i, '.webp');
      const webpPath = path.join('public', webpSrc);
      const webpRelativePath = webpSrc.startsWith('/') ? webpSrc.substring(1) : webpSrc;
      
      // Check if the webp version exists
      if (fs.existsSync(webpPath) || fs.existsSync(path.join('public', webpRelativePath))) {
        // Create picture element
        const $picture = $('<picture></picture>');
        const $source = $('<source></source>');
        
        $source.attr('srcset', webpSrc);
        $source.attr('type', 'image/webp');
        
        // Extract img element and wrap it
        const $parent = $img.parent();
        const $clone = $img.clone();
        
        $picture.append($source);
        $picture.append($clone);
        
        $img.replaceWith($picture);
        modified = true;
        console.log(`Added WebP support for ${src} in ${filePath}`);
      }
    });
    
    // Save modified file
    if (modified) {
      fs.writeFileSync(filePath, $.html());
      console.log(`Updated ${filePath}`);
    }
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error);
  }
};

// Find all HTML and EJS files
const files = [...glob.sync('views/**/*.ejs'), ...glob.sync('public/**/*.html')];
console.log(`Found ${files.length} template files to process`);

// Process each file
files.forEach(processFile);

console.log('Done adding image dimensions and WebP support'); 