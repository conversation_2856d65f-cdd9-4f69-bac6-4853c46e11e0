const Service = require('../models/Service');
const Order = require('../models/Order');
const mongoose = require('mongoose');
const { sendContactFormToAdmin } = require('../config/email');

// Home page controller
exports.getHomePage = async (req, res) => {
  try {
    // Check if database is connected - wait for connection if needed
    if (mongoose.connection.readyState !== 1) {
      console.log('Database not connected, waiting for connection...');
      // Wait for up to 5 seconds for connection
      for (let i = 0; i < 5; i++) {
        if (mongoose.connection.readyState === 1) break;
        await new Promise(resolve => setTimeout(resolve, 1000));
        console.log(`Connection attempt ${i+1}, state: ${mongoose.connection.readyState}`);
      }
      
      // If still not connected after waiting, render without data
      if (mongoose.connection.readyState !== 1) {
        console.log('Database connection timed out, rendering without services');
        return res.render('index', {
          title: 'JunkExperts | Premium Junk Removal Services UAE',
          services: []
        });
      }
    }
    
    // Get popular services for the homepage
    const popularServices = await Service.find({ isPopular: true }).limit(3);
    
    // If no popular services are marked, get any services
    const services = popularServices.length > 0 ? 
      popularServices : 
      await Service.find().limit(3);
    
    res.render('index', { 
      title: 'JunkExperts | Premium Junk Removal Services UAE',
      services
    });
  } catch (error) {
    console.error('Error fetching homepage data:', error);
    res.render('index', { 
      title: 'JunkExperts | Premium Junk Removal Services UAE',
      services: []
    });
  }
};

// About page controller
exports.getAboutPage = (req, res) => {
  res.render('about', { 
    title: 'About Us | JunkExperts'
  });
};

// Values page controller
exports.getValuesPage = (req, res) => {
  res.render('values', { 
    title: 'Our Values | JunkExperts'
  });
};

// Contact page controller
exports.getContactPage = (req, res) => {
  res.render('contact', { 
    title: 'Contact Us | JunkExperts'
  });
};

// Mattress Disposal Dubai page controller
exports.getMattressDisposalDubaiPage = (req, res) => {
  res.render('mattress-disposal-dubai', { 
    title: 'Mattress Disposal Dubai | Professional Mattress Removal Service | Same Day Pickup'
  });
};

// Furniture Disposal page controller
exports.getFurnitureDisposalPage = (req, res) => {
  res.render('furniture-disposal', { 
    title: 'Furniture Disposal Dubai & UAE | Professional Furniture Removal Service'
  });
};

// Sofa Disposal page controller
exports.getSofaDisposalPage = (req, res) => {
  res.render('sofa-disposal', { 
    title: 'Sofa Disposal Dubai | Professional Couch Removal Service | Same Day Pickup'
  });
};

// Handle contact form submission
exports.submitContactForm = async (req, res) => {
  try {
    const { name, email, phone, subject, message } = req.body;
    
    // Validate form data
    if (!name || !email || !message) {
      req.session.error = 'All fields are required';
      return res.redirect('/contact');
    }
    
    // Send email notification to admin
    const formData = {
      name,
      email,
      phone: phone || 'Not provided',
      subject: subject || 'General Inquiry',
      message
    };
    
    await sendContactFormToAdmin(formData);
    
    req.session.success = 'Thank you for your message. We will get back to you soon!';
    res.redirect('/contact');
  } catch (error) {
    console.error('Error processing contact form:', error);
    req.session.error = 'Failed to send your message';
    res.redirect('/contact');
  }
};

// Revenue tracker page - only for admin
exports.getRevenueTracker = async (req, res) => {
  try {
    // Only allow admin to access this page
    if (req.session.userRole !== 'admin') {
      req.session.error = 'You do not have permission to access this page';
      return res.redirect('/');
    }
    
    // Get all orders
    const orders = await Order.find();
    
    // Calculate total orders and revenue
    const totalOrders = orders.length;
    
    // Include orders marked as paid either by paymentStatus or isPaid flag
    const paidOrders = orders.filter(order => 
      order.paymentStatus === 'paid' || order.isPaid === true
    );
    
    const totalRevenue = paidOrders.reduce((sum, order) => sum + order.totalAmount, 0);
    
    // Only include orders that are not marked as paid
    const pendingOrders = orders.filter(order => 
      order.paymentStatus !== 'paid' && order.isPaid !== true
    );
    
    const pendingRevenue = pendingOrders.reduce((sum, order) => sum + order.totalAmount, 0);
    
    res.render('admin/revenue-tracker', {
      title: 'Revenue Tracker',
      user: {
        id: req.session.userId,
        name: req.session.userName,
        role: req.session.userRole,
        isAuthenticated: req.session.isAuthenticated
      },
      totalOrders,
      totalRevenue,
      pendingRevenue,
      orders
    });
  } catch (error) {
    console.error('Error loading revenue tracker:', error);
    req.session.error = 'Failed to load revenue data';
    res.redirect('/admin/dashboard');
  }
};

// Cash payments page
exports.getCashPayments = async (req, res) => {
  try {
    // Only allow admin to access this page
    if (req.session.userRole !== 'admin') {
      req.session.error = 'You do not have permission to access this page';
      return res.redirect('/');
    }
    
    // Get cash orders
    const cashOrders = await Order.find({ paymentMethod: 'cash' }).sort({ createdAt: -1 });
    
    res.render('admin/cash-payments', {
      title: 'Cash Payments',
      user: {
        id: req.session.userId,
        name: req.session.userName,
        role: req.session.userRole,
        isAuthenticated: req.session.isAuthenticated
      },
      cashOrders
    });
  } catch (error) {
    console.error('Error loading cash payments:', error);
    req.session.error = 'Failed to load cash payment data';
    res.redirect('/admin/dashboard');
  }
};

// Update cash payment status
exports.updateCashPayment = async (req, res) => {
  try {
    const { id } = req.params;
    const { cashReceived, cashAmount, notes, paymentDate } = req.body;
    
    // Validate data
    if (!id || cashAmount === undefined) {
      req.session.error = 'Invalid update request';
      return res.redirect('/admin/cash-payments');
    }
    
    // Update the order
    const updateData = {
      cashReceived: cashReceived === 'true' || cashReceived === true,
      cashAmount: parseFloat(cashAmount),
      cashReceivedDate: cashReceived === 'true' || cashReceived === true ? 
        (paymentDate ? new Date(paymentDate) : new Date()) : null,
      paymentStatus: cashReceived === 'true' || cashReceived === true ? 'paid' : 'pending'
    };
    
    // Add notes if provided
    if (notes) {
      updateData.paymentNotes = notes;
    }
    
    const updatedOrder = await Order.findByIdAndUpdate(
      id,
      updateData,
      { new: true }
    );
    
    if (!updatedOrder) {
      req.session.error = 'Order not found';
      return res.redirect('/admin/cash-payments');
    }
    
    req.session.success = 'Cash payment updated successfully';
    
    // If request is AJAX, return JSON response
    if (req.xhr || req.headers.accept.indexOf('json') > -1) {
      return res.status(200).json({ 
        message: 'Cash payment updated successfully',
        order: updatedOrder
      });
    }
    
    // Otherwise redirect
    res.redirect('/admin/cash-payments');
  } catch (error) {
    console.error('Error updating cash payment:', error);
    
    // If request is AJAX, return JSON response
    if (req.xhr || req.headers.accept.indexOf('json') > -1) {
      return res.status(500).json({ 
        message: 'Failed to update cash payment',
        error: error.message
      });
    }
    
    req.session.error = 'Failed to update cash payment';
    res.redirect('/admin/cash-payments');
  }
};

// Get cash payments data API
exports.getCashPaymentsData = async (req, res) => {
  try {
    // Get limit from query or default to 10
    const limit = parseInt(req.query.limit) || 10;
    
    // Get all cash payments that have been received
    const cashPayments = await Order.find({ 
      paymentMethod: 'cash',
      cashReceived: true
    })
    .sort({ cashReceivedDate: -1 })
    .limit(limit);
    
    res.status(200).json(cashPayments);
  } catch (error) {
    console.error('Error fetching cash payments data:', error);
    res.status(500).json({ message: 'Failed to fetch cash payments' });
  }
};

// Get booking page
exports.getBooking = async (req, res) => {
  try {
    // Get the service ID from query parameter if it exists
    const serviceId = req.query.service;
    
    // Get all services for the dropdown
    const services = await Service.find().sort({ name: 1 });
    
    // Get the selected service if there's a serviceId
    let selectedService = null;
    if (serviceId) {
      try {
        selectedService = await Service.findById(serviceId);
      } catch (err) {
        console.error('Error finding service by ID:', err);
        // Try to find by name as fallback
        if (mongoose.Types.ObjectId.isValid(serviceId) === false) {
          selectedService = await Service.findOne({ name: serviceId });
        }
      }
    }
    
    res.render('booking', {
      title: 'Book Our Services | JunkExperts',
      services,
      selectedService,
      user: req.session.user || null,
      error: req.session.error || null,
      success: req.session.success || null
    });
    
    // Clear session messages
    req.session.error = null;
    req.session.success = null;
  } catch (error) {
    console.error('Error rendering booking page:', error);
    res.status(500).render('error', {
      title: 'Error',
      error: 'Failed to load booking page'
    });
  }
};