{"name": "junkexpert", "version": "1.0.0", "description": "JunkExperts - Premium Junk Removal Services UAE", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "test": "echo \"Error: no test specified\" && exit 1", "optimize-images": "node scripts/optimize-images.js", "add-image-dimensions": "node scripts/add-image-dimensions.js", "build": "npm run optimize-images && npm run add-image-dimensions", "postbuild": "echo 'Image optimization and dimension updates complete'"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"axios": "^1.8.4", "bcryptjs": "^2.4.3", "body-parser": "^1.20.2", "cheerio": "^1.0.0", "connect-flash": "^0.1.1", "connect-mongo": "^5.1.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "cros": "^1.0.1", "dotenv": "^16.5.0", "ejs": "^3.1.9", "express": "^4.19.1", "express-session": "^1.18.0", "glob": "^11.0.1", "moment": "^2.30.1", "mongoose": "^8.2.3", "multer": "^1.4.5-lts.1", "nodemailer": "^6.10.0", "sharp": "^0.34.1", "sib-api-v3-sdk": "^8.5.0", "stripe": "^17.7.0"}, "devDependencies": {"nodemon": "^3.1.9"}, "engines": {"node": ">=14.0.0"}}