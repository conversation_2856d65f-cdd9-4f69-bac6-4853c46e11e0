<%- include('../partials/header') %>

<div class="pt-24 pb-16 bg-gray-50">
    <div class="container mx-auto px-4">
        
        <!-- Page Header -->
        <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
            <div>
                <h1 class="text-2xl font-bold text-gray-800">Manage Services</h1>
                <p class="text-gray-600">Add, edit, and manage service offerings</p>
            </div>
            <div class="mt-4 md:mt-0">
                <a href="/admin/dashboard" class="text-blue-600 hover:text-blue-800">
                    <i class="fas fa-arrow-left mr-1"></i> Back to Dashboard
                </a>
            </div>
        </div>
        
        <!-- Admin Actions -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <div class="flex flex-col md:flex-row justify-between items-start md:items-center">
                <h2 class="text-lg font-bold text-gray-800 mb-4 md:mb-0">Service Management</h2>
                <button id="add-service-btn" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50">
                    <i class="fas fa-plus mr-2"></i> Add New Service
                </button>
            </div>
        </div>
        
        <!-- Services Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" id="services-grid">
            <div class="col-span-full text-center py-8">
                <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
                <p class="mt-4 text-gray-600">Loading services...</p>
            </div>
        </div>
    </div>
</div>

<!-- Add/Edit Service Modal -->
<div id="service-modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50">
    <div class="bg-white rounded-lg max-w-3xl w-full overflow-hidden shadow-xl transform transition-all max-h-[90vh] flex flex-col">
        <div class="bg-orange-600 px-4 py-3 flex justify-between items-center">
            <h3 class="text-lg font-bold text-white" id="modal-title">Add New Service</h3>
            <button class="text-white hover:text-gray-200" id="close-modal">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <form id="service-form" enctype="multipart/form-data" class="bg-gray-50 overflow-y-auto flex-grow">
            <div class="px-4 py-3">
                <input type="hidden" id="service-id">
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="bg-white p-3 rounded-lg shadow-sm border border-gray-200">
                        <h4 class="text-base font-semibold text-blue-700 mb-2 pb-1 border-b border-gray-200">Basic Information</h4>
                        <div class="mb-3">
                            <label for="name" class="block text-sm font-medium text-gray-700 mb-1">
                                Service Name <span class="text-red-500">*</span>
                            </label>
                            <input type="text" id="name" name="name" required 
                                class="w-full rounded-md border-2 border-blue-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50 bg-white px-3 py-1">
                        </div>
                        
                        <div class="mb-3">
                            <label for="price" class="block text-sm font-medium text-gray-700 mb-1">
                                Price (AED) <span class="text-red-500">*</span>
                            </label>
                            <input type="number" id="price" name="price" min="0" step="0.01" required 
                                class="w-full rounded-md border-2 border-blue-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50 bg-white px-3 py-1">
                        </div>
                        
                        <div class="mb-3">
                            <label for="category" class="block text-sm font-medium text-gray-700 mb-1">
                                Category <span class="text-red-500">*</span>
                            </label>
                            <input type="text" id="category" name="category" required 
                                class="w-full rounded-md border-2 border-blue-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50 bg-white px-3 py-1">
                        </div>
                        
                        <div class="mb-2">
                            <label for="serviceImage" class="block text-sm font-medium text-gray-700 mb-1">
                                Service Image <span class="text-red-500">*</span>
                            </label>
                            <div class="border-2 border-dashed border-blue-300 rounded-md p-2 bg-blue-50">
                                <input type="file" id="serviceImage" name="serviceImage" accept="image/*" 
                                    class="w-full text-sm text-gray-700 file:mr-2 file:py-1 file:px-3 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-blue-100 file:text-blue-700 hover:file:bg-blue-200">
                                <p class="mt-1 text-xs text-red-500">Image must be less than 150KB</p>
                            </div>
                            <input type="hidden" id="image" name="image">
                            <input type="hidden" id="icon" name="icon" value="fas fa-star">
                            <div id="image-preview" class="mt-2 hidden p-1 border border-gray-300 rounded-md bg-white">
                                <img id="preview-img" src="" alt="Preview" class="h-24 object-cover rounded">
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-white p-3 rounded-lg shadow-sm border border-gray-200">
                        <h4 class="text-base font-semibold text-blue-700 mb-2 pb-1 border-b border-gray-200">Service Details</h4>
                        <div class="mb-3">
                            <label for="shortDescription" class="block text-sm font-medium text-gray-700 mb-1">
                                Short Description <span class="text-red-500">*</span>
                            </label>
                            <textarea id="shortDescription" name="shortDescription" required 
                                class="w-full rounded-md border-2 border-blue-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50 bg-white px-3 py-1"
                                rows="2"></textarea>
                        </div>
                        
                        <div class="mb-2">
                            <label for="description" class="block text-sm font-medium text-gray-700 mb-1">
                                Full Description <span class="text-red-500">*</span>
                            </label>
                            <textarea id="description" name="description" required 
                                class="w-full rounded-md border-2 border-blue-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50 bg-white px-3 py-1"
                                rows="5"></textarea>
                        </div>
                    </div>
                </div>
                
                <div class="mt-4 bg-white p-3 rounded-lg shadow-sm border border-gray-200">
                    <h4 class="text-base font-semibold text-blue-700 mb-2 pb-1 border-b border-gray-200">
                        Features <i class="fas fa-list-ul text-blue-500 ml-2"></i>
                    </h4>
                    <div id="features-container" class="max-h-32 overflow-y-auto">
                        <div class="flex mb-2">
                            <input type="text" name="features[]" 
                                class="w-full rounded-md border-2 border-blue-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50 bg-white px-3 py-1">
                            <button type="button" class="ml-2 bg-red-100 text-red-600 px-2 py-1 rounded-md hover:bg-red-200 border border-red-300 remove-feature">
                                Remove
                            </button>
                        </div>
                    </div>
                    <div class="flex items-center mt-2">
                        <button type="button" id="add-feature-btn" class="flex items-center text-white bg-green-600 hover:bg-green-700 px-3 py-1 rounded-md shadow-sm transition duration-150 text-sm">
                            <i class="fas fa-plus mr-1"></i> Add Feature
                        </button>
                        <p class="text-xs text-gray-500 ml-2">Add key features to highlight benefits</p>
                    </div>
                </div>
            </div>
            <div class="bg-gray-100 px-4 py-3 flex justify-end border-t border-gray-200 sticky bottom-0 shadow-md">
                <button type="button" class="bg-gray-500 text-white px-3 py-1 rounded mr-2 hover:bg-gray-600 shadow-sm" id="cancel-btn">
                    Cancel
                </button>
                <button type="submit" class="bg-orange-600 text-white px-5 py-2 rounded hover:bg-blue-700 shadow-sm font-medium" id="save-btn">
                    <i class="fas fa-save mr-1"></i> Save Service
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div id="delete-modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50">
    <div class="bg-white rounded-lg max-w-md w-full overflow-hidden shadow-xl transform transition-all">
        <div class="bg-red-100 px-6 py-4 flex justify-between items-center">
            <h3 class="text-lg font-bold text-red-900">Confirm Deletion</h3>
            <button class="text-gray-400 hover:text-gray-500" id="close-delete-modal">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="px-6 py-4">
            <p class="text-gray-700">
                Are you sure you want to delete this service? This action cannot be undone.
            </p>
            <p class="mt-2 font-medium text-gray-900" id="delete-service-name"></p>
        </div>
        <div class="bg-gray-50 px-6 py-4 flex justify-end">
            <button class="bg-gray-500 text-white px-4 py-2 rounded mr-2 hover:bg-gray-600" id="cancel-delete-btn">
                Cancel
            </button>
            <button class="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700" id="confirm-delete-btn">
                Delete Service
            </button>
        </div>
    </div>
</div>

<script>
let services = [];

// Initialize the page
document.addEventListener('DOMContentLoaded', function() {
    fetchServices();
    setupEventListeners();
});

// Fetch services from API
async function fetchServices() {
    try {
        const response = await fetch('/admin/api/services');
        services = await response.json();
        
        displayServices();
    } catch (error) {
        console.error('Error fetching services:', error);
        document.getElementById('services-grid').innerHTML = `
            <div class="col-span-full text-center py-8">
                <p class="text-red-500">Error loading services. Please try again.</p>
            </div>
        `;
    }
}

// Set up event listeners
function setupEventListeners() {
    // Add service button
    document.getElementById('add-service-btn').addEventListener('click', () => {
        openServiceModal();
    });
    
    // Close modal buttons
    document.getElementById('close-modal').addEventListener('click', closeServiceModal);
    document.getElementById('cancel-btn').addEventListener('click', closeServiceModal);
    
    // Close delete modal buttons
    document.getElementById('close-delete-modal').addEventListener('click', closeDeleteModal);
    document.getElementById('cancel-delete-btn').addEventListener('click', closeDeleteModal);
    
    // Service form submission
    document.getElementById('service-form').addEventListener('submit', handleServiceFormSubmit);
    
    // Add feature button
    document.getElementById('add-feature-btn').addEventListener('click', addFeatureField);
    
    // Image preview
    document.getElementById('serviceImage').addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            // Check file size
            if (file.size > 150 * 1024) { // 150KB
                alert('Image size must be less than 150KB');
                this.value = ''; // Clear the file input
                document.getElementById('image-preview').classList.add('hidden');
                return;
            }
            
            const reader = new FileReader();
            reader.onload = function(e) {
                const preview = document.getElementById('preview-img');
                preview.src = e.target.result;
                document.getElementById('image-preview').classList.remove('hidden');
            };
            reader.readAsDataURL(file);
        } else {
            document.getElementById('image-preview').classList.add('hidden');
        }
    });
    
    // Event delegation for remove feature buttons
    document.getElementById('features-container').addEventListener('click', function(event) {
        if (event.target.classList.contains('remove-feature') || event.target.parentElement.classList.contains('remove-feature')) {
            const button = event.target.classList.contains('remove-feature') ? event.target : event.target.parentElement;
            const featureDiv = button.parentElement;
            
            // Only remove if there's more than one feature field
            if (document.querySelectorAll('#features-container > div').length > 1) {
                featureDiv.remove();
            }
        }
    });
}

// Display services in grid
function displayServices() {
    const servicesGrid = document.getElementById('services-grid');
    
    if (services.length === 0) {
        servicesGrid.innerHTML = `
            <div class="col-span-full text-center py-8">
                <p class="text-gray-500">No services found. Click "Add New Service" to create one.</p>
            </div>
        `;
        return;
    }
    
    servicesGrid.innerHTML = services.map(service => `
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <img src="${service.image}" alt="${service.name}" class="w-full h-48 object-cover">
            
            <div class="p-6">
                <div class="flex justify-between items-start mb-4">
                    <div class="flex items-center">
                        <i class="${service.icon} text-orange-500 mr-2"></i>
                        <h3 class="text-xl font-bold text-gray-800">${service.name}</h3>
                    </div>
                    <div class="text-lg font-bold text-orange-600">AED ${service.price.toFixed(2)}</div>
                </div>
                
                <p class="text-gray-600 mb-4">${service.shortDescription}</p>
                
                <div class="border-t pt-4">
                    <h4 class="font-medium text-gray-800 mb-2">Features:</h4>
                    <ul class="space-y-1">
                        ${service.features.map(feature => `
                            <li class="flex items-start">
                                <i class="fas fa-check-circle text-green-500 mt-1 mr-2"></i>
                                <span class="text-gray-600">${feature}</span>
                            </li>
                        `).join('')}
                    </ul>
                </div>
                
                <div class="flex justify-end mt-6">
                    <button class="text-indigo-600 hover:text-indigo-900 mr-4 edit-service" data-id="${service._id}">
                        <i class="fas fa-edit mr-1"></i> Edit
                    </button>
                    <button class="text-red-600 hover:text-red-900 delete-service" data-id="${service._id}" data-name="${service.name}">
                        <i class="fas fa-trash mr-1"></i> Delete
                    </button>
                </div>
            </div>
        </div>
    `).join('');
    
    // Add event listeners to action buttons
    document.querySelectorAll('.edit-service').forEach(button => {
        button.addEventListener('click', () => editService(button.dataset.id));
    });
    
    document.querySelectorAll('.delete-service').forEach(button => {
        button.addEventListener('click', () => {
            openDeleteModal(button.dataset.id, button.dataset.name);
        });
    });
}

// Open service modal for adding a new service
function openServiceModal(serviceId = null) {
    const modal = document.getElementById('service-modal');
    const modalTitle = document.getElementById('modal-title');
    const form = document.getElementById('service-form');
    const featuresContainer = document.getElementById('features-container');
    const imagePreview = document.getElementById('image-preview');
    const previewImg = document.getElementById('preview-img');
    
    // Reset form
    form.reset();
    imagePreview.classList.add('hidden');
    document.getElementById('icon').value = "fas fa-star"; // Set default icon
    featuresContainer.innerHTML = `
        <div class="flex mb-2">
            <input type="text" name="features[]" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
            <button type="button" class="ml-2 bg-gray-200 text-gray-700 px-2 py-1 rounded hover:bg-gray-300 remove-feature">
                Remove
            </button>
        </div>
    `;
    
    if (serviceId) {
        // Edit existing service
        const service = services.find(s => s._id === serviceId);
        if (!service) return;
        
        modalTitle.textContent = 'Edit Service';
        document.getElementById('service-id').value = service._id;
        document.getElementById('name').value = service.name;
        document.getElementById('price').value = service.price;
        document.getElementById('category').value = service.category;
        document.getElementById('image').value = service.image;
        document.getElementById('icon').value = "fas fa-star"; // Always use star icon
        document.getElementById('shortDescription').value = service.shortDescription;
        document.getElementById('description').value = service.description;
        
        // Show image preview if available
        if (service.image) {
            previewImg.src = service.image;
            imagePreview.classList.remove('hidden');
        }
        
        // Add feature fields
        featuresContainer.innerHTML = '';
        service.features.forEach(feature => {
            addFeatureField(feature);
        });
    } else {
        // Add new service
        modalTitle.textContent = 'Add New Service';
        document.getElementById('service-id').value = '';
        document.getElementById('image').value = '';
    }
    
    modal.classList.remove('hidden');
}

// Close service modal
function closeServiceModal() {
    document.getElementById('service-modal').classList.add('hidden');
}

// Add feature field
function addFeatureField(featureValue = '') {
    const featuresContainer = document.getElementById('features-container');
    
    const featureDiv = document.createElement('div');
    featureDiv.className = 'flex mb-2';
    featureDiv.innerHTML = `
        <input type="text" name="features[]" value="${featureValue}" 
            class="w-full rounded-md border-2 border-blue-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50 bg-white px-3 py-1">
        <button type="button" class="ml-2 bg-red-100 text-red-600 px-2 py-1 rounded-md hover:bg-red-200 border border-red-300 remove-feature">
            Remove
        </button>
    `;
    
    featuresContainer.appendChild(featureDiv);
}

// Handle service form submission
async function handleServiceFormSubmit(event) {
    event.preventDefault();
    
    const serviceId = document.getElementById('service-id').value;
    const isNewService = !serviceId;
    
    // Check image size if a file is selected
    const imageFile = document.getElementById('serviceImage').files[0];
    if (imageFile) {
        if (imageFile.size > 150 * 1024) { // 150KB in bytes
            alert('Image size must be less than 150KB');
            return;
        }
        
        // Convert image to base64 string for storing
        const reader = new FileReader();
        reader.onload = async function(e) {
            const base64Image = e.target.result;
            document.getElementById('image').value = base64Image;
            await submitServiceForm(isNewService, serviceId);
        };
        reader.readAsDataURL(imageFile);
    } else {
        // If no new image is selected, use the existing URL value
        if (!document.getElementById('image').value && isNewService) {
            alert('Please select an image for the service');
            return;
        }
        await submitServiceForm(isNewService, serviceId);
    }
}

// Actually submit the service form
async function submitServiceForm(isNewService, serviceId) {
    // Get form data
    const featureInputs = document.querySelectorAll('input[name="features[]"]');
    const features = Array.from(featureInputs).map(input => input.value).filter(Boolean);
    
    const serviceData = {
        name: document.getElementById('name').value,
        price: parseFloat(document.getElementById('price').value),
        category: document.getElementById('category').value,
        image: document.getElementById('image').value,
        icon: "fas fa-star", // Always use star icon
        shortDescription: document.getElementById('shortDescription').value,
        description: document.getElementById('description').value,
        features
    };
    
    try {
        let response;
        
        if (isNewService) {
            // Create new service
            response = await fetch('/admin/api/services', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(serviceData),
            });
        } else {
            // Update existing service
            response = await fetch(`/admin/api/services/${serviceId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(serviceData),
            });
        }
        
        const result = await response.json();
        
        if (response.ok) {
            alert(isNewService ? 'Service created successfully.' : 'Service updated successfully.');
            closeServiceModal();
            
            // Refresh services
            await fetchServices();
        } else {
            alert(result.message || 'Error saving service. Please try again.');
        }
    } catch (error) {
        console.error('Error saving service:', error);
        alert('Error saving service. Please try again.');
    }
}

// Edit service
function editService(serviceId) {
    openServiceModal(serviceId);
}

// Open delete confirmation modal
function openDeleteModal(serviceId, serviceName) {
    const modal = document.getElementById('delete-modal');
    document.getElementById('delete-service-name').textContent = serviceName;
    
    // Set up delete button event
    const deleteButton = document.getElementById('confirm-delete-btn');
    deleteButton.dataset.id = serviceId;
    
    deleteButton.addEventListener('click', handleDeleteService);
    
    modal.classList.remove('hidden');
}

// Close delete modal
function closeDeleteModal() {
    document.getElementById('delete-modal').classList.add('hidden');
    
    // Remove event listener to prevent duplicates
    const deleteButton = document.getElementById('confirm-delete-btn');
    deleteButton.removeEventListener('click', handleDeleteService);
}

// Handle service deletion
async function handleDeleteService() {
    const serviceId = this.dataset.id;
    
    try {
        const response = await fetch(`/admin/api/services/${serviceId}`, {
            method: 'DELETE',
        });
        
        if (response.ok) {
            alert('Service deleted successfully.');
            closeDeleteModal();
            
            // Refresh services
            await fetchServices();
        } else {
            const error = await response.json();
            alert(error.message || 'Error deleting service. Please try again.');
        }
    } catch (error) {
        console.error('Error deleting service:', error);
        alert('Error deleting service. Please try again.');
    }
}
</script>

<%- include('../partials/footer') %> 