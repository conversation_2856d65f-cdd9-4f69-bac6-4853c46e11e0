const fs = require('fs');
const path = require('path');
const sharp = require('sharp');

// Make sure we have the optimized directories
const dirs = ['public/images/optimized', 'public/optimized'];
dirs.forEach(dir => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
});

// Get all image files from public directory
const getAllImages = (dir) => {
  let results = [];
  const files = fs.readdirSync(dir);
  
  for (const file of files) {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      // Skip the optimized directory to avoid reprocessing
      if (!filePath.includes('optimized')) {
        results = results.concat(getAllImages(filePath));
      }
    } else if (/\.(jpg|jpeg|png|webp)$/i.test(file)) {
      results.push(filePath);
    }
  }
  
  return results;
};

// Optimize each image
const optimizeImages = async () => {
  const imageFiles = getAllImages('public');
  console.log(`Found ${imageFiles.length} images to optimize`);
  
  for (const imagePath of imageFiles) {
    try {
      const fileExt = path.extname(imagePath);
      const fileName = path.basename(imagePath, fileExt);
      const dirName = path.dirname(imagePath);
      const outputDir = dirName.replace('public', 'public').includes('/images') 
        ? 'public/images/optimized' 
        : 'public/optimized';
        
      // Get the dimensions of the original image
      const metadata = await sharp(imagePath).metadata();
      
      // Create webp version (better compression, modern format)
      const outputPathWebP = path.join(outputDir, `${fileName}.webp`);
      
      await sharp(imagePath)
        .resize({ 
          width: Math.min(metadata.width, 1200),
          withoutEnlargement: true
        })
        .webp({ quality: 80 })
        .toFile(outputPathWebP);
        
      // Create optimized jpg version (for fallback)
      const outputPathJpg = path.join(outputDir, `${fileName}${fileExt}`);
      
      await sharp(imagePath)
        .resize({ 
          width: Math.min(metadata.width, 1200),
          withoutEnlargement: true
        })
        .jpeg({ quality: 80, mozjpeg: true })
        .toFile(outputPathJpg);
        
      // Log dimensions for adding to HTML
      console.log(`${fileName}${fileExt}: ${metadata.width}x${metadata.height}`);
      
      const originalSize = fs.statSync(imagePath).size;
      const optimizedSizeJpg = fs.statSync(outputPathJpg).size;
      const optimizedSizeWebP = fs.statSync(outputPathWebP).size;
      
      const savedJpg = ((originalSize - optimizedSizeJpg) / originalSize * 100).toFixed(1);
      const savedWebP = ((originalSize - optimizedSizeWebP) / originalSize * 100).toFixed(1);
      
      console.log(`  Original: ${(originalSize / 1024).toFixed(1)}KB`);
      console.log(`  Optimized JPG: ${(optimizedSizeJpg / 1024).toFixed(1)}KB (saved ${savedJpg}%)`);
      console.log(`  WebP: ${(optimizedSizeWebP / 1024).toFixed(1)}KB (saved ${savedWebP}%)`);
      
    } catch (error) {
      console.error(`Error optimizing ${imagePath}:`, error);
    }
  }
};

// Create a JSON file with image dimensions for reference
const createImageDimensionsJson = async () => {
  const imageFiles = getAllImages('public');
  const dimensions = {};
  
  for (const imagePath of imageFiles) {
    try {
      const relativePath = imagePath.replace('public', '');
      const metadata = await sharp(imagePath).metadata();
      dimensions[relativePath] = {
        width: metadata.width,
        height: metadata.height
      };
    } catch (error) {
      console.error(`Error processing ${imagePath}:`, error);
    }
  }
  
  fs.writeFileSync('public/image-dimensions.json', JSON.stringify(dimensions, null, 2));
  console.log('Created image-dimensions.json with all image sizes');
};

// Run the optimization
(async () => {
  console.log('Starting image optimization...');
  await optimizeImages();
  await createImageDimensionsJson();
  console.log('Image optimization complete!');
})(); 