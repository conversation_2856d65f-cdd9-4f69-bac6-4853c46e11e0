const express = require('express');
const router = express.Router();
const paymentController = require('../controllers/paymentController');
const { isAuthenticated } = require('../middleware/auth');

// Create payment intent (AJAX endpoint)
router.post('/create-payment-intent', paymentController.createPaymentIntent);

// Stripe webhook endpoint (no CSRF protection)
router.post('/webhook', express.raw({ type: 'application/json' }), paymentController.handleWebhook);

// Payment pages
router.get('/stripe/:orderId', paymentController.getPaymentPage);
router.get('/success/:orderId', paymentController.getPaymentSuccessPage);
router.get('/failed/:orderId', paymentController.getPaymentFailedPage);

module.exports = router; 