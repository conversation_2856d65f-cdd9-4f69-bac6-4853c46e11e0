<%- include('../partials/header') %>

<div class="pt-24 pb-16 bg-gray-50">
    <div class="container mx-auto px-4">
        <!-- Page Header -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <div class="flex flex-col md:flex-row justify-between items-start md:items-center">
                <div>
                    <h1 class="text-2xl font-bold text-gray-800">Cash on Delivery Payments</h1>
                    <p class="text-gray-600">Manage and update pending and completed cash payments</p>
                </div>
                <div class="mt-4 md:mt-0">
                    <a href="/admin/cod-email-search" class="bg-orange-500 hover:bg-orange-600 text-white py-2 px-4 rounded-lg inline-flex items-center transition">
                        <i class="fas fa-search mr-2"></i> Search by Email
                    </a>
                </div>
            </div>
        </div>

        <!-- Success Alert -->
        <div id="successAlert" class="hidden mb-6 bg-green-100 border-l-4 border-green-500 text-green-700 p-4 rounded-lg shadow-md">
            <div class="flex items-center">
                <i class="fas fa-check-circle mr-2 text-green-500"></i>
                <span>Payment updated successfully!</span>
                <button type="button" onclick="this.parentElement.parentElement.classList.add('hidden')" class="ml-auto text-green-700 hover:text-green-800">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>

        <!-- Error Alert -->
        <div id="errorAlert" class="hidden mb-6 bg-red-100 border-l-4 border-red-500 text-red-700 p-4 rounded-lg shadow-md">
            <div class="flex items-center">
                <i class="fas fa-exclamation-circle mr-2 text-red-500"></i>
                <span id="errorMessage">An error occurred</span>
                <button type="button" onclick="this.parentElement.parentElement.classList.add('hidden')" class="ml-auto text-red-700 hover:text-red-800">
                    <i class="fas fa-times"></i>
                </button>
    </div>
  </div>

  <!-- Pending COD Payments -->
        <div class="bg-white rounded-lg shadow-md overflow-hidden mb-6">
            <div class="bg-gray-50 border-b border-gray-200 px-6 py-4">
                <h2 class="text-lg font-bold text-gray-800">Pending COD Payments</h2>
        </div>
            <div class="p-4">
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200" id="pendingPaymentsTable">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Order ID</th>
                                <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                                <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Service</th>
                                <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                                <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Scheduled Date</th>
                                <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
                        <tbody class="bg-white divide-y divide-gray-200" id="pendingTableBody">
                            <tr>
                                <td colspan="6" class="px-4 py-4 text-center text-sm text-gray-500">
                                    <div class="flex justify-center items-center">
                                        <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-orange-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                        </svg>
                                        <span>Loading payments...</span>
                                    </div>
                                </td>
                            </tr>
              </tbody>
            </table>
      </div>
    </div>
  </div>

  <!-- Recent COD Payments -->
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <div class="bg-gray-50 border-b border-gray-200 px-6 py-4">
                <h2 class="text-lg font-bold text-gray-800">Recent COD Payments</h2>
        </div>
            <div class="p-4">
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200" id="recentPaymentsTable">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Order ID</th>
                                <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                                <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Service</th>
                                <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                                <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Payment Date</th>
                                <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Collected By</th>
                                <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                </tr>
              </thead>
                        <tbody class="bg-white divide-y divide-gray-200" id="recentTableBody">
                            <tr>
                                <td colspan="7" class="px-4 py-4 text-center text-sm text-gray-500">
                                    <div class="flex justify-center items-center">
                                        <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-orange-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                        </svg>
                                        <span>Loading payments...</span>
                                    </div>
                                </td>
                            </tr>
              </tbody>
            </table>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Payment Row Template -->
<template id="pendingRowTemplate">
    <tr class="hover:bg-gray-50">
        <td class="px-4 py-4 whitespace-nowrap text-sm font-medium text-gray-900"></td>
        <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
            <div class="font-medium customerName"></div>
            <div class="text-xs text-gray-400 customerPhone"></div>
        </td>
        <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500 serviceName"></td>
        <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500 orderAmount"></td>
        <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500 scheduledDate"></td>
        <td class="px-4 py-4 whitespace-nowrap">
            <div class="inline-block relative">
                <button type="button" class="expand-btn bg-orange-500 hover:bg-orange-600 text-white py-1 px-3 rounded text-sm inline-flex items-center">
                    <i class="fas fa-edit mr-1"></i> Update
                </button>
      </div>
        </td>
    </tr>
    <tr class="payment-details bg-gray-50 hidden">
        <td colspan="6" class="px-4 py-0">
            <div class="pb-4 pt-2 px-2">
                <form class="payment-form grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Payment Status</label>
                        <select name="paymentStatus" class="w-full rounded border-gray-300 shadow-sm focus:border-orange-500 focus:ring focus:ring-orange-200">
              <option value="cod_pending">Pending</option>
              <option value="cod_paid">Paid</option>
            </select>
          </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Amount Collected (AED)</label>
                        <input type="number" name="actualAmount" class="w-full rounded border-gray-300 shadow-sm focus:border-orange-500 focus:ring focus:ring-orange-200">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Payment Date</label>
                        <input type="date" name="paymentDate" class="w-full rounded border-gray-300 shadow-sm focus:border-orange-500 focus:ring focus:ring-orange-200">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Payment Notes</label>
                        <textarea name="paymentNotes" rows="1" class="w-full rounded border-gray-300 shadow-sm focus:border-orange-500 focus:ring focus:ring-orange-200"></textarea>
                    </div>
                    <div class="md:col-span-2 flex justify-end space-x-2 mt-2">
                        <button type="button" class="cancel-btn px-3 py-1 bg-gray-200 hover:bg-gray-300 text-gray-800 rounded transition-colors">
                            Cancel
                        </button>
                        <button type="submit" class="save-btn px-3 py-1 bg-green-500 hover:bg-green-600 text-white rounded transition-colors flex items-center">
                            <span>Save</span>
                            <svg class="save-spinner hidden ml-1 animate-spin h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                        </button>
          </div>
        </form>
      </div>
        </td>
    </tr>
</template>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Load payment data on page load
    loadPayments();

    function loadPayments() {
        const pendingTableBody = document.getElementById('pendingTableBody');
        const recentTableBody = document.getElementById('recentTableBody');
        
        // Show loading state
        pendingTableBody.innerHTML = `
            <tr>
                <td colspan="6" class="px-4 py-4 text-center text-sm text-gray-500">
                    <div class="flex justify-center items-center">
                        <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-orange-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <span>Loading payments...</span>
                    </div>
                </td>
            </tr>
        `;
        
        recentTableBody.innerHTML = `
            <tr>
                <td colspan="7" class="px-4 py-4 text-center text-sm text-gray-500">
                    <div class="flex justify-center items-center">
                        <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-orange-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <span>Loading payments...</span>
                    </div>
                </td>
            </tr>
        `;

        // Fetch payment data
        fetch('/admin/api/revenue-analysis')
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            })
            .then(data => {
                // Process the data
                renderPendingPayments(data.pendingCODPayments || []);
                renderRecentPayments(data.recentCODPayments || []);
            })
            .catch(error => {
                console.error('Error loading payment data:', error);
                showError('Failed to load payment data. Please refresh the page.');
                
                pendingTableBody.innerHTML = `
                    <tr>
                        <td colspan="6" class="px-4 py-4 text-center text-sm text-red-500">
                            Failed to load payment data. Please refresh the page.
                        </td>
                    </tr>
                `;
                
                recentTableBody.innerHTML = `
                    <tr>
                        <td colspan="7" class="px-4 py-4 text-center text-sm text-red-500">
                            Failed to load payment data. Please refresh the page.
                        </td>
                    </tr>
                `;
            });
    }

    function renderPendingPayments(payments) {
        const pendingTableBody = document.getElementById('pendingTableBody');
        pendingTableBody.innerHTML = '';
        
        if (payments.length === 0) {
            pendingTableBody.innerHTML = `
                <tr>
                    <td colspan="6" class="px-4 py-4 text-center text-sm text-gray-500">
                        No pending payments found
                    </td>
                </tr>
            `;
            return;
        }
        
        // Get the template
        const template = document.getElementById('pendingRowTemplate');
        
        // For each payment, create a row
        payments.forEach(payment => {
            // Clone the template
            const fragment = template.content.cloneNode(true);
            
            // Set the payment data
            fragment.querySelector('tr:first-child td:nth-child(1)').textContent = payment._id;
            fragment.querySelector('.customerName').textContent = payment.customer?.name || 'N/A';
            fragment.querySelector('.customerPhone').textContent = payment.customer?.phone || 'N/A';
            fragment.querySelector('.serviceName').textContent = payment.service?.name || 'N/A';
            fragment.querySelector('.orderAmount').textContent = `AED ${(payment.amount !== undefined) ? payment.amount.toFixed(2) : '0.00'}`;
            fragment.querySelector('.scheduledDate').textContent = formatDate(payment.scheduledDate);
            
            // Set form default values
            const form = fragment.querySelector('.payment-form');
            const statusSelect = form.querySelector('[name="paymentStatus"]');
            const amountInput = form.querySelector('[name="actualAmount"]');
            const dateInput = form.querySelector('[name="paymentDate"]');
            
            // Set the current values
            statusSelect.value = payment.paymentStatus || 'cod_pending';
            amountInput.value = payment.amount || '';
            dateInput.value = new Date().toISOString().split('T')[0];
            
            // Store the payment ID
            form.dataset.paymentId = payment._id;
            
            // Add event listeners
            const expandBtn = fragment.querySelector('.expand-btn');
            const cancelBtn = fragment.querySelector('.cancel-btn');
            const saveBtn = fragment.querySelector('.save-btn');
            const detailsRow = fragment.querySelector('.payment-details');
            
            expandBtn.addEventListener('click', function() {
                // Toggle details row
                detailsRow.classList.toggle('hidden');
            });
            
            cancelBtn.addEventListener('click', function() {
                // Hide details row
                detailsRow.classList.add('hidden');
            });
            
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                savePayment(form);
            });
            
            // Add the fragment to the table
            pendingTableBody.appendChild(fragment);
        });
    }

    function renderRecentPayments(payments) {
        const recentTableBody = document.getElementById('recentTableBody');
        recentTableBody.innerHTML = '';
        
        if (payments.length === 0) {
            recentTableBody.innerHTML = `
                <tr>
                    <td colspan="7" class="px-4 py-4 text-center text-sm text-gray-500">
                        No recent payments found
                    </td>
                </tr>
            `;
            return;
        }
        
        // For each payment, create a row
        payments.forEach(payment => {
            const statusClass = payment.paymentStatus === 'cod_paid' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800';
            const statusText = payment.paymentStatus === 'cod_paid' ? 'Paid' : 'Pending';
            
            const row = document.createElement('tr');
            row.className = 'hover:bg-gray-50';
            row.innerHTML = `
                <td class="px-4 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${payment._id}</td>
                <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                    <div class="font-medium">${payment.customer?.name || 'N/A'}</div>
                    <div class="text-xs text-gray-400">${payment.customer?.phone || 'N/A'}</div>
                </td>
                <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">${payment.service?.name || 'N/A'}</td>
                <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">AED ${(payment.amount !== undefined) ? payment.amount.toFixed(2) : '0.00'}</td>
                <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">${formatDate(payment.paymentDate)}</td>
                <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">${payment.paymentDetails?.collectedBy?.name || 'N/A'}</td>
                <td class="px-4 py-4 whitespace-nowrap">
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${statusClass}">
                        ${statusText}
                    </span>
                </td>
            `;
            
            recentTableBody.appendChild(row);
        });
    }

    function savePayment(form) {
        // Get form data
        const paymentId = form.dataset.paymentId;
        const saveBtn = form.querySelector('.save-btn');
        const spinner = form.querySelector('.save-spinner');
        
        // Show loading state
        saveBtn.disabled = true;
        saveBtn.querySelector('span').textContent = 'Saving';
        spinner.classList.remove('hidden');
        
        const data = {
            paymentStatus: form.querySelector('[name="paymentStatus"]').value,
            notes: form.querySelector('[name="paymentNotes"]').value,
            paymentDate: form.querySelector('[name="paymentDate"]').value,
            actualAmount: parseFloat(form.querySelector('[name="actualAmount"]').value) || 0
        };
        
        // Send the request
        fetch(`/admin/api/order/${paymentId}/payment`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
            body: JSON.stringify(data)
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(result => {
            if (result.success) {
                // Hide details row
                form.closest('.payment-details').classList.add('hidden');
        
        // Show success message
                showSuccess('Payment updated successfully!');
                
                // Reload payments
                loadPayments();
      } else {
                throw new Error(result.error || 'Failed to update payment');
      }
    })
    .catch(error => {
      console.error('Error updating payment:', error);
            showError(error.message || 'Failed to update payment');
        })
        .finally(() => {
            // Reset form state
            saveBtn.disabled = false;
            saveBtn.querySelector('span').textContent = 'Save';
            spinner.classList.add('hidden');
        });
    }

    function showSuccess(message) {
        const alert = document.getElementById('successAlert');
        if (alert) {
            alert.querySelector('span').textContent = message;
            alert.classList.remove('hidden');
            
            // Hide after 3 seconds
            setTimeout(() => {
                alert.classList.add('hidden');
            }, 3000);
        }
    }

    function showError(message) {
        const alert = document.getElementById('errorAlert');
        if (alert) {
            alert.querySelector('#errorMessage').textContent = message;
            alert.classList.remove('hidden');
        }
    }

    function formatDate(dateString) {
        if (!dateString) return 'N/A';
        try {
            const date = new Date(dateString);
            return date.toLocaleDateString();
        } catch (error) {
            return 'Invalid date';
        }
    }
});
</script>

<%- include('../partials/footer') %> 