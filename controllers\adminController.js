const User = require('../models/User');
const Order = require('../models/Order');
const Service = require('../models/Service');
const bcrypt = require('bcryptjs');
const moment = require('moment');
const mongoose = require('mongoose');

// Get admin dashboard
exports.getDashboard = async (req, res) => {
    try {
        console.log('Rendering admin dashboard with session:', {
            id: req.sessionID,
            isAuthenticated: req.session.isAuthenticated,
            userId: req.session.userId,
            userRole: req.session.userRole
        });
        
        // Add check for admin cookie as backup
        const hasAdminCookie = req.cookies['admin-auth'] === process.env.ADMIN_COOKIE_VALUE;
        
        // Check if admin user exists
        const adminUser = await User.findOne({ role: 'admin' });
        
        if (!adminUser) {
            console.error('Admin user not found in the database');
            return res.status(500).render('error', {
                title: 'Error',
                error: 'Admin user not set up correctly'
            });
        }
        
        // For production troubleshooting - set admin session if needed
        if (process.env.NODE_ENV === 'production' && 
            (hasAdminCookie || req.query.direct === 'true')) {
            console.log('Direct dashboard access - ensuring admin session');
            
            // Set session data if missing
            if (!req.session.isAuthenticated || req.session.userRole !== 'admin') {
                req.session.isAuthenticated = true;
                req.session.userId = adminUser._id.toString();
                req.session.userRole = 'admin';
                req.session.userName = adminUser.name || 'Admin';
                
                // Don't wait for save to complete
                req.session.save(err => {
                    if (err) console.error('Error saving session in direct dashboard access:', err);
                });
            }
        }
        
        // Get stats for dashboard
        const totalOrders = await Order.countDocuments();
        const pendingOrders = await Order.countDocuments({ status: 'pending' });
        const completedOrders = await Order.countDocuments({ status: 'completed' });
        const totalServices = await Service.countDocuments();
        const totalUsers = await User.countDocuments();
        
        console.log('AdminController: Dashboard stats loaded', { 
            totalOrders, pendingOrders, completedOrders 
        });
        
        // Get recent orders
        const recentOrders = await Order.find()
            .sort({ createdAt: -1 })
            .limit(5)
            .lean(); // Use lean for better performance
        
        // Normalize recent orders to ensure consistent format
        const normalizedRecentOrders = recentOrders.map(order => normalizeOrderFields(order));
        
        // Calculate revenue
        const paidOrders = await Order.find({ 
            $or: [
                { paymentStatus: { $in: ['paid', 'cod_paid'] } },
                { isPaid: true }
            ]
        });
        
        // Use normalizeOrderFields to ensure consistent format for amount calculation
        const totalRevenue = paidOrders.reduce((sum, order) => {
            const normalized = normalizeOrderFields(order);
            return sum + normalized.amount;
        }, 0);
        
        // Get services by category
        const services = await Service.find().lean();
        
        // Group services by category
        const servicesByCategory = {};
        services.forEach(service => {
            // Handle case where category might not exist
            if (!service.category) {
                service.category = 'Uncategorized';
            }
            
            if (!servicesByCategory[service.category]) {
                servicesByCategory[service.category] = 0;
            }
            servicesByCategory[service.category]++;
        });
        
        res.render('admin/dashboard', {
            title: 'Admin Dashboard | JunkExperts',
            stats: {
                totalOrders,
                pendingOrders,
                completedOrders,
                totalServices,
                totalUsers,
                totalRevenue
            },
            recentOrders: normalizedRecentOrders,
            servicesByCategory,
            moment: require('moment') // Include moment for date formatting
        });
    } catch (error) {
        console.error('Error rendering admin dashboard:', error);
        res.status(500).render('error', {
            title: 'Error',
            error: 'Failed to load admin dashboard: ' + error.message
        });
    }
};

// Get admin orders page
exports.getOrders = async (req, res) => {
    try {
        res.render('admin/orders', {
            title: 'Manage Orders',
            user: {
                id: req.session.userId,
                name: req.session.userName,
                role: req.session.userRole,
                isAuthenticated: req.session.isAuthenticated
            }
        });
    } catch (error) {
        console.error('Error getting orders page:', error);
        req.session.error = 'Failed to load orders page';
        res.redirect('/admin/dashboard');
    }
};

// Mark order as paid
exports.markOrderAsPaid = async (req, res) => {
    try {
        const { id } = req.params;
        
        const order = await Order.findById(id);
        if (!order) {
            return res.status(404).json({ message: 'Order not found' });
        }
        
        order.isPaid = true;
        order.paidAt = Date.now();
        order.paymentMethod = order.paymentMethod || 'cash';
        order.status = 'confirmed';
        
        await order.save();
        
        res.status(200).json({ message: 'Order marked as paid', order });
    } catch (error) {
        console.error('Error marking order as paid:', error);
        res.status(500).json({ message: 'Failed to update order payment status' });
    }
};

// Send reminder email for order
exports.sendOrderEmail = async (req, res) => {
    try {
        const { id } = req.params;
        const emailService = require('../config/email');
        
        const order = await Order.findById(id)
            .populate('user', 'name email')
            .populate('service', 'name price');
        
        if (!order) {
            return res.status(404).json({ message: 'Order not found' });
        }
        
        // Prepare recipient email and name
        let recipientEmail, recipientName;
        
        // Check if order has an associated user
        if (!order.user) {
            // Try to get email from the order directly
            recipientEmail = order.email;
            recipientName = order.customerName;
            console.log(`No user associated with order. Using order email: ${recipientEmail} (${recipientName})`);
        } else {
            recipientEmail = order.user.email;
            recipientName = order.user.name;
            console.log(`Using user email: ${recipientEmail} (${recipientName})`);
        }
        
        if (!recipientEmail) {
            return res.status(400).json({ 
                message: 'Cannot send email - no recipient email found',
                order: { id: order._id, customer: order.customerName }
            });
        }
        
        // Prepare email content
        const mailOptions = {
            from: `"JunkExperts" <${process.env.EMAIL_FROM || '<EMAIL>'}>`,
            to: recipientEmail,
            subject: `Reminder: Your JunkExperts Order #${order._id}`,
            html: `
                <h1>Reminder About Your Order</h1>
                <p>Dear ${recipientName},</p>
                <p>We're excited to have you as our customer. Our team will be in touch with you shortly before the scheduled service time.</p>
                <p><strong>Order ID:</strong> ${order._id}</p>
                <p><strong>Service:</strong> ${order.service ? order.service.name : order.service}</p>
                <p><strong>Date:</strong> ${new Date(order.date).toLocaleDateString()}</p>
                <p><strong>Address:</strong> ${order.address}</p>
                <p><strong>Total Amount:</strong> AED ${order.totalAmount.toFixed(2)}</p>
                <p><strong>Payment Status:</strong> ${order.paymentStatus || (order.isPaid ? 'Paid' : 'Pending')}</p>
                <p><strong>Status:</strong> ${order.status || 'Processing'}</p>
                <p>If you have any questions, need to reschedule, or want to discuss payment options, please contact us at +971 56 925 7614.</p>
                <p>Thank you for choosing JunkExperts!</p>
            `
        };
        
        console.log(`Attempting to send reminder email to ${recipientEmail} for order ${order._id}`);
        
        // Send the email using nodemailer
        const nodemailer = require('nodemailer');
        const transporter = nodemailer.createTransport({
            host: process.env.BREVO_SMTP_HOST || 'smtp-relay.brevo.com',
            port: process.env.BREVO_SMTP_PORT || 587,
            secure: false,
            auth: {
                user: process.env.BREVO_SMTP_USER || '<EMAIL>',
                pass: process.env.BREVO_SMTP_PASS || 'MGWqtS5g4LRkw1Tz',
            },
            debug: true
        });
        
        // Try to send the email
        try {
            const info = await transporter.sendMail(mailOptions);
            console.log('Reminder email sent successfully:', info.messageId);
            
            // Update the order to record that a reminder was sent
            order.lastReminderSent = Date.now();
            await order.save();
            
            return res.status(200).json({ 
                message: 'Reminder email sent successfully',
                email: recipientEmail,
                messageId: info.messageId
            });
        } catch (emailError) {
            console.error('Error sending reminder email:', emailError);
            return res.status(500).json({ 
                message: 'Failed to send email due to SMTP error',
                error: emailError.message,
                recipient: recipientEmail 
            });
        }
    } catch (error) {
        console.error('Error in sendOrderEmail function:', error);
        res.status(500).json({ 
            message: 'Failed to process reminder email request', 
            error: error.message 
        });
    }
};

// Get users page
exports.getUsers = async (req, res) => {
    try {
        res.render('admin/users', {
            title: 'Manage Users',
            user: {
                id: req.session.userId,
                name: req.session.userName,
                role: req.session.userRole,
                isAuthenticated: req.session.isAuthenticated
            }
        });
    } catch (error) {
        console.error('Error getting users page:', error);
        req.session.error = 'Failed to load users page';
        res.redirect('/admin/dashboard');
    }
};

// Create a new user
exports.createUser = async (req, res) => {
    try {
        const { name, email, phone, address, role, password } = req.body;
        
        // Validate required fields
        if (!name || !email || !password) {
            return res.status(400).json({ message: 'Name, email and password are required' });
        }
        
        // Check if user already exists
        const existingUser = await User.findOne({ email });
        if (existingUser) {
            return res.status(400).json({ message: 'User with this email already exists' });
        }
        
        // Hash password
        const salt = await bcrypt.genSalt(10);
        const hashedPassword = await bcrypt.hash(password, salt);
        
        // Create new user
        const user = new User({
            name,
            email,
            phone: phone || '',
            address: address || '',
            role: role || 'user',
            password: hashedPassword
        });
        
        await user.save();
        
        // Don't send back the password
        user.password = undefined;
        
        res.status(201).json(user);
    } catch (error) {
        console.error('Error creating user:', error);
        res.status(500).json({ message: 'Failed to create user' });
    }
};

// Update an existing user
exports.updateUser = async (req, res) => {
    try {
        const { id } = req.params;
        const { name, email, phone, address, role, password } = req.body;
        
        // Find user
        const user = await User.findById(id);
        if (!user) {
            return res.status(404).json({ message: 'User not found' });
        }
        
        // Check if email is taken by another user
        if (email !== user.email) {
            const existingUser = await User.findOne({ email });
            if (existingUser) {
                return res.status(400).json({ message: 'Email is already in use' });
            }
        }
        
        // Update user fields
        user.name = name || user.name;
        user.email = email || user.email;
        user.phone = phone || user.phone;
        user.address = address || user.address;
        
        // Only admin can change roles, and an admin cannot downgrade themselves
        if (role && req.user.role === 'admin') {
            // Prevent removing the last admin
            if (user.role === 'admin' && role !== 'admin') {
                const adminCount = await User.countDocuments({ role: 'admin' });
                if (adminCount <= 1) {
                    return res.status(400).json({ message: 'Cannot remove the last admin user' });
                }
            }
            user.role = role;
        }
        
        // Update password if provided
        if (password) {
            const salt = await bcrypt.genSalt(10);
            user.password = await bcrypt.hash(password, salt);
        }
        
        await user.save();
        
        // Don't send back the password
        user.password = undefined;
        
        res.status(200).json(user);
    } catch (error) {
        console.error('Error updating user:', error);
        res.status(500).json({ message: 'Failed to update user' });
    }
};

// Delete a user
exports.deleteUser = async (req, res) => {
    try {
        const { id } = req.params;
        
        // Prevent deleting yourself
        if (id === req.user.id) {
            return res.status(400).json({ message: 'You cannot delete your own account' });
        }
        
        // Find user
        const user = await User.findById(id);
        if (!user) {
            return res.status(404).json({ message: 'User not found' });
        }
        
        // Prevent deleting the last admin
        if (user.role === 'admin') {
            const adminCount = await User.countDocuments({ role: 'admin' });
            if (adminCount <= 1) {
                return res.status(400).json({ message: 'Cannot delete the last admin user' });
            }
        }
        
        // Check if user has orders
        const userOrders = await Order.countDocuments({ user: id });
        if (userOrders > 0) {
            return res.status(400).json({ message: 'Cannot delete user with existing orders' });
        }
        
        await User.findByIdAndDelete(id);
        res.status(200).json({ message: 'User deleted successfully' });
    } catch (error) {
        console.error('Error deleting user:', error);
        res.status(500).json({ message: 'Failed to delete user' });
    }
};

// Get services management page
exports.getServices = async (req, res) => {
    try {
        res.render('admin/services', {
            title: 'Manage Services',
            user: {
                id: req.session.userId,
                name: req.session.userName,
                role: req.session.userRole,
                isAuthenticated: req.session.isAuthenticated
            }
        });
    } catch (error) {
        console.error('Error getting services page:', error);
        req.session.error = 'Failed to load services page';
        res.redirect('/admin/dashboard');
    }
};

// Get all services data for API
exports.getServicesData = async (req, res) => {
    try {
        const services = await Service.find().sort({ category: 1, name: 1 });
        res.json(services);
    } catch (error) {
        console.error('Error getting services data:', error);
        res.status(500).json({ message: 'Failed to load services' });
    }
};

// Create a new service
exports.createService = async (req, res) => {
    try {
        const { name, price, category, image, icon, shortDescription, description, features } = req.body;
        
        // Validate required fields
        if (!name || !price || !category || !image || !icon || !shortDescription || !description) {
            return res.status(400).json({ message: 'All required fields must be provided' });
        }
        
        // Check if service already exists
        const existingService = await Service.findOne({ name });
        if (existingService) {
            return res.status(400).json({ message: 'A service with this name already exists' });
        }
        
        // Create new service
        const service = new Service({
            name,
            price,
            category,
            image,
            icon,
            shortDescription,
            description,
            features: features || []
        });
        
        await service.save();
        res.status(201).json(service);
    } catch (error) {
        console.error('Error creating service:', error);
        res.status(500).json({ message: 'Failed to create service', error: error.message });
    }
};

// Update an existing service
exports.updateService = async (req, res) => {
    try {
        const { id } = req.params;
        const { name, price, category, image, icon, shortDescription, description, features } = req.body;
        
        // Validate required fields
        if (!name || !price || !category || !image || !icon || !shortDescription || !description) {
            return res.status(400).json({ message: 'All required fields must be provided' });
        }
        
        // Check if service exists
        const service = await Service.findById(id);
        if (!service) {
            return res.status(404).json({ message: 'Service not found' });
        }
        
        // Check if name is taken by another service
        const existingService = await Service.findOne({ name, _id: { $ne: id } });
        if (existingService) {
            return res.status(400).json({ message: 'A service with this name already exists' });
        }
        
        // Update service
        service.name = name;
        service.price = price;
        service.category = category;
        service.image = image;
        service.icon = icon;
        service.shortDescription = shortDescription;
        service.description = description;
        service.features = features || [];
        
        await service.save();
        res.status(200).json(service);
    } catch (error) {
        console.error('Error updating service:', error);
        res.status(500).json({ message: 'Failed to update service', error: error.message });
    }
};

// Delete a service
exports.deleteService = async (req, res) => {
    try {
        const { id } = req.params;
        
        // Check if service exists
        const service = await Service.findById(id);
        if (!service) {
            return res.status(404).json({ message: 'Service not found' });
        }
        
        // Check if service is used in any orders
        const ordersWithService = await Order.countDocuments({ service: id });
        if (ordersWithService > 0) {
            return res.status(400).json({ 
                message: 'This service cannot be deleted because it is used in existing orders. Consider updating it instead.' 
            });
        }
        
        await Service.findByIdAndDelete(id);
        res.status(200).json({ message: 'Service deleted successfully' });
    } catch (error) {
        console.error('Error deleting service:', error);
        res.status(500).json({ message: 'Failed to delete service' });
    }
};

// Get dashboard stats for API
exports.getDashboardStats = async (req, res) => {
    try {
        // Get counts using a single aggregation pipeline for better performance
        const statsAggregation = await Order.aggregate([
            {
                $facet: {
                    'pendingOrders': [
                        { $match: { status: 'pending' } },
                        { $count: 'count' }
                    ],
                    'completedOrders': [
                        { $match: { status: 'completed' } },
                        { $count: 'count' }
                    ],
                    'revenueStats': [
                        {
                            $match: {
                                $or: [
                                    { paymentStatus: { $in: ['paid', 'cod_paid'] } },
                                    { isPaid: true }
                                ]
                            }
                        },
                        {
                            $project: {
                                amount: { $ifNull: ['$amount', '$totalAmount'] },
                                paymentMethod: 1,
                                paidAt: { $ifNull: ['$paidAt', '$paymentDate'] },
                                month: { $month: { $ifNull: ['$paidAt', '$paymentDate'] } },
                                year: { $year: { $ifNull: ['$paidAt', '$paymentDate'] } }
                            }
                        },
                        {
                            $group: {
                                _id: null,
                                totalRevenue: { $sum: '$amount' },
                                methodStats: {
                                    $push: {
                                        method: '$paymentMethod',
                                        amount: '$amount',
                                        paidAt: '$paidAt',
                                        month: '$month',
                                        year: '$year'
                                    }
                                }
                            }
                        }
                    ]
                }
            }
        ]);
        
        // Extract counts from aggregation results
        const pendingOrders = statsAggregation[0].pendingOrders[0]?.count || 0;
        const completedOrders = statsAggregation[0].completedOrders[0]?.count || 0;
        
        // Process revenue data
        let totalRevenue = 0;
        let monthRevenue = 0;
        const revenueByMethod = {
            cash: 0,
            card: 0,
            bank_transfer: 0
        };
        
        // Get current month/year for monthly filtering
        const now = new Date();
        const currentMonth = now.getMonth() + 1;
        const currentYear = now.getFullYear();
        
        if (statsAggregation[0].revenueStats[0]) {
            totalRevenue = statsAggregation[0].revenueStats[0].totalRevenue || 0;
            
            // Process method stats
            const methodStats = statsAggregation[0].revenueStats[0].methodStats || [];
            methodStats.forEach(stat => {
                const method = stat.method || 'cash';
                const amount = stat.amount || 0;
                
                // Update revenue by method
                if (method === 'cash' || method === 'cod') {
                    revenueByMethod.cash += amount;
                } else if (method === 'card') {
                    revenueByMethod.card += amount;
                } else if (method === 'bank_transfer') {
                    revenueByMethod.bank_transfer += amount;
                }
                
                // Check if this is from current month for monthly revenue
                if (stat.year === currentYear && stat.month === currentMonth) {
                    monthRevenue += amount;
                }
            });
        }
        
        res.json({
            pendingOrders,
            completedOrders,
            totalRevenue,
            monthRevenue,
            revenueByMethod
        });
    } catch (error) {
        console.error('Error getting dashboard stats:', error);
        res.status(500).json({ message: 'Failed to load dashboard stats' });
    }
};

// Get recent orders for API
exports.getRecentOrders = async (req, res) => {
    try {
        const recentOrders = await Order.find()
            .sort({ createdAt: -1 })
            .limit(5)
            .lean();  // Use lean for better performance
            
        // Map to the format needed for the dashboard
        const formattedOrders = recentOrders.map(order => {
            // Normalize the order to handle field differences
            const normalizedOrder = normalizeOrderFields(order);
            
            return {
                _id: normalizedOrder._id,
                customerName: normalizedOrder.customerName,
                email: normalizedOrder.email,
                service: normalizedOrder.serviceName || normalizedOrder.service,
                date: normalizedOrder.date,
                totalAmount: normalizedOrder.amount,
                paymentStatus: normalizedOrder.paymentStatus
            };
        });
        
        res.json(formattedOrders);
    } catch (error) {
        console.error('Error getting recent orders:', error);
        res.status(500).json({ message: 'Failed to load recent orders' });
    }
};

// Get all orders data for API
exports.getOrdersData = async (req, res) => {
    try {
        console.log('API: Getting orders data with query params:', req.query);
        
        // Verify database connection
        if (mongoose.connection.readyState !== 1) {
            console.error('Database connection not ready. State:', mongoose.connection.readyState);
            return res.status(500).json({ 
                message: 'Database connection not ready',
                dbState: mongoose.connection.readyState 
            });
        }
        
        // Check if Orders collection exists and count documents
        try {
            const orderCount = await Order.countDocuments({});
            console.log(`Total orders in database: ${orderCount}`);
            
            if (orderCount === 0) {
                console.log('No orders found in database');
                return res.json([]);
            }
        } catch (countError) {
            console.error('Error counting orders:', countError);
        }
        
        // Get query parameters for filtering
        const { paymentMethod, paymentStatus, showArchived } = req.query;
        
        // Build filter object
        const filter = {
            // By default, don't show archived orders unless specifically requested
            isArchived: showArchived === 'true' ? true : { $ne: true }
        };
        
        if (paymentMethod) {
            filter.paymentMethod = paymentMethod;
        }
        
        if (paymentStatus) {
            // Handle payment status filtering with both isPaid and paymentStatus fields
            if (paymentStatus === 'paid') {
                filter.$or = [
                    { paymentStatus: { $in: ['paid', 'cod_paid'] } },
                    { isPaid: true }
                ];
            } else if (paymentStatus === 'pending') {
                filter.$and = [
                    { $or: [
                        { paymentStatus: { $ne: 'paid' } },
                        { paymentStatus: { $exists: false } }
                    ]},
                    { $or: [
                        { isPaid: false },
                        { isPaid: { $exists: false } }
                    ]}
                ];
            }
        }
        
        console.log('Database query filter:', JSON.stringify(filter, null, 2));
        
        // Only select fields that are needed
        const orders = await Order.find(filter)
            .select('_id customerName email phone address service date scheduledDate amount totalAmount paymentMethod paymentStatus status message notes createdAt paymentId isPaid isArchived archivedAt')
            .sort({ createdAt: -1 })
            .lean(); // Use lean to improve performance
            
        console.log(`Found ${orders.length} orders matching criteria`);
        
        // Check for mock data by seeing if we have orders even with empty DB
        if (orders.length > 0) {
            const sampleOrder = orders[0];
            console.log('Sample order data structure:', Object.keys(sampleOrder));
            console.log('Sample order ID:', sampleOrder._id);
            
            // Check if this ID actually exists in the database
            const verifyOrder = await Order.findById(sampleOrder._id);
            if (!verifyOrder) {
                console.error('CRITICAL: Order exists in result but not in database!');
                return res.status(500).json({ 
                    message: 'Data integrity error - orders exist in result but not in database',
                    debug: true,
                    sample: sampleOrder._id
                });
            }
        }
            
        // Format orders for admin panel with proper field normalization
        const formattedOrders = orders.map(order => normalizeOrderFields(order));
        
        // Return empty array instead of null or undefined
        res.json(formattedOrders || []);
    } catch (error) {
        console.error('Error getting orders data:', error);
        res.status(500).json({ 
            message: 'Failed to load orders', 
            error: error.message,
            stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
        });
    }
};

// Helper function to normalize order fields between old and new schema
function normalizeOrderFields(order) {
    if (!order) return {};
    
    // Convert Mongoose document to plain object
    const plainOrder = order.toObject ? order.toObject() : { ...order };
    
    // Normalize customer information
    plainOrder.customerName = plainOrder.customerName || 
                            (plainOrder.customer && plainOrder.customer.name) || 
                            'Unknown';
    
    // Normalize amount/totalAmount - ensure they're numbers
    const amount = parseFloat(plainOrder.amount) || parseFloat(plainOrder.totalAmount) || 0;
    plainOrder.amount = amount;
    plainOrder.totalAmount = amount;
    
    // Normalize date/scheduledDate
    plainOrder.date = plainOrder.scheduledDate || plainOrder.date || plainOrder.createdAt;
    plainOrder.scheduledDate = plainOrder.scheduledDate || plainOrder.date;
    
    // Normalize message/notes
    plainOrder.message = plainOrder.message || plainOrder.notes || '';
    plainOrder.notes = plainOrder.notes || plainOrder.message || '';
    
    // Normalize payment status
    if (plainOrder.isPaid === true) {
        plainOrder.paymentStatus = 'paid';
    } else if (plainOrder.paymentStatus === 'cod_paid') {
        plainOrder.isPaid = true;
    } else if (!plainOrder.paymentStatus) {
        plainOrder.paymentStatus = plainOrder.isPaid ? 'paid' : 'pending';
    }
    
    // Normalize payment method
    if (!plainOrder.paymentMethod) {
        plainOrder.paymentMethod = 'cod'; // Default to COD if not specified
    }
    
    // Normalize service
    if (plainOrder.service && typeof plainOrder.service === 'object') {
        plainOrder.serviceName = plainOrder.service.name || 'Unknown Service';
    } else {
        plainOrder.serviceName = plainOrder.service || 'Unknown Service';
    }
    
    // Ensure all common fields exist with defaults
    plainOrder.email = plainOrder.email || '';
    plainOrder.phone = plainOrder.phone || '';
    plainOrder.address = plainOrder.address || '';
    plainOrder.status = plainOrder.status || 'pending';
    
    return plainOrder;
}

// Export the normalizeOrderFields function
exports.normalizeOrderFields = normalizeOrderFields;

// Get single order data for API
exports.getOrderData = async (req, res) => {
    try {
        const { id } = req.params;
        
        const order = await Order.findById(id);
        if (!order) {
            return res.status(404).json({ message: 'Order not found' });
        }
        
        res.json(order);
    } catch (error) {
        console.error('Error getting order data:', error);
        res.status(500).json({ message: 'Failed to load order' });
    }
};

// Get all users data for API
exports.getUsersData = async (req, res) => {
    try {
        const users = await User.find()
            .select('-password')
            .sort({ createdAt: -1 });
            
        res.json(users);
    } catch (error) {
        console.error('Error getting users data:', error);
        res.status(500).json({ message: 'Failed to load users' });
    }
};

// Mark order as completed
exports.markOrderAsCompleted = async (req, res) => {
    try {
        const { id } = req.params;
        
        const order = await Order.findById(id);
        if (!order) {
            return res.status(404).json({ message: 'Order not found' });
        }
        
        // Normalize fields to ensure compatibility
        const normalizedOrder = normalizeOrderFields(order);
        
        // Update order status
        order.status = 'completed';
        order.completedAt = Date.now();
        
        // Ensure all required fields are present for both schemas
        if (order.amount === undefined && normalizedOrder.totalAmount) {
            order.amount = normalizedOrder.totalAmount;
        }
        
        if (order.totalAmount === undefined && normalizedOrder.amount) {
            order.totalAmount = normalizedOrder.amount;
        }
        
        // Ensure payment method is set
        if (!order.paymentMethod) {
            order.paymentMethod = normalizedOrder.paymentMethod || 'cod';
        }
        
        // Log the update for debugging
        console.log(`Updating order ${id} status to completed`, {
            beforeUpdate: {
                status: order.status,
                paymentStatus: order.paymentStatus,
                paymentMethod: order.paymentMethod,
                amount: order.amount || order.totalAmount,
                fields: Object.keys(order.toObject())
            }
        });
        
        // Save the order with error handling
        try {
            await order.save();
            console.log(`Order ${id} marked as completed successfully`);
            return res.status(200).json({ message: 'Order marked as completed', order });
        } catch (saveError) {
            console.error('Error saving order:', saveError);
            
            // Try an alternative update approach if save fails
            const updateResult = await Order.updateOne(
                { _id: id },
                { 
                    $set: { 
                        status: 'completed',
                        completedAt: Date.now(),
                        // Include these fields to ensure they exist
                        paymentMethod: order.paymentMethod || 'cod',
                        amount: order.amount || order.totalAmount || 0,
                        totalAmount: order.totalAmount || order.amount || 0
                    } 
                }
            );
            
            console.log('Update result:', updateResult);
            
            if (updateResult.modifiedCount > 0) {
                return res.status(200).json({ message: 'Order marked as completed (alternative method)', updateResult });
            } else {
                throw new Error('Failed to update order status with alternative method');
            }
        }
    } catch (error) {
        console.error('Error marking order as completed:', error);
        res.status(500).json({ message: 'Failed to update order status: ' + error.message });
    }
};

// Delete/cancel order
exports.deleteOrder = async (req, res) => {
    try {
        const { id } = req.params;
        
        const order = await Order.findById(id);
        if (!order) {
            return res.status(404).json({ message: 'Order not found' });
        }
        
        // Only allow archiving actions on orders
        if (order.status === 'completed') {
            // Archive completed orders instead of deleting
            order.isArchived = true;
            order.archivedAt = new Date();
            order.archivedReason = 'Archived by admin';
            await order.save();
            
            return res.status(200).json({ 
                message: 'Order has been archived successfully. It will no longer appear in the orders list but remains in the database for refund processing.' 
            });
        }
        
        if (order.isPaid || order.paymentStatus === 'paid' || order.paymentStatus === 'cod_paid') {
            // Archive paid orders instead of deleting
            order.isArchived = true;
            order.archivedAt = new Date();
            order.archivedReason = 'Archived by admin';
            await order.save();
            
            return res.status(200).json({ 
                message: 'Paid order has been archived successfully. It will no longer appear in the orders list but remains in the database for refund processing.' 
            });
        }
        
        // For pending and unpaid orders, we archive too so that we maintain a complete record
        order.isArchived = true;
        order.archivedAt = new Date();
        order.archivedReason = 'Deleted by admin';
        await order.save();
        
        res.status(200).json({ message: 'Order archived successfully' });
    } catch (error) {
        console.error('Error archiving order:', error);
        res.status(500).json({ message: 'Failed to archive order' });
    }
};

// Get revenue analysis
exports.getRevenueAnalysis = async (req, res) => {
    try {
        const today = new Date();
        const oneYearAgo = new Date(today.getFullYear() - 1, today.getMonth(), 1);

        // Create an optimized aggregation pipeline that handles everything in the database
        const revenueAnalysis = await Order.aggregate([
            {
                $facet: {
                    // Revenue by payment method
                    'byMethod': [
                        {
                            $match: {
                                $or: [
                                    { paymentStatus: { $in: ['paid', 'cod_paid'] } },
                                    { isPaid: true }
                                ]
                            }
                        },
                        {
                            $project: {
                                paymentMethod: 1,
                                amount: { $cond: [{ $gt: ["$amount", 0] }, "$amount", { $ifNull: ["$totalAmount", 0] }] }
                            }
                        },
                        {
                            $group: {
                                _id: {
                                    $cond: [
                                        { $in: ["$paymentMethod", ["cash", "cod"]] },
                                        "cash",
                                        { $ifNull: ["$paymentMethod", "other"] }
                                    ]
                                },
                                total: { $sum: "$amount" },
                                count: { $sum: 1 }
                            }
                        }
                    ],
                    // Total revenue
                    'total': [
                        {
                            $match: {
                                $or: [
                                    { paymentStatus: { $in: ['paid', 'cod_paid'] } },
                                    { isPaid: true }
                                ]
                            }
                        },
                        {
                            $group: {
                                _id: null,
                                total: {
                                    $sum: {
                                        $cond: [
                                            { $gt: ["$amount", 0] },
                                            "$amount",
                                            { $ifNull: ["$totalAmount", 0] }
                                        ]
                                    }
                                }
                            }
                        }
                    ],
                    // Monthly data - pre-calculate last 12 months
                    'monthly': [
                        {
                            $match: {
                                $or: [
                                    { paymentStatus: { $in: ['paid', 'cod_paid'] } },
                                    { isPaid: true }
                                ],
                                $or: [
                                    { paidAt: { $gte: oneYearAgo } },
                                    { paymentDate: { $gte: oneYearAgo } }
                                ]
                            }
                        },
                        {
                            $project: {
                                amount: { $cond: [{ $gt: ["$amount", 0] }, "$amount", { $ifNull: ["$totalAmount", 0] }] },
                                paymentDate: { $ifNull: ["$paidAt", { $ifNull: ["$paymentDate", "$createdAt"] }] }
                            }
                        },
                        {
                            $group: {
                                _id: {
                                    year: { $year: "$paymentDate" },
                                    month: { $month: "$paymentDate" }
                                },
                                total: { $sum: "$amount" },
                                count: { $sum: 1 }
                            }
                        },
                        {
                            $sort: { "_id.year": 1, "_id.month": 1 }
                        }
                    ]
                }
            }
        ]);

        // Process the aggregation results
        const result = revenueAnalysis[0];
        const totalRevenue = result.total[0]?.total || 0;
        const revenueByMethod = result.byMethod || [];
        
        // Initialize last 12 months with zero values
        const last12Months = [];
        for (let i = 11; i >= 0; i--) {
            const monthDate = new Date(today.getFullYear(), today.getMonth() - i, 1);
            last12Months.push({
                _id: {
                    year: monthDate.getFullYear(),
                    month: monthDate.getMonth() + 1
                },
                total: 0,
                count: 0
            });
        }
        
        // Fill in data for months that have revenue
        result.monthly.forEach(monthData => {
            const matchingMonth = last12Months.find(m => 
                m._id.year === monthData._id.year && m._id.month === monthData._id.month
            );
            
            if (matchingMonth) {
                matchingMonth.total = monthData.total;
                matchingMonth.count = monthData.count;
            }
        });

        // Efficient query for pending COD payments with proper projection
        const pendingCODPayments = await Order.find({
            $or: [
                { paymentMethod: 'cod', paymentStatus: 'pending' },
                { paymentMethod: 'cash', paymentStatus: 'pending' }
            ]
        })
        .select('_id customer service amount totalAmount scheduledDate date')
        .populate('customer', 'name email phone')
        .populate('service', 'name price')
        .lean();

        // Efficient query for recent COD payments with proper projection
        const recentCODPayments = await Order.find({
            $or: [
                { paymentMethod: 'cod', paymentStatus: 'paid' },
                { paymentMethod: 'cash', paymentStatus: 'paid' },
                { paymentMethod: 'cod', paymentStatus: 'cod_paid' }
            ]
        })
        .select('_id customer service amount totalAmount paymentDate scheduledDate date paymentDetails')
        .populate('customer', 'name email phone')
        .populate('service', 'name price')
        .populate('paymentDetails.collectedBy', 'name')
        .sort({ paymentDate: -1 })
        .limit(10)
        .lean();

        // Apply normalization to ensure consistent format
        const normalizedPendingPayments = pendingCODPayments.map(payment => {
            // Simple normalization for amount
            payment.amount = payment.amount || payment.totalAmount || 0;
            return payment;
        });

        const normalizedRecentPayments = recentCODPayments.map(payment => {
            // Simple normalization for amount
            payment.amount = payment.amount || payment.totalAmount || 0;
            return payment;
        });

        res.json({
            totalRevenue,
            revenueByMethod,
            monthlyRevenue: last12Months,
            pendingCODPayments: normalizedPendingPayments,
            recentCODPayments: normalizedRecentPayments
        });
    } catch (error) {
        console.error('Error getting revenue analysis:', error);
        res.status(500).json({ error: 'Failed to get revenue analysis' });
    }
};

// Get completed orders page
exports.getCompletedOrders = async (req, res) => {
    try {
        res.render('admin/completed-orders', {
            title: 'Completed Orders',
            user: {
                id: req.session.userId,
                name: req.session.userName,
                role: req.session.userRole,
                isAuthenticated: req.session.isAuthenticated
            }
        });
    } catch (error) {
        console.error('Error getting completed orders page:', error);
        req.session.error = 'Failed to load completed orders page';
        res.redirect('/admin/dashboard');
    }
};

// Get completed orders data
exports.getCompletedOrdersData = async (req, res) => {
    try {
        const completedOrders = await Order.find({ status: 'completed' })
            .sort({ completedAt: -1 });
            
        // Format orders for admin panel
        const formattedOrders = completedOrders.map(order => ({
            _id: order._id,
            customerName: order.customerName,
            email: order.email,
            phone: order.phone,
            service: order.service,
            date: order.date,
            totalAmount: order.totalAmount,
            paymentMethod: order.paymentMethod,
            paymentStatus: order.isPaid ? 'paid' : 'pending',
            completedAt: order.completedAt,
            paymentId: order.paymentId
        }));
        
        res.json(formattedOrders);
    } catch (error) {
        console.error('Error getting completed orders data:', error);
        res.status(500).json({ message: 'Failed to load completed orders' });
    }
};

// Reset admin password
exports.resetAdminPassword = async (req, res) => {
    try {
        const { userId } = req.params;
        
        // Validate admin ID
        if (!mongoose.Types.ObjectId.isValid(userId)) {
            return res.status(400).json({ message: 'Invalid user ID format' });
        }
        
        // Find the admin user
        const admin = await User.findById(userId);
        if (!admin) {
            return res.status(404).json({ message: 'Admin user not found' });
        }
        
        if (admin.role !== 'admin') {
            return res.status(400).json({ message: 'User is not an admin' });
        }
         
        // Create a new password hash
        const password = 'admin123'; // Default reset password
        const salt = await bcrypt.genSalt(10);
        const hashedPassword = await bcrypt.hash(password, salt);
        
        // Update the admin's password
        admin.password = hashedPassword;
        await admin.save();
        
        console.log(`Admin password reset successfully for ${admin.name} (${admin._id})`);
        
        // Return success response
        res.status(200).json({ 
            message: 'Admin password has been reset successfully',
            user: {
                id: admin._id,
                name: admin.name,
                email: admin.email,
                role: admin.role
            }
        });
    } catch (error) {
        console.error('Error resetting admin password:', error);
        res.status(500).json({ message: 'Failed to reset admin password' });
    }
};

// Update order payment status (for COD)
exports.updateOrderPayment = async (req, res) => {
    try {
        const { orderId } = req.params;
        const { paymentStatus, notes } = req.body;

        const order = await Order.findById(orderId);
        if (!order) {
            return res.status(404).json({ error: 'Order not found' });
        }

        // Update payment status
        order.paymentStatus = paymentStatus;
        order.paymentDate = new Date();
        order.paymentDetails = {
            ...order.paymentDetails,
            paymentStatus,
            paymentDate: new Date(),
            collectedBy: req.session.userId
        };

        // If payment is marked as paid, update order status to completed
        if (paymentStatus === 'cod_paid') {
            order.status = 'completed';
            order.completedAt = new Date();
        }

        await order.save();

        // Log the payment update
        console.log('Order payment updated:', {
            orderId,
            paymentStatus,
            updatedBy: req.session.userId
        });

        res.json({ success: true, message: 'Payment status updated successfully' });
    } catch (error) {
        console.error('Error updating order payment:', error);
        res.status(500).json({ error: 'Failed to update payment status' });
    }
};

// Get COD payment details
exports.getCODPaymentDetails = async (req, res) => {
    try {
        const { orderId } = req.params;
        
        const order = await Order.findById(orderId)
            .populate('customer', 'name email phone')
            .populate('service', 'name price')
            .populate('paymentDetails.collectedBy', 'name');

        if (!order) {
            return res.status(404).json({ error: 'Order not found' });
        }

        res.json(order);
    } catch (error) {
        console.error('Error getting COD payment details:', error);
        res.status(500).json({ error: 'Failed to get payment details' });
    }
};

// COD Email Search Page
exports.getCODEmailSearch = async (req, res) => {
  res.render('admin/cod-email-search', {
    title: 'COD Email Search | Admin Dashboard',
    user: {
      id: req.session.userId,
      name: req.session.userName,
      role: req.session.userRole,
      isAuthenticated: req.session.isAuthenticated
    }
  });
};

// Get Customer Orders by Email (for COD Email Search Page)
exports.getCustomerOrdersByEmail = async (req, res) => {
  try {
    const { email, paymentMethod } = req.query;
    
    if (!email) {
      return res.status(400).json({ 
        success: false, 
        error: 'Email is required' 
      });
    }
    
    // Load models
    const Order = require('../models/Order');
    const User = require('../models/User');
    
    // Find customer by email (either directly or in an order)
    const user = await User.findOne({ email: email.toLowerCase() });
    
    // Find all orders for this email
    const query = { email: email.toLowerCase() };
    
    // If payment method filter is provided
    if (paymentMethod) {
      query.paymentMethod = paymentMethod;
    }
    
    // Find orders
    const orders = await Order.find(query)
      .sort({ date: -1, createdAt: -1 })
      .lean();
      
    // Get all payment history for this customer (including non-COD)
    const paymentHistory = await Order.find({ 
      email: email.toLowerCase(),
      paymentStatus: { $in: ['paid', 'cod_paid'] } // Only include paid orders
    })
    .sort({ paymentDate: -1 })
    .lean();
    
    // Customer information
    const customerInfo = {
      name: user ? user.name : (orders.length > 0 ? orders[0].customerName : 'Unknown'),
      email: email,
      phone: user ? user.phone : (orders.length > 0 ? orders[0].phone : 'Unknown'),
      totalOrders: orders.length
    };
    
    res.json({
      success: true,
      orders: orders.map(order => normalizeOrderFields(order)),
      paymentHistory: paymentHistory.map(order => normalizeOrderFields(order)),
      customerInfo
    });
  } catch (error) {
    console.error('Error fetching customer orders by email:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Failed to fetch customer orders' 
    });
  }
};

// Refund Management
exports.getRefundManagement = async (req, res) => {
    try {
        return res.render('admin/refund-management', {
            title: 'Refund Management',
            user: req.user,
            searchQuery: '',
            searchResults: null,
            successMessage: req.flash('success'),
            errorMessage: req.flash('error')
        });
    } catch (error) {
        console.error('Error loading refund management page:', error);
        req.flash('error', 'Failed to load refund management page');
        return res.redirect('/admin/dashboard');
    }
};

exports.searchOrderForRefund = async (req, res) => {
    const { orderId } = req.body;
    
    if (!orderId) {
        req.flash('error', 'Order ID is required');
        return res.redirect('/admin/refund-management');
    }
    
    try {
        // Find the order and populate customer data if available
        const order = await Order.findById(orderId)
            .populate('customer', 'name email phone')
            .lean();
            
        if (!order) {
            req.flash('error', 'Order not found');
            return res.render('admin/refund-management', {
                title: 'Refund Management',
                user: req.user,
                searchQuery: orderId,
                searchResults: null,
                successMessage: null,
                errorMessage: 'Order not found'
            });
        }
        
        // Normalize order fields to ensure consistent structure
        const normalizedOrder = normalizeOrderFields(order);
        
        // If order doesn't have customer reference but has customer fields directly
        if (!normalizedOrder.customer && normalizedOrder.customerName) {
            normalizedOrder.customer = {
                name: normalizedOrder.customerName,
                email: normalizedOrder.email || 'N/A',
                phone: normalizedOrder.phone || 'N/A'
            };
        }
        
        return res.render('admin/refund-management', {
            title: 'Refund Management',
            user: req.user,
            searchQuery: orderId,
            searchResults: normalizedOrder,
            successMessage: null,
            errorMessage: null
        });
    } catch (error) {
        console.error('Error searching for order:', error);
        req.flash('error', 'Failed to search for order');
        return res.render('admin/refund-management', {
            title: 'Refund Management',
            user: req.user,
            searchQuery: orderId,
            searchResults: null,
            successMessage: null,
            errorMessage: 'Failed to search for order: ' + error.message
        });
    }
};

exports.processRefund = async (req, res) => {
    const { orderId, refundAmount, refundReason } = req.body;
    
    if (!orderId || !refundAmount || !refundReason) {
        req.flash('error', 'Missing required fields');
        return res.redirect('/admin/refund-management');
    }
    
    try {
        const order = await Order.findById(orderId);
        
        if (!order) {
            req.flash('error', 'Order not found');
            return res.redirect('/admin/refund-management');
        }
        
        if (order.paymentStatus !== 'paid') {
            req.flash('error', 'Cannot refund an unpaid order');
            return res.redirect(`/admin/refund-management/search?orderId=${orderId}`);
        }
        
        if (order.refunded) {
            req.flash('error', 'This order has already been refunded');
            return res.redirect(`/admin/refund-management/search?orderId=${orderId}`);
        }
        
        const refundAmountValue = parseFloat(refundAmount);
        
        if (isNaN(refundAmountValue) || refundAmountValue <= 0 || refundAmountValue > order.totalAmount) {
            req.flash('error', 'Invalid refund amount');
            return res.redirect(`/admin/refund-management/search?orderId=${orderId}`);
        }
        
        // If it's a Stripe payment, process through Stripe
        if (order.paymentMethod === 'card' && order.paymentDetails && order.paymentDetails.paymentId) {
            try {
                // Assuming you have Stripe configured elsewhere in your app
                const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
                
                // Process the refund through Stripe
                const refund = await stripe.refunds.create({
                    payment_intent: order.paymentDetails.paymentId,
                    amount: Math.round(refundAmountValue * 100), // Convert to cents
                    reason: 'requested_by_customer'
                });
                
                // Update the order with refund details
                order.refunded = true;
                order.refundDetails = {
                    refundId: refund.id,
                    amount: refundAmountValue,
                    date: new Date(),
                    reason: refundReason,
                    processedBy: req.user._id
                };
                
                await order.save();
                
                // Send email notification about the refund
                try {
                    const emailService = require('../config/email');
                    await emailService.sendRefundConfirmation(order);
                } catch (emailError) {
                    console.error('Failed to send refund confirmation email:', emailError);
                    // Continue with the refund process even if email fails
                }
                
                req.flash('success', `Refund of ₹${refundAmountValue.toFixed(2)} processed successfully.`);
                return res.redirect('/admin/refund-management');
                
            } catch (stripeError) {
                console.error('Stripe refund error:', stripeError);
                req.flash('error', `Failed to process refund: ${stripeError.message}`);
                return res.redirect(`/admin/refund-management/search?orderId=${orderId}`);
            }
        } else {
            // For cash payments or other methods, just mark as refunded in our system
            order.refunded = true;
            order.refundDetails = {
                refundId: `manual-${Date.now()}`,
                amount: refundAmountValue,
                date: new Date(),
                reason: refundReason,
                processedBy: req.user._id
            };
            
            await order.save();
            
            // Send email notification about the refund
            try {
                const emailService = require('../config/email');
                await emailService.sendRefundConfirmation(order);
            } catch (emailError) {
                console.error('Failed to send refund confirmation email:', emailError);
                // Continue with the refund process even if email fails
            }
            
            req.flash('success', `Manual refund of ₹${refundAmountValue.toFixed(2)} recorded successfully.`);
            return res.redirect('/admin/refund-management');
        }
        
    } catch (error) {
        console.error('Error processing refund:', error);
        req.flash('error', 'Failed to process refund: ' + error.message);
        return res.redirect('/admin/refund-management');
    }
}; 