const express = require('express');
const router = express.Router();
const authController = require('../controllers/authController');
const { isAuthenticated, isAdmin } = require('../middleware/auth');

// Authentication routes
router.get('/login', authController.getLoginPage);
router.post('/login', authController.login);

// Email verification routes
router.post('/verify-email', authController.sendVerificationCode);
router.post('/verify-code', authController.verifyEmailCode);

// Restrict register page to admin users in production, or allow in development
if (process.env.NODE_ENV === 'production') {
  // In production, only admin can access register page
  router.get('/register', isAdmin, authController.getRegisterPage);
  router.post('/register', isAdmin, authController.register);
} else {
  // In development, register page is freely accessible
  router.get('/register', authController.getRegisterPage);
  router.post('/register', authController.register);
}

// Special route to clear all session data without redirecting
router.get('/clear-session', (req, res) => {
  // Clear all cookies
  res.clearCookie('connect.sid');
  res.clearCookie('admin-auth');
  res.clearCookie('junkexpert.sid');
  
  // Clear session data
  if (req.session) {
    req.session.isAuthenticated = false;
    req.session.userId = null;
    req.session.userRole = null;
    req.session.userName = null;
    
    // Destroy session
    req.session.destroy(() => {
      res.send('All session data cleared. <a href="/auth/login">Click here to login</a>');
    });
  } else {
    res.send('No session to clear. <a href="/auth/login">Click here to login</a>');
  }
});

router.get('/logout', isAuthenticated, authController.logout);

// Direct admin login route for production troubleshooting
router.post('/direct-admin-login', authController.directAdminLogin);

// Special Render admin login route for direct dashboard access
router.get('/render-admin', authController.renderAdminLogin);

module.exports = router; 