<%- include('../partials/header') %>

<div class="pt-32 pb-16 bg-gray-50">
    <div class="container mx-auto px-6">
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <div class="p-6 border-b">
                <div class="flex justify-between items-center">
                    <div>
                        <h1 class="text-3xl font-bold mb-2">Revenue Tracker</h1>
                        <p class="text-gray-600">Track and monitor all revenue from your business</p>
                    </div>
                    <a href="/admin/dashboard" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                        <i class="fas fa-arrow-left mr-1"></i> Back to Dashboard
                    </a>
                </div>
            </div>
            
            <!-- Stats Overview -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 p-6">
                <div class="bg-blue-50 rounded-lg p-6 shadow-sm">
                    <h3 class="text-lg font-semibold mb-2 text-blue-800">Total Orders</h3>
                    <p class="text-3xl font-bold" id="total-orders"><%= totalOrders %></p>
                </div>
                <div class="bg-green-50 rounded-lg p-6 shadow-sm">
                    <h3 class="text-lg font-semibold mb-2 text-green-800">Total Revenue</h3>
                    <p class="text-3xl font-bold" id="total-revenue">AED <%= totalRevenue.toFixed(2) %></p>
                </div>
                <div class="bg-yellow-50 rounded-lg p-6 shadow-sm">
                    <h3 class="text-lg font-semibold mb-2 text-yellow-800">Pending Revenue</h3>
                    <p class="text-3xl font-bold" id="pending-revenue">AED <%= pendingRevenue.toFixed(2) %></p>
                </div>
            </div>
            
            <!-- Manual Revenue Entry Section -->
            <div class="p-6 border-t border-b">
                <h2 class="text-xl font-bold mb-4">Add Manual Revenue Entry</h2>
                <form id="manual-revenue-form" class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-gray-700 mb-2" for="order-id">Order ID</label>
                        <select id="order-id" name="orderId" class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500" required>
                            <option value="">Select an order</option>
                            <!-- Order options will be populated via JavaScript -->
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-gray-700 mb-2" for="amount">Amount (AED)</label>
                        <input type="number" step="0.01" id="amount" name="cashAmount" class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500" required>
                    </div>
                    
                    <div>
                        <label class="block text-gray-700 mb-2" for="payment-date">Payment Date</label>
                        <input type="date" id="payment-date" name="paymentDate" class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500" required>
                    </div>
                    
                    <div>
                        <label class="block text-gray-700 mb-2" for="payment-notes">Notes</label>
                        <input type="text" id="payment-notes" name="notes" class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500" placeholder="Optional notes about this payment">
                    </div>
                    
                    <div class="md:col-span-2 mt-2">
                        <button type="submit" class="bg-green-500 text-white py-2 px-6 rounded-lg hover:bg-green-600 transition">
                            <i class="fas fa-plus-circle mr-1"></i> Record Payment
                        </button>
                    </div>
                </form>
            </div>
            
            <!-- Payment Method Stats -->
            <div class="p-6 border-b">
                <h2 class="text-xl font-bold mb-4">Payment Method Breakdown</h2>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <% 
                    // Initialize payment method stats
                    const paymentStats = {
                        cash: { count: 0, revenue: 0 },
                        card: { count: 0, revenue: 0 },
                        bank_transfer: { count: 0, revenue: 0 }
                    };
                    
                    // Calculate stats from orders
                    if (orders && orders.length) {
                        orders.forEach(order => {
                            const method = order.paymentMethod || 'cash';
                            if (paymentStats[method]) {
                                paymentStats[method].count++;
                                if (order.paymentStatus === 'paid' || order.isPaid === true) {
                                    paymentStats[method].revenue += order.totalAmount || 0;
                                }
                            }
                        });
                    }
                    %>
                    
                    <div class="border rounded-lg p-4">
                        <div class="flex justify-between items-center mb-3">
                            <h3 class="font-semibold">Cash Payments</h3>
                            <span class="bg-blue-100 text-blue-800 text-xs font-bold px-2 py-1 rounded-full">
                                <%= paymentStats.cash.count %> orders
                            </span>
                        </div>
                        <p class="text-2xl font-bold">AED <%= paymentStats.cash.revenue.toFixed(2) %></p>
                    </div>
                    <div class="border rounded-lg p-4">
                        <div class="flex justify-between items-center mb-3">
                            <h3 class="font-semibold">Card Payments</h3>
                            <span class="bg-green-100 text-green-800 text-xs font-bold px-2 py-1 rounded-full">
                                <%= paymentStats.card.count %> orders
                            </span>
                        </div>
                        <p class="text-2xl font-bold">AED <%= paymentStats.card.revenue.toFixed(2) %></p>
                    </div>
                    <div class="border rounded-lg p-4">
                        <div class="flex justify-between items-center mb-3">
                            <h3 class="font-semibold">Bank Transfers</h3>
                            <span class="bg-purple-100 text-purple-800 text-xs font-bold px-2 py-1 rounded-full">
                                <%= paymentStats.bank_transfer.count %> orders
                            </span>
                        </div>
                        <p class="text-2xl font-bold">AED <%= paymentStats.bank_transfer.revenue.toFixed(2) %></p>
                    </div>
                </div>
            </div>
            
            <!-- Cash Payments Quick View -->
            <div class="p-6">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-xl font-bold">Recent Cash Payments</h2>
                    <a href="/admin/cash-payments" class="bg-blue-500 text-white py-2 px-4 rounded-lg hover:bg-blue-600 transition">
                        View All Cash Payments
                    </a>
                </div>
                
                <div class="overflow-x-auto">
                    <table class="min-w-full bg-white border">
                        <thead>
                            <tr class="bg-gray-100 text-left">
                                <th class="py-3 px-4 border-b font-semibold">Order ID</th>
                                <th class="py-3 px-4 border-b font-semibold">Customer</th>
                                <th class="py-3 px-4 border-b font-semibold">Amount</th>
                                <th class="py-3 px-4 border-b font-semibold">Date Recorded</th>
                                <th class="py-3 px-4 border-b font-semibold">Status</th>
                            </tr>
                        </thead>
                        <tbody id="cash-payments-table">
                            <tr class="border-b hover:bg-gray-50">
                                <td colspan="5" class="py-4 px-4 text-center text-gray-500">Loading cash payments...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Set today's date as default payment date
    document.getElementById('payment-date').valueAsDate = new Date();
    
    // Fetch pending cash orders for the dropdown
    fetchPendingCashOrders();
    
    // Fetch recent cash payments
    fetchRecentCashPayments();
    
    // Form submission for manual revenue entry
    document.getElementById('manual-revenue-form').addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const orderId = document.getElementById('order-id').value;
        const amount = document.getElementById('amount').value;
        
        if (!orderId || !amount) {
            alert('Please select an order and enter an amount.');
            return;
        }
        
        try {
            const response = await fetch(`/admin/cash-payments/${orderId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    cashReceived: true,
                    cashAmount: parseFloat(amount),
                    notes: document.getElementById('payment-notes').value,
                    paymentDate: document.getElementById('payment-date').value
                })
            });
            
            if (response.ok) {
                alert('Payment recorded successfully!');
                
                // Reset form
                document.getElementById('manual-revenue-form').reset();
                document.getElementById('payment-date').valueAsDate = new Date();
                
                // Refresh data
                fetchPendingCashOrders();
                fetchRecentCashPayments();
                
                // Reload the page to update stats
                window.location.reload();
            } else {
                const data = await response.json();
                alert('Error recording payment: ' + (data.message || 'Unknown error'));
            }
        } catch (error) {
            console.error('Error recording payment:', error);
            alert('Error recording payment. Please try again.');
        }
    });
});

// Fetch pending cash orders for the dropdown
async function fetchPendingCashOrders() {
    try {
        const response = await fetch('/admin/api/orders?paymentMethod=cash&paymentStatus=pending');
        const orders = await response.json();
        
        const orderSelect = document.getElementById('order-id');
        
        if (orders.length === 0) {
            orderSelect.innerHTML = '<option value="">No pending cash orders</option>';
            return;
        }
        
        orderSelect.innerHTML = '<option value="">Select an order</option>' + 
            orders.map(order => `
                <option value="${order._id}" data-amount="${order.totalAmount}">
                    ${order.customerName} - AED ${order.totalAmount.toFixed(2)} (${new Date(order.date).toLocaleDateString()})
                </option>
            `).join('');
            
        // Set amount when order is selected
        orderSelect.addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];
            if (selectedOption.value) {
                document.getElementById('amount').value = selectedOption.dataset.amount;
            } else {
                document.getElementById('amount').value = '';
            }
        });
    } catch (error) {
        console.error('Error fetching pending cash orders:', error);
    }
}

// Fetch recent cash payments
async function fetchRecentCashPayments() {
    try {
        const response = await fetch('/admin/api/cash-payments?limit=5');
        const payments = await response.json();
        
        const tableBody = document.getElementById('cash-payments-table');
        
        if (payments.length === 0) {
            tableBody.innerHTML = `
                <tr class="border-b">
                    <td colspan="5" class="py-4 px-4 text-center text-gray-500">No cash payments found</td>
                </tr>
            `;
            return;
        }
        
        tableBody.innerHTML = payments.map(payment => `
            <tr class="border-b hover:bg-gray-50">
                <td class="py-3 px-4 text-sm">${payment._id.substring(0, 8)}...</td>
                <td class="py-3 px-4">
                    <div class="font-medium">${payment.customerName}</div>
                    <div class="text-gray-500 text-sm">${payment.email}</div>
                </td>
                <td class="py-3 px-4 font-medium">AED ${(payment.cashAmount || 0).toFixed(2)}</td>
                <td class="py-3 px-4 text-sm">${new Date(payment.cashReceivedDate).toLocaleDateString()}</td>
                <td class="py-3 px-4">
                    <span class="bg-green-100 text-green-800 text-xs font-bold px-2 py-1 rounded-full">Received</span>
                </td>
            </tr>
        `).join('');
    } catch (error) {
        console.error('Error fetching recent cash payments:', error);
        
        const tableBody = document.getElementById('cash-payments-table');
        tableBody.innerHTML = `
            <tr class="border-b">
                <td colspan="5" class="py-4 px-4 text-center text-red-500">Error loading cash payments</td>
            </tr>
        `;
    }
}
</script>

<%- include('../partials/footer') %> 