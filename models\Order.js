const mongoose = require('mongoose');

const orderSchema = new mongoose.Schema({
  // Core fields (new schema)
  customer: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
    // Not required to support guest checkout
  },
  service: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Service'
  },
  // Additional fields for multiple services
  additionalServices: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Service'
  }],
  servicesData: {
    type: Array,
    default: []
  },
  status: {
    type: String,
    enum: ['pending', 'completed', 'cancelled'],
    default: 'pending'
  },
  paymentStatus: {
    type: String,
    enum: ['pending', 'paid', 'cod_pending', 'cod_paid'],
    default: 'pending'
  },
  paymentMethod: {
    type: String,
    enum: ['card', 'cod', 'cash', 'bank_transfer'],
    default: 'cod'
  },
  amount: {
    type: Number
  },
  address: {
    type: String,
    required: true
  },
  scheduledDate: {
    type: Date
  },
  notes: String,
  completedAt: Date,
  paymentDate: Date,
  paymentDetails: {
    transactionId: String,
    paymentMethod: String,
    paymentStatus: String,
    paymentDate: Date,
    collectedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    }
  },
  
  // Payment processing fields
  confirmationEmailSent: {
    type: Boolean,
    default: false
  },
  paymentError: String,
  stripePaymentIntentId: String,
  
  // Legacy fields (old schema) - kept for backward compatibility
  customerName: String,
  email: String,
  phone: String,
  serviceId: mongoose.Schema.Types.ObjectId,
  date: Date,
  message: String,
  totalAmount: Number,
  isPaid: Boolean,
  paidAt: Date,
  paymentId: String,
  cashReceived: Boolean,
  cashReceivedDate: Date,
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  
  // Common field
  createdAt: {
    type: Date,
    default: Date.now
  },
  // Archive field - to hide instead of delete
  isArchived: {
    type: Boolean,
    default: false
  },
  archivedAt: Date,
  archivedReason: String
});

// Pre-save middleware to ensure required fields
orderSchema.pre('save', function(next) {
  // If scheduledDate is missing but date exists, use date
  if (!this.scheduledDate && this.date) {
    this.scheduledDate = this.date;
  }
  
  // If amount is missing but totalAmount exists, use totalAmount
  if (this.amount === undefined && this.totalAmount) {
    this.amount = this.totalAmount;
  }
  
  // Ensure customer field is present
  if (!this.customer && this.user) {
    this.customer = this.user;
  }
  
  next();
});

// Add indexes for better query performance
orderSchema.index({ customer: 1, createdAt: -1 });
orderSchema.index({ status: 1, paymentStatus: 1 });
orderSchema.index({ createdAt: -1 });
orderSchema.index({ paymentStatus: 1 });

module.exports = mongoose.model('Order', orderSchema); 