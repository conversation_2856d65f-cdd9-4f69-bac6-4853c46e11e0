require('dotenv').config();      
const express = require('express');
const path = require('path');      
const bodyParser = require('body-parser');
const session = require('express-session');
const MongoStore = require('connect-mongo');
const connectDB = require('./config/db'); 
const mongoose = require('mongoose');   
const flash = require('connect-flash');  
const cors = require('cors');                   
const cookieParser = require('cookie-parser');    
const setCacheControl = require('./middleware/cache');
                                                                            
// Import routes    
const mainRoutes = require('./routes/mainRoutes');
const serviceRoutes = require('./routes/serviceRoutes');
const orderRoutes = require('./routes/orderRoutes');
const authRoutes = require('./routes/authRoutes');
const paymentRoutes = require('./routes/paymentRoutes');
const adminRoutes = require('./routes/adminRoutes');
                                 
// Import controllers and middleware  
const serviceController = require('./controllers/serviceController');
const authController = require('./controllers/authController');
const { setUserData } = require('./middleware/auth');
             
// Initialize Express app
const app = express();
    
// Set view engine 
app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'views'));
  
// Static files with cache control
app.use(setCacheControl);
app.use(express.static(path.join(__dirname, 'public'), {
  maxAge: '1d' // Fallback for browsers that don't support Cache-Control
}));
  
// Stripe webhook needs raw body
app.use('/payment/webhook', express.raw({ type: 'application/json' }));
 
// Middleware 
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(cors());
app.use(cookieParser());
 
// MongoDB connection state middleware - important for serverless
app.use(async (req, res, next) => {
  // Check if connection is established
  if (mongoose.connection.readyState !== 1) {
    try {
      // Try to connect
      console.log('Connecting to database on request...');
      await connectDB();
    } catch (err) {
      console.error('Error connecting to database:', err.message);
    }
  }  
  next();
});    
                 
// Configure session with enhanced security
app.use(session({
  secret: process.env.SESSION_SECRET || 'junkexperts-secret-key-should-be-changed-in-prod',
  resave: false,
  saveUninitialized: false,
  store: MongoStore.create({
    mongoUrl: process.env.MONGO_URI,
    ttl: 24 * 60 * 60, // 24 hours
    autoRemove: 'native',
    crypto: {
      secret: process.env.SESSION_SECRET || 'junkexperts-secret-key-should-be-changed-in-prod'
    } 
  }),
  cookie: {
    secure: false, // Must be false for Render
    httpOnly: true,
    maxAge: 24 * 60 * 60 * 1000, // 24 hours
    sameSite: 'lax',
    path: '/'
  },
  name: 'junkexpert.sid',
  rolling: true
}));

// Middleware to make flash messages available to all templates
app.use((req, res, next) => {
  res.locals.success = req.session.success;
  res.locals.error = req.session.error;
  res.locals.currentPath = req.path; // Add current path for homepage detection
  delete req.session.success;
  delete req.session.error;
  next();
});    
        
// Set user data for all views
app.use(setUserData);
 
// Health check endpoint for Vercel
app.get('/api/health', async (req, res) => {
  try { 
    // Check DB connection status
    const dbStatus = mongoose.connection.readyState;
    const statusMap = {
      0: 'disconnected',
      1: 'connected',
      2: 'connecting', 
      3: 'disconnecting'
    };
         
    res.json({
      status: 'ok',
      timestamp: new Date(),
      database: {
        status: statusMap[dbStatus] || 'unknown',
        host: mongoose.connection.host || 'not connected'
      },
      environment: process.env.NODE_ENV
    });
  } catch (error) {
    res.status(500).json({
      status: 'error',
      message: error.message
    });
  }
}); 
                   
// Add session debugging middleware
app.use((req, res, next) => {
  if (process.env.SESSION_DEBUG === 'true') {
    console.log('SESSION DATA:', {
      id: req.sessionID,
      data: req.session,
      cookies: req.cookies,
      isAuthenticated: req.session.isAuthenticated,
      userId: req.session.userId,
      userRole: req.session.userRole
    });
  }
  next();
});
                 
// Configure flash messages
app.use(flash());

// Special middleware for landing page (root route) to clear admin cookies
app.use('/', (req, res, next) => {
  // Only apply to the exact landing page (root route)
  if (req.path === '/') {
    // Check for explicit login from session
    const hasExplicitLogin = req.session && req.session.loginTime && 
                             (Date.now() - req.session.loginTime < 60 * 60 * 1000); // 1 hour
    
    // If there's no explicit login through login page, clear admin cookies
    if (!hasExplicitLogin && req.cookies['admin-auth']) {
      console.log('Clearing admin cookie on landing page visit');
      res.clearCookie('admin-auth', { path: '/' });
      // Don't clear the session entirely, just make sure admin role isn't shown
      if (req.session && req.session.userRole === 'admin') {
        req.session.tempRole = req.session.userRole; // Store temporarily
        req.session.userRole = 'user'; // Downgrade temporarily for this request
      }
    }
  }
  next();
});
               
// Routes
app.use('/', mainRoutes);
app.use('/', serviceRoutes);
app.use('/', orderRoutes);
app.use('/auth', authRoutes);
app.use('/payment', paymentRoutes);
app.use('/admin', adminRoutes);
   
// Emergency admin access route (for Render)
app.get('/admin-access', async (req, res) => {
  try {
    const User = require('./models/User');
    const admin = await User.findOne({ role: 'admin' });
    
    if (!admin) {
      return res.status(404).send('No admin user found');
    }
      
    // Set admin cookie
    res.cookie('admin-auth', 'admin-logged-in', {
      maxAge: 24 * 60 * 60 * 1000,
      httpOnly: true,
      secure: false,
      sameSite: 'lax',
      path: '/'
    });
       
    // Redirect to admin dashboard
    res.redirect('/admin/dashboard');
  } catch (error) {
    console.error('Emergency admin access error:', error);
    res.status(500).send('Error accessing admin dashboard');
  }
}); 
  
// 404 handler
app.use((req, res) => {
  res.status(404).render('404', { 
    title: '404 - Page Not Found',
    error: 'The page you are looking for does not exist.' 
  });
});
         
// Error handler
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).render('error', { 
    title: 'Error',
    error: 'Something went wrong. Please try again later.'
  });
});  
    
// Initialize database and sample data with better error handling
const initializeApp = async () => {
  try { 
    // Connect to database
    await connectDB();   
     
    // Initialize services with retry logic 
    await serviceController.initializeServices().catch(err => {
      console.warn('Service initialization warning:', err.message);
    });
    
    // Create admin user with retry logic
    await authController.createAdminUser().catch(err => {
      console.warn('Admin user creation warning:', err.message);
    });
    
    console.log('App initialization completed');
  } catch (error) {
    console.error('App initialization error:', error.message);
  }
};
 
// Call initialization function
initializeApp();
 
// For local development, listen on port
if (process.env.NODE_ENV !== 'production') {
  const PORT = process.env.PORT || 3000;
  app.listen(PORT, () => {
    console.log(`Server running on port ${PORT} (development)`);
  });
} else {
  // Production - ensure we only bind to Render's assigned port once
  const PORT = process.env.PORT || 10000;
  app.listen(PORT, () => console.log(`Server running on port ${PORT} (production)`));
}
    
// Export for Vercel
module.exports = app; 
             