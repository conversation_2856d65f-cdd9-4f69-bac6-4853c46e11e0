const Service = require('../models/Service');
const mongoose = require('mongoose');

// Get all services
exports.getAllServices = async (req, res) => {
  try {
    const services = await Service.find().sort({ isPopular: -1 });
    res.render('services', { 
      title: 'Our Services',
      services
    });
  } catch (error) {
    console.error('Error fetching services:', error);
    req.session.error = 'Failed to load services';
    res.redirect('/');
  }
};

// Get service by ID
exports.getServiceById = async (req, res) => {
  try {
    const service = await Service.findById(req.params.id);
    if (!service) {
      req.session.error = 'Service not found';
      return res.redirect('/services');
    }
    res.render('service-detail', {
      title: service.name,
      service
    });
  } catch (error) {
    console.error('Error fetching service:', error);
    req.session.error = 'Failed to load service details';
    res.redirect('/services');
  }
};

// Create sample services if none exist (development only)
exports.initializeServices = async () => {
  // Add retry mechanism
  let retries = 3;
  let success = false;
  let error;
  
  while (retries > 0 && !success) {
    try {
      // Check connection before proceeding
      if (mongoose.connection.readyState !== 1) {
        console.log('MongoDB not connected, waiting...');
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
      
      const count = await Service.countDocuments();
      if (count === 0) {
        const sampleServices = [
          {
            name: 'Furniture Removal',
            description: 'Efficient removal and recycling of old furniture items',
            price: 249,
            image: 'https://images.unsplash.com/photo-1581578731548-c64695cc6952',
            icon: 'fas fa-couch',
            features: ['Same-day service available', 'Eco-friendly disposal', 'Professional handling']
          },
          {
            name: 'General Waste Removal',
            description: 'Clear out clutter and unwanted items quickly',
            price: 199,
            image: 'https://images.unsplash.com/photo-1542601906990-b4d3fb778b09',
            icon: 'fas fa-trash-alt',
            features: ['Flexible scheduling', 'Affordable pricing', 'Complete cleanup']
          },
          {
            name: 'Eco-Friendly Recycling',
            description: 'Sustainable solutions for waste management',
            price: 299,
            image: 'https://images.unsplash.com/photo-1532996122724-e3c354a0b15b',
            icon: 'fas fa-recycle',
            features: ['Environmentally responsible', 'Certified partners', 'Detailed reporting']
          }
        ];
        
        await Service.insertMany(sampleServices);
        console.log('Sample services created');
      } else {
        console.log(`Services already exist: ${count} found`);
      }
      success = true;
    } catch (err) {
      error = err;
      console.error(`Error initializing services (retries left: ${retries-1}):`, err.message);
      retries--;
      // Wait before retrying
      if (retries > 0) {
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }
  }
  
  if (!success) {
    throw new Error(`Failed to initialize services after multiple attempts: ${error.message}`);
  }
  
  return success;
}; 