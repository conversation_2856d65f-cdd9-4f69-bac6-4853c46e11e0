<%- include('partials/header') %>

    <!-- Order Confirmation Section -->
    <section class="pt-32 pb-16 bg-gray-50" style="background-image: url('/image9.jpg'); background-size: cover; background-position: center; background-attachment: fixed; background-blend-mode: overlay; background-color: rgba(249, 250, 251, 0.95);">
        <div class="container mx-auto px-6">
            <div class="max-w-2xl mx-auto bg-white rounded-lg shadow-xl overflow-hidden transform transition-all duration-500 hover:shadow-2xl">
                <div class="bg-gradient-to-r from-green-500 to-green-600 p-8 text-white text-center relative overflow-hidden">
                    <!-- Success background pattern -->
                    <div class="absolute inset-0 opacity-10">
                        <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%">
                            <defs>
                                <pattern id="confetti" patternUnits="userSpaceOnUse" width="40" height="40" patternTransform="rotate(45)">
                                    <rect width="5" height="5" fill="white" x="0" y="0" opacity="0.4" />
                                </pattern>
                            </defs>
                            <rect width="100%" height="100%" fill="url(#confetti)" />
                        </svg>
                    </div>
                    
                    <div class="relative z-10">
                        <div class="inline-block bg-white rounded-full p-4 mb-6 shadow-lg">
                            <i class="fas fa-check text-4xl text-green-500"></i>
                        </div>
                        <h2 class="text-3xl font-bold mb-2">Booking Confirmed!</h2>
                        <p class="text-xl">Thank you for choosing JunkExperts</p>
                    </div>
                </div>
                
                <div class="p-8">
                    <h3 class="text-2xl font-bold mb-6 text-center text-gray-800">Your Order Details</h3>
                    
                    <!-- Order Information -->
                    <div class="flex flex-col md:flex-row mb-8">
                        <div class="md:w-1/3 flex justify-center mb-6 md:mb-0">
                            <div class="relative w-32 h-32 rounded-full border-4 border-green-100 overflow-hidden">
                                <img src="/image3.jpg" alt="Order confirmed" class="w-full h-full object-cover">
                            </div>
                        </div>
                        <div class="md:w-2/3">
                            <!-- Redesigned order details with better alignment -->
                            <div class="bg-white border border-gray-200 rounded-lg shadow-sm overflow-hidden">
                                <!-- Order ID Section with better handling for long IDs -->
                                <div class="border-b border-gray-200 bg-gray-50 px-4 py-3">
                                    <div class="flex flex-col">
                                        <span class="text-sm text-gray-600 font-medium">Order ID:</span>
                                        <div class="bg-white mt-1 p-2 rounded border border-gray-200 font-mono text-sm text-gray-800 break-all">
                                            <%= order._id %>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Service Information - Consistently formatted -->
                                <div class="p-4">
                                    <div class="mb-4">
                                        <span class="text-sm text-gray-600 font-medium block mb-2">
                                            <% if (order.servicesData && order.servicesData.length > 1) { %>
                                                Services:
                                            <% } else { %>
                                                Service:
                                            <% } %>
                                        </span>
                                        
                                        <% if (order.servicesData && order.servicesData.length > 0) { %>
                                            <div class="space-y-2">
                                            <% order.servicesData.forEach(service => { %>
                                                <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center bg-gray-50 p-3 rounded-md">
                                                    <div class="flex items-center mb-2 sm:mb-0">
                                                        <i class="fas fa-check-circle text-green-500 mr-2"></i>
                                                        <span class="text-gray-800 font-medium"><%= service.name %></span>
                                                    </div>
        
                                                </div>
                                            <% }); %>
                                            </div>
                                        <% } else { %>
                                            <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center bg-gray-50 p-3 rounded-md">
                                                <div class="flex items-center mb-2 sm:mb-0">
                                                    <i class="fas fa-check-circle text-green-500 mr-2"></i>
                                                    <span class="text-gray-800 font-medium"><%= order.service %></span>
                                                </div>
                                                <span class="text-orange-500 font-bold bg-orange-50 px-3 py-1 rounded sm:ml-3">AED <%= order.totalAmount.toFixed(2) %></span>
                                            </div>
                                        <% } %>
                                    </div>
                                    
                                    <!-- Other Order Information -->
                                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 mt-3">
                                        <div class="bg-gray-50 p-3 rounded-md">
                                            <span class="text-sm text-gray-600 font-medium block mb-1">Date & Time:</span>
                                            <div class="flex items-center text-gray-800">
                                                <i class="far fa-calendar-alt text-orange-500 mr-2"></i>
                                                <%= new Date(order.date || order.scheduledDate).toLocaleString() %>
                                            </div>
                                        </div>
                                        
                                        <div class="bg-gray-50 p-3 rounded-md">
                                            <span class="text-sm text-gray-600 font-medium block mb-1">Total Amount:</span>
                                            <div class="font-bold text-orange-600 flex items-center">
                                                <i class="fas fa-tag mr-2"></i>
                                                AED <%= order.totalAmount.toFixed(2) %>
                                            </div>
                                        </div>
                                        
                                        <div class="bg-gray-50 p-3 rounded-md sm:col-span-2">
                                            <span class="text-sm text-gray-600 font-medium block mb-1">Address:</span>
                                            <div class="flex items-start text-gray-800">
                                                <i class="fas fa-map-marker-alt text-orange-500 mr-2 mt-1"></i>
                                                <span><%= order.address %></span>
                                            </div>
                                        </div>
                                        
                                        <div class="bg-gray-50 p-3 rounded-md">
                                            <span class="text-sm text-gray-600 font-medium block mb-1">Payment Method:</span>
                                            <div class="flex items-center text-gray-800 font-medium">
                                                <% if (order.paymentMethod === 'cash') { %>
                                                    <span class="bg-green-50 text-green-700 px-3 py-1 rounded-full inline-flex items-center">
                                                        <i class="fas fa-money-bill-wave mr-2"></i>
                                                        Cash on Service
                                                    </span>
                                                <% } else if (order.paymentMethod === 'card') { %>
                                                    <span class="bg-blue-50 text-blue-700 px-3 py-1 rounded-full inline-flex items-center">
                                                        <i class="fas fa-credit-card mr-2"></i>
                                                        Credit/Debit Card
                                                    </span>
                                                <% } else if (order.paymentMethod === 'bank_transfer') { %>
                                                    <span class="bg-purple-50 text-purple-700 px-3 py-1 rounded-full inline-flex items-center">
                                                        <i class="fas fa-university mr-2"></i>
                                                        Bank Transfer
                                                    </span>
                                                <% } else { %>
                                                    <%= order.paymentMethod %>
                                                <% } %>
                                            </div>
                                        </div>
                                        
                                        <div class="bg-gray-50 p-3 rounded-md">
                                            <span class="text-sm text-gray-600 font-medium block mb-1">Payment Status:</span>
                                            <div>
                                                <% if (order.paymentStatus === 'paid') { %>
                                                    <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium inline-flex items-center">
                                                        <i class="fas fa-check-circle mr-1"></i> Paid
                                                    </span>
                                                <% } else if (order.paymentStatus === 'pending') { %>
                                                    <span class="bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm font-medium inline-flex items-center">
                                                        <i class="fas fa-clock mr-1"></i> Pending
                                                    </span>
                                                <% } else if (order.paymentStatus === 'cod_pending') { %>
                                                    <span class="bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm font-medium inline-flex items-center">
                                                        <i class="fas fa-money-bill-wave mr-1"></i> Cash on Delivery (Pending)
                                                    </span>
                                                <% } else if (order.paymentStatus === 'cod_paid') { %>
                                                    <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium inline-flex items-center">
                                                        <i class="fas fa-check-circle mr-1"></i> Cash on Delivery (Paid)
                                                    </span>
                                                <% } else { %>
                                                    <span class="bg-red-100 text-red-800 px-3 py-1 rounded-full text-sm font-medium inline-flex items-center">
                                                        <i class="fas fa-exclamation-circle mr-1"></i> Failed
                                                    </span>
                                                <% } %>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Booking Confirmation Message -->
                    <div class="bg-gray-50 p-6 rounded-lg mb-8 border border-gray-200 shadow-sm">
                        <h4 class="font-bold mb-5 flex items-center text-xl text-gray-800">
                            <span class="w-8 h-8 bg-orange-500 text-white rounded-full flex items-center justify-center mr-2">
                                <i class="fas fa-info"></i>
                            </span>
                            What happens next?
                        </h4>
                        <div class="grid md:grid-cols-3 gap-6">
                            <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
                                <div class="flex flex-col items-center">
                                    <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-3">
                                        <i class="fas fa-envelope text-blue-600 text-xl"></i>
                                    </div>
                                    <span class="text-center text-sm text-gray-700 font-medium">You'll receive a confirmation email with your booking details</span>
                                </div>
                            </div>
                            <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
                                <div class="flex flex-col items-center">
                                    <div class="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mb-3">
                                        <i class="fas fa-phone-alt text-orange-600 text-xl"></i>
                                    </div>
                                    <span class="text-center text-sm text-gray-700 font-medium">Our team will contact you one day before your scheduled service</span>
                                </div>
                            </div>
                            <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
                                <div class="flex flex-col items-center">
                                    <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-3">
                                        <i class="fas fa-truck text-green-600 text-xl"></i>
                                    </div>
                                    <span class="text-center text-sm text-gray-700 font-medium">Our team will arrive at your location within the scheduled time</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Cash Payment Instructions (Only for cash payments) -->
                    <% if (order.paymentMethod === 'cash') { %>
                    <div class="mb-8 bg-yellow-50 p-6 rounded-lg border border-yellow-200 shadow-sm">
                        <h4 class="font-bold mb-4 flex items-center text-xl text-gray-800">
                            <span class="w-8 h-8 bg-yellow-500 text-white rounded-full flex items-center justify-center mr-2">
                                <i class="fas fa-money-bill-wave"></i>
                            </span>
                            Cash Payment Instructions
                        </h4>
                        <div class="space-y-3 text-gray-700">
                            <div class="flex items-start">
                                <div class="min-w-8 h-8 rounded-full bg-white flex items-center justify-center mr-3 border border-yellow-200 shadow-sm">
                                    <span class="text-yellow-600 font-bold">1</span>
                                </div>
                                <p>Please have the exact amount of <strong class="text-yellow-700">AED <%= order.totalAmount.toFixed(2) %></strong> ready when our team arrives.</p>
                            </div>
                            <div class="flex items-start">
                                <div class="min-w-8 h-8 rounded-full bg-white flex items-center justify-center mr-3 border border-yellow-200 shadow-sm">
                                    <span class="text-yellow-600 font-bold">2</span>
                                </div>
                                <p>Our team members will provide a receipt after receiving your payment.</p>
                            </div>
                            <div class="flex items-start">
                                <div class="min-w-8 h-8 rounded-full bg-white flex items-center justify-center mr-3 border border-yellow-200 shadow-sm">
                                    <span class="text-yellow-600 font-bold">3</span>
                                </div>
                                <p>If you need to change your payment method, please contact us at least 24 hours before your scheduled service.</p>
                            </div>
                        </div>
                    </div>
                    <% } %>
                    
                    <!-- Contact Information -->
                    <div class="text-center mb-8 bg-gradient-to-r from-orange-50 to-orange-100 p-6 rounded-lg">
                        <div class="w-16 h-16 bg-white rounded-full mx-auto mb-4 flex items-center justify-center shadow-sm">
                            <i class="fas fa-headset text-orange-500 text-2xl"></i>
                        </div>
                        <h4 class="font-bold mb-2 text-gray-800">Need to make changes?</h4>
                        <p class="text-gray-700 mb-4">If you need to reschedule or have any questions about your service, please contact us:</p>
                        <div class="space-y-2 max-w-xs mx-auto">
                            <a href="tel:+971569257614" class="flex items-center justify-center text-orange-600 hover:text-orange-700 bg-white p-3 rounded-lg shadow-sm border border-orange-200 transition-colors">
                                <i class="fas fa-phone mr-2"></i>
                                +971 56 925 7614
                            </a>
                            <a href="mailto:<EMAIL>" class="flex items-center justify-center text-orange-600 hover:text-orange-700 bg-white p-3 rounded-lg shadow-sm border border-orange-200 transition-colors">
                                <i class="fas fa-envelope mr-2"></i>
                                <EMAIL>
                            </a>
                        </div>
                    </div>
                    
                    <!-- Action Buttons -->
                    <div class="flex flex-col md:flex-row gap-4">
                        <a href="/" class="flex-1 bg-gray-800 text-white py-3 px-6 rounded-lg hover:bg-gray-700 transition text-center flex items-center justify-center">
                            <i class="fas fa-home mr-2"></i> Back to Home
                        </a>
                        
                        <!-- Button to return to payment page (only when coming from payment) -->
                        <button id="backToPayment" onclick="goBackToPayment()" class="hidden flex-1 bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 transition text-center flex items-center justify-center">
                            <i class="fas fa-arrow-left mr-2"></i> Back to Payment
                        </button>
                        
                        <a href="/services" class="flex-1 bg-gradient-to-r from-orange-500 to-orange-600 text-white py-3 px-6 rounded-lg hover:from-orange-600 hover:to-orange-700 transition text-center flex items-center justify-center shadow-md">
                            <i class="fas fa-search mr-2"></i> Browse More Services
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

<%- include('partials/footer') %> 