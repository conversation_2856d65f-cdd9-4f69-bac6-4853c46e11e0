<!-- Footer -->
<footer class="bg-gray-900 text-white py-10 sm:py-12">
    <div class="px-4 sm:px-6">
        <div class="flex flex-col md:flex-row items-start md:items-stretch gap-8 container mx-auto">
            <!-- Logo Area (Left Side) -->
            <div class="h-20 w-20 rounded-full overflow-hidden shadow-md border-2 border-orange-500">
                <img src="https://d1csarkz8obe9u.cloudfront.net/posterpreviews/junk-removal-logo-junk-removal-services-icon-design-template-113e6dbca3b9794444bd7fc5a46e0817_screen.jpg?ts=1683623837" alt="Logo" class="h-full w-full object-cover">
            </div>

            <!-- Main Footer Grid (Right Side Content) -->
            <div class="w-full md:w-4/5">
                <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-8">
                    <!-- About Us -->
                    <div>
                        <h3 class="text-lg font-bold mb-3 sm:mb-4">About Us</h3>
                        <p class="text-gray-400 text-sm sm:text-base">
                            JunkExperts is a premium junk removal service provider in UAE, offering efficient and eco-friendly solutions.
                        </p>
                    </div>
                    <!-- Quick Links -->
                    <div>
                        <h3 class="text-lg font-bold mb-3 sm:mb-4">Quick Links</h3>
                        <ul class="space-y-1 sm:space-y-2">
                            <li><a href="/" class="text-gray-400 hover:text-orange-500 transition text-sm sm:text-base">Home</a></li>
                            <li><a href="/services" class="text-gray-400 hover:text-orange-500 transition text-sm sm:text-base">Services</a></li>
                            <li><a href="/about" class="text-gray-400 hover:text-orange-500 transition text-sm sm:text-base">About</a></li>
                            <li><a href="/contact" class="text-gray-400 hover:text-orange-500 transition text-sm sm:text-base">Contact</a></li>
                            <li><a href="/booking" class="text-gray-400 hover:text-orange-500 transition text-sm sm:text-base">Book Now</a></li>
                        </ul>
                    </div>
                    <!-- Contact Info -->
                    <div>
                        <h3 class="text-lg font-bold mb-3 sm:mb-4">Contact Us</h3>
                        <p class="text-gray-400 text-sm sm:text-base mb-2"><i class="fas fa-phone mr-2"></i>+971569257614</p>
                        <p class="text-gray-400 text-sm sm:text-base mb-2"><i class="fas fa-envelope mr-2"></i><EMAIL></p>
                        <p class="text-gray-400 text-sm sm:text-base"><i class="fas fa-map-marker-alt mr-2"></i>Dubai, UAE</p>
                    </div>
                    <!-- Social Media -->
                    <div>
                        <h3 class="text-lg font-bold mb-3 sm:mb-4">Follow Us</h3>
                        <div class="flex flex-col space-y-4">
                            <!-- Facebook -->
                            <a href="https://www.facebook.com/share/1AjgWj7njS/" target="_blank" class="flex items-center space-x-2 text-gray-400 hover:text-orange-500 transition">
                              <i class="fab fa-facebook-f text-xl"></i>
                              <span>Facebook</span>
                            </a>
                          
                            <!-- Instagram -->
                            <a href="https://www.instagram.com/expertjunk.com5/?igsh=MXN6ZWZjMHo5OGZiNA#" target="_blank" class="flex items-center space-x-2 text-gray-400 hover:text-orange-500 transition">
                              <i class="fab fa-instagram text-xl"></i>
                              <span>Instagram</span>
                            </a>
                          
                          </div>
                          
                    </div>
                </div>
            </div>
        </div>

        <!-- Bottom Line -->
        <div class="border-t border-gray-800 mt-8 sm:mt-12 pt-6 sm:pt-8 text-center text-gray-400">
            <p class="text-sm sm:text-base">&copy; <%= new Date().getFullYear() %> JunkExperts. All rights reserved</p>
        </div>
    </div>
    
</footer>

<!-- Sticky Contact Buttons -->
<div class="fixed bottom-8 sm:bottom-10 w-full px-4 z-50 pointer-events-none">
    <div class="container mx-auto flex justify-between">
        <!-- Phone Button (Left) -->
        <div class="relative group pointer-events-auto">
            <a href="tel:+971569257614" class="bg-orange-500 hover:bg-orange-600 text-white p-4 w-14 h-14 sm:w-16 sm:h-16 rounded-full shadow-xl flex items-center justify-center phone-btn">
                <i class="fas fa-phone-alt text-xl sm:text-2xl"></i>
            </a>
            <span class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 py-1 px-3 bg-gray-900 text-white text-sm rounded-lg whitespace-nowrap opacity-0 group-hover:opacity-100 transition-opacity duration-300 shadow-lg">
                Call Us
            </span>
        </div>
        
        <!-- WhatsApp Button (Right) - Completely rebuilt -->
        <div class="relative group pointer-events-auto">
            <div id="whatsapp-pulse-wrapper" class="absolute inset-0 rounded-full" style="z-index: -1;"></div>
            <a href="https://wa.me/971569257614" target="_blank" 
               id="whatsapp-button"
               class="bg-green-500 hover:bg-green-600 text-white p-4 w-14 h-14 sm:w-16 sm:h-16 rounded-full shadow-xl flex items-center justify-center">
                <i class="fab fa-whatsapp text-xl sm:text-2xl"></i>
            </a>
            <span class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 py-1 px-3 bg-gray-900 text-white text-sm rounded-lg whitespace-nowrap opacity-0 group-hover:opacity-100 transition-opacity duration-300 shadow-lg">
                WhatsApp
            </span>
        </div>
    </div>
</div>

<style>
    /* Phone button pulse effect */
    .phone-btn {
        position: relative;
        overflow: visible;
        z-index: 1;
    }
    
    .phone-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        border-radius: 50%;
        background: rgba(249, 115, 22, 0.5);
        animation: phone-pulse 2s infinite;
        z-index: -1;
    }
    
    @keyframes phone-pulse {
        0% {
            transform: scale(0.95);
            opacity: 1;
        }
        70% {
            transform: scale(1.2);
            opacity: 0;
        }
        100% {
            transform: scale(0.95);
            opacity: 0;
        }
    }
    
    /* Ensure contact buttons appear above other elements */
    .fixed {
        z-index: 9999;
    }
    
    /* Remove any pseudo elements that might cause overflow */
    .fixed a::after {
        display: none !important;
        content: none !important;
    }
</style>

<!-- Script for WhatsApp button pulse animation -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const wrapper = document.getElementById('whatsapp-pulse-wrapper');
        
        function createPulse() {
            const pulse = document.createElement('div');
            pulse.className = 'whatsapp-pulse';
            pulse.style.cssText = `
                position: absolute;
                inset: 0;
                border-radius: 50%;
                background: rgba(34, 197, 94, 0.5);
                animation: whatsapp-pulse 2s forwards;
                pointer-events: none;
            `;
            wrapper.appendChild(pulse);
            
            setTimeout(() => {
                pulse.remove();
            }, 2000);
        }
        
        // Create initial pulse
        createPulse();
        
        // Create new pulse every 2 seconds
        setInterval(createPulse, 2000);
    });
</script>

<style>
    @keyframes whatsapp-pulse {
        0% {
            transform: scale(0.95);
            opacity: 1;
        }
        70% {
            transform: scale(1.2);
            opacity: 0;
        }
        100% {
            transform: scale(0.95);
            opacity: 0;
        }
    }
    
    /* Important: ensure WhatsApp button has NO orange elements or borders */
    #whatsapp-button::after,
    #whatsapp-button::before {
        display: none !important;
        content: none !important;
    }
</style>

<!-- Script Section -->
<script src="https://unpkg.com/aos@next/dist/aos.js"></script>
<script>
    // Initialize AOS
    AOS.init({
        duration: 1000,
        once: true
    });

    // Navbar Scroll Effect (only add if not already handled in header)
    if (!window.navbarInitialized) {
        window.navbarInitialized = true;
        console.log('AOS initialized in footer');
    }
</script>
</body>
</html>