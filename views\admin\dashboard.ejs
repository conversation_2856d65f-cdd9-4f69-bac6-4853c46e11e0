<%- include('../partials/header') %>

<div class="pt-24 pb-16 bg-gray-50">
    <div class="container mx-auto px-4">
        
        <!-- Admin Dashboard Header -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
                <div>
                    <h1 class="text-2xl font-bold text-gray-800">Admin Dashboard</h1>
                    <p class="text-gray-600">Welcome back, <%= user.name %>!</p>
                </div>
                <div class="mt-4 md:mt-0">
                    <p class="text-sm text-gray-600">Current Date: <%= new Date().toLocaleDateString() %></p>
                </div>
            </div>
            
            <!-- Dashboard Quick Stats -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                <div class="bg-blue-50 rounded-lg p-4 border-l-4 border-blue-500">
                    <p class="text-blue-800 text-sm font-medium">Pending Orders</p>
                    <p class="text-2xl font-bold" id="dashboard-pending-orders">-</p>
                </div>
                <div class="bg-green-50 rounded-lg p-4 border-l-4 border-green-500">
                    <p class="text-green-800 text-sm font-medium">Completed Orders</p>
                    <p class="text-2xl font-bold" id="dashboard-completed-orders">-</p>
                </div>
                <div class="bg-purple-50 rounded-lg p-4 border-l-4 border-purple-500">
                    <p class="text-purple-800 text-sm font-medium">Total Revenue</p>
                    <p class="text-2xl font-bold" id="dashboard-total-revenue">-</p>
                </div>
                <div class="bg-orange-50 rounded-lg p-4 border-l-4 border-orange-500">
                    <p class="text-orange-800 text-sm font-medium">This Month</p>
                    <p class="text-2xl font-bold" id="dashboard-month-revenue">-</p>
                </div>
            </div>
        </div>
        
        <!-- Admin Quick Actions -->
        <div class="mb-8">
            <h2 class="text-xl font-bold text-gray-800 mb-4">Quick Actions</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <a href="/admin/orders" class="bg-white rounded-lg shadow-md p-5 transition hover:shadow-lg flex items-center">
                    <div class="w-12 h-12 flex items-center justify-center bg-blue-100 rounded-full mr-4">
                        <i class="fas fa-clipboard-list text-blue-600 text-xl"></i>
                    </div>
                    <div>
                        <h3 class="font-bold text-lg">Manage Orders</h3>
                        <p class="text-gray-600 text-sm">View and update orders</p>
                    </div>
                </a>
                
                <a href="/admin/services" class="bg-white rounded-lg shadow-md p-5 transition hover:shadow-lg flex items-center">
                    <div class="w-12 h-12 flex items-center justify-center bg-green-100 rounded-full mr-4">
                        <i class="fas fa-tools text-green-600 text-xl"></i>
                    </div>
                    <div>
                        <h3 class="font-bold text-lg">Manage Services</h3>
                        <p class="text-gray-600 text-sm">Update services and pricing</p>
                    </div>
                </a>
                 
                <a href="/admin/cod-email-search" class="bg-white rounded-lg shadow-md p-5 transition hover:shadow-lg flex items-center">
                    <div class="w-12 h-12 flex items-center justify-center bg-orange-100 rounded-full mr-4">
                        <i class="fas fa-search-dollar text-orange-600 text-xl"></i>
                    </div>
                    <div>
                        <h3 class="font-bold text-lg">COD Email Search</h3>
                        <p class="text-gray-600 text-sm">Find & manage COD customers by email</p>
                    </div>
                </a>
                
                <a href="/admin/refund-management" class="bg-white rounded-lg shadow-md p-5 transition hover:shadow-lg flex items-center">
                    <div class="w-12 h-12 flex items-center justify-center bg-red-100 rounded-full mr-4">
                        <i class="fas fa-undo-alt text-red-600 text-xl"></i>
                    </div>
                    <div>
                        <h3 class="font-bold text-lg">Refund Management</h3>
                        <p class="text-gray-600 text-sm">Process customer refunds</p>
                    </div>
                </a>
                
            </div>
        </div>
        
        <!-- Recent Orders -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-xl font-bold text-gray-800">Recent Orders</h2>
                <a href="/admin/orders" class="text-blue-600 hover:text-blue-800 font-medium">View All</a>
            </div>
            
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Order ID
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Customer
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Service
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Date
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Amount
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Status
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200" id="recent-orders-table">
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500" colspan="6">
                                Loading recent orders...
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">

            
            <!-- Order Stats -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-bold text-gray-800 mb-4">Order Statistics</h2>
                <div class="grid grid-cols-2 gap-4">
                    <div class="bg-yellow-50 rounded-lg p-4">
                        <p class="text-sm text-yellow-700 mb-1">Pending Orders</p>
                        <p class="text-2xl font-bold" id="order-stats-pending">0</p>
                    </div>
                    <div class="bg-green-50 rounded-lg p-4">
                        <p class="text-sm text-green-700 mb-1">Completed Orders</p>
                        <p class="text-2xl font-bold" id="order-stats-completed">0</p>
                    </div>
                </div>
                
                <div class="mt-4">
                    <h3 class="font-semibold text-gray-700 mb-2">Order Conversion</h3>
                    <div class="h-4 bg-gray-200 rounded-full overflow-hidden">
                        <div id="order-conversion-bar" class="h-full bg-green-500" style="width: 0%"></div>
                    </div>
                    <div class="flex justify-between mt-1 text-sm text-gray-600">
                        <span>Created</span>
                        <span id="order-conversion-text">0% Completed</span>
                    </div>
                </div>
                
                <div class="mt-4 text-center">
                    <a href="/admin/orders" class="inline-block text-blue-600 font-medium hover:text-blue-800">
                        Manage all orders <i class="fas fa-arrow-right ml-1"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add custom styles for progress bar -->
<style>
    /* Ensure progress bar displays correctly */
    #order-conversion-bar {
        transition: width 0.5s ease-in-out;
        min-width: 0% !important;
        display: block !important;
    }
    
    #order-conversion-bar.active-progress {
        opacity: 1 !important;
        visibility: visible !important;
    }
    
    /* Debugging outline */
    .h-4.bg-gray-200.rounded-full {
        position: relative;
        overflow: visible !important;
    }
</style>

<script>
// Helper functions for better performance
const formatCurrency = (amount) => {
    return 'AED ' + (amount !== undefined && amount !== null ? parseFloat(amount).toFixed(2) : '0.00');
};

const getServiceName = (order) => {
    let serviceName = 'Unknown Service';
    if (order.service) {
        if (typeof order.service === 'object' && order.service.name) {
            serviceName = order.service.name;
        } else if (typeof order.service === 'string') {
            serviceName = order.service;
        }
    } else if (order.serviceName) {
        serviceName = order.serviceName;
    }
    return serviceName;
};

const getStatusClass = (status) => {
    if (status === 'paid' || status === 'cod_paid') {
        return 'bg-green-100 text-green-800';
    } else if (status === 'failed') {
        return 'bg-red-100 text-red-800';
    }
    return 'bg-yellow-100 text-yellow-800';
};

// Use a more efficient data loading approach
const loadDashboard = async () => {
    try {
        // Show loading indicators
        document.getElementById('dashboard-pending-orders').textContent = '...';
        document.getElementById('dashboard-completed-orders').textContent = '...';
        document.getElementById('dashboard-total-revenue').textContent = '...';
        document.getElementById('dashboard-month-revenue').textContent = '...';
        
        // Fetch dashboard stats
        const statsResponse = await fetch('/admin/api/dashboard-stats');
        if (!statsResponse.ok) throw new Error('Failed to fetch dashboard stats');
        const statsData = await statsResponse.json();
        
        // Update the dashboard stats
        document.getElementById('dashboard-pending-orders').textContent = statsData.pendingOrders || 0;
        document.getElementById('dashboard-completed-orders').textContent = statsData.completedOrders || 0;
        document.getElementById('order-stats-pending').textContent = statsData.pendingOrders || 0;
        document.getElementById('order-stats-completed').textContent = statsData.completedOrders || 0;
        
        // Update revenue stats
        const totalRevenue = statsData.totalRevenue || 0;
        const monthRevenue = statsData.monthRevenue || 0;
        
        // Update main stats
        document.getElementById('dashboard-total-revenue').textContent = formatCurrency(totalRevenue);
        document.getElementById('dashboard-month-revenue').textContent = formatCurrency(monthRevenue);
        
        // Update revenue overview - only try to update if elements exist
        const totalRevenueElement = document.getElementById('revenue-overview-total');
        const monthRevenueElement = document.getElementById('revenue-overview-month');
        
        if (totalRevenueElement) {
            totalRevenueElement.textContent = formatCurrency(totalRevenue);
        }
        
        if (monthRevenueElement) {
            monthRevenueElement.textContent = formatCurrency(monthRevenue);
        }
        
        // Calculate order completion rate
        const totalOrders = (statsData.pendingOrders || 0) + (statsData.completedOrders || 0);
        if (totalOrders > 0) {
            const completionRate = Math.round((statsData.completedOrders / totalOrders) * 100);
            console.log("Order stats:", {
                pendingOrders: statsData.pendingOrders,
                completedOrders: statsData.completedOrders,
                totalOrders: totalOrders,
                calculatedRate: completionRate
            });
            
            // Use setTimeout to ensure DOM is fully rendered
            setTimeout(() => {
                // Get the bar element
                const conversionBar = document.getElementById('order-conversion-bar');
                if (conversionBar) {
                    // Force the width with !important to override any CSS
                    conversionBar.setAttribute('style', `width: ${completionRate}% !important`);
                    // Also set the traditional way as backup
                    conversionBar.style.width = `${completionRate}%`;
                    console.log("Set conversion bar width to:", completionRate + '%');
                    
                    // Add a class to ensure visibility
                    conversionBar.classList.add('active-progress');
                } else {
                    console.error("Could not find element with ID 'order-conversion-bar'");
                }
                
                // Update the conversion text
                const conversionText = document.getElementById('order-conversion-text');
                if (conversionText) {
                    conversionText.textContent = completionRate + '% Completed';
                }
            }, 100); // Small delay to ensure DOM is ready
        }
        
        // Update revenue by payment method if available
        if (statsData.revenueByMethod) {
            // Add null checks for each element
            const cashRevenueElement = document.getElementById('cash-revenue');
            const cardRevenueElement = document.getElementById('card-revenue');
            const bankRevenueElement = document.getElementById('bank-revenue');
            
            if (cashRevenueElement) {
                cashRevenueElement.textContent = formatCurrency(statsData.revenueByMethod.cash || 0);
            }
            
            if (cardRevenueElement) {
                cardRevenueElement.textContent = formatCurrency(statsData.revenueByMethod.card || 0);
            }
            
            if (bankRevenueElement) {
                bankRevenueElement.textContent = formatCurrency(statsData.revenueByMethod.bank_transfer || 0);
            }
        }
        
        // Fetch recent orders asynchronously to avoid blocking UI
        loadRecentOrders();
        
    } catch (error) {
        console.error('Error fetching dashboard data:', error);
        
        // Show error message to the user
        const errorMessages = document.querySelectorAll('.dashboard-error');
        errorMessages.forEach(el => {
            el.textContent = 'Failed to load data. Please refresh or try again later.';
            el.classList.remove('hidden');
        });
    }
};

// Separate function to load recent orders
const loadRecentOrders = async () => {
    try {
        // Show loading message in the table
        const ordersTable = document.getElementById('recent-orders-table');
        ordersTable.innerHTML = `
            <tr>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500" colspan="6">
                    Loading recent orders...
                </td>
            </tr>
        `;
        
        // Fetch recent orders
        const ordersResponse = await fetch('/admin/api/recent-orders');
        if (!ordersResponse.ok) throw new Error('Failed to fetch recent orders');
        const ordersData = await ordersResponse.json();
        
        // Update the recent orders table
        if (ordersData.length === 0) {
            ordersTable.innerHTML = `
                <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500" colspan="6">
                        No recent orders found.
                    </td>
                </tr>
            `;
        } else {
            ordersTable.innerHTML = ordersData.map(order => {
                // Format amount correctly
                const amount = order.amount || order.totalAmount || 0;
                const formattedAmount = parseFloat(amount).toFixed(2);
                
                // Format date correctly
                const orderDate = new Date(order.date || order.scheduledDate || order.createdAt);
                const formattedDate = orderDate.toLocaleDateString();
                
                return `
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            ${order._id.substring(0, 8)}...
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            ${order.customerName}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            ${getServiceName(order)}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            ${formattedDate}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            AED ${formattedAmount}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                ${getStatusClass(order.paymentStatus)}">
                                ${order.paymentStatus}
                            </span>
                        </td>
                    </tr>
                `;
            }).join('');
        }
    } catch (error) {
        console.error('Error fetching recent orders:', error);
        
        // Show error message in the table
        const ordersTable = document.getElementById('recent-orders-table');
        ordersTable.innerHTML = `
            <tr>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-red-600" colspan="6">
                    Failed to load recent orders. Please refresh to try again.
                </td>
            </tr>
        `;
    }
};

// Initialize dashboard on page load
document.addEventListener('DOMContentLoaded', loadDashboard);
</script>

<%- include('../partials/footer') %> 