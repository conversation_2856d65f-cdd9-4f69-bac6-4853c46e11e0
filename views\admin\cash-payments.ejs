<%- include('../partials/header') %>

<div class="pt-32 pb-16 bg-gray-50">
    <div class="container mx-auto px-6">
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <div class="p-6 border-b">
                <div class="flex justify-between items-center">
                    <div>
                        <h1 class="text-3xl font-bold mb-2">Cash Payments Management</h1>
                        <p class="text-gray-600">Record and track cash payments from customers</p>
                    </div>
                    <a href="/admin/revenue" class="bg-orange-500 text-white py-2 px-6 rounded-lg hover:bg-orange-600 transition">
                        Back to Revenue Dashboard
                    </a>
                </div>
            </div>
            
            <!-- Cash Payments Table -->
            <div class="p-6">
                <h2 class="text-xl font-bold mb-4">Cash Payment Orders</h2>
                <div class="overflow-x-auto">
                    <table class="min-w-full bg-white border">
                        <thead>
                            <tr class="bg-gray-100 text-left">
                                <th class="py-3 px-4 border-b font-semibold">Order ID</th>
                                <th class="py-3 px-4 border-b font-semibold">Customer</th>
                                <th class="py-3 px-4 border-b font-semibold">Service</th>
                                <th class="py-3 px-4 border-b font-semibold">Service Date</th>
                                <th class="py-3 px-4 border-b font-semibold">Amount</th>
                                <th class="py-3 px-4 border-b font-semibold">Status</th>
                                <th class="py-3 px-4 border-b font-semibold">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <% if (cashOrders && cashOrders.length > 0) { %>
                                <% cashOrders.forEach(order => { %>
                                    <tr class="border-b hover:bg-gray-50">
                                        <td class="py-3 px-4 text-sm"><%= order._id %></td>
                                        <td class="py-3 px-4">
                                            <div class="font-medium"><%= order.customerName %></div>
                                            <div class="text-gray-500 text-sm"><%= order.email %></div>
                                            <div class="text-gray-500 text-sm"><%= order.phone %></div>
                                        </td>
                                        <td class="py-3 px-4"><%= order.service %></td>
                                        <td class="py-3 px-4 text-sm"><%= new Date(order.date).toLocaleDateString() %></td>
                                        <td class="py-3 px-4 font-medium">AED <%= order.totalAmount.toFixed(2) %></td>
                                        <td class="py-3 px-4">
                                            <% if (order.cashReceived) { %>
                                                <span class="bg-green-100 text-green-800 text-xs font-bold px-2 py-1 rounded-full">Received</span>
                                            <% } else { %>
                                                <span class="bg-yellow-100 text-yellow-800 text-xs font-bold px-2 py-1 rounded-full">Pending</span>
                                            <% } %>
                                        </td>
                                        <td class="py-3 px-4">
                                            <% if (!order.cashReceived) { %>
                                                <button 
                                                    class="text-blue-600 hover:text-blue-800 text-sm"
                                                    onclick="openModal('<%= order._id %>', <%= order.totalAmount %>, '<%= order.customerName %>')">
                                                    Record Payment
                                                </button>
                                            <% } else { %>
                                                <div class="text-sm text-gray-500">
                                                    <div>Amount: AED <%= order.cashAmount ? order.cashAmount.toFixed(2) : order.totalAmount.toFixed(2) %></div>
                                                    <div>Date: <%= order.cashReceivedDate ? new Date(order.cashReceivedDate).toLocaleDateString() : 'N/A' %></div>
                                                    <% if (order.paymentNotes) { %>
                                                    <div>Notes: <%= order.paymentNotes %></div>
                                                    <% } %>
                                                </div>
                                            <% } %>
                                        </td>
                                    </tr>
                                <% }); %>
                            <% } else { %>
                                <tr>
                                    <td colspan="7" class="py-4 px-4 text-center text-gray-500">No cash payment orders found</td>
                                </tr>
                            <% } %>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Payment Modal -->
<div id="paymentModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center">
    <div class="bg-white rounded-lg w-full max-w-md p-6 relative">
        <button onclick="closeModal()" class="absolute top-2 right-2 text-gray-500 hover:text-gray-800">
            <i class="fas fa-times"></i>
        </button>
        
        <h3 class="text-xl font-bold mb-4">Record Cash Payment</h3>
        <p id="customerInfo" class="mb-4 text-gray-600"></p>
        
        <form id="cashPaymentForm" method="POST" action="">
            <div class="mb-4">
                <label class="block text-gray-700 mb-2" for="cashAmount">Amount Received (AED)</label>
                <input type="number" step="0.01" id="cashAmount" name="cashAmount" 
                       class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500" required>
            </div>
            
            <div class="mb-4">
                <label class="block text-gray-700 mb-2" for="payment-date">Payment Date</label>
                <input type="date" id="payment-date" name="paymentDate" 
                       class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500" required>
            </div>
            
            <div class="mb-4">
                <label class="block text-gray-700 mb-2" for="payment-notes">Notes (Optional)</label>
                <input type="text" id="payment-notes" name="notes" 
                       class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500"
                       placeholder="e.g., Partial payment, Payment by relative, etc.">
            </div>
            
            <div class="mb-6">
                <label class="flex items-center">
                    <input type="checkbox" id="cashReceived" name="cashReceived" value="true" class="mr-2" checked>
                    <span>Confirm payment has been received</span>
                </label>
            </div>
            
            <div class="flex gap-3">
                <button type="submit" class="bg-green-500 text-white py-2 px-6 rounded-lg hover:bg-green-600 transition">
                    Save Payment
                </button>
                <button type="button" onclick="closeModal()" class="bg-gray-300 text-gray-700 py-2 px-6 rounded-lg hover:bg-gray-400 transition">
                    Cancel
                </button>
            </div>
        </form>
    </div>
</div>

<script>
    function openModal(orderId, amount, customerName) {
        const modal = document.getElementById('paymentModal');
        const form = document.getElementById('cashPaymentForm');
        const customerInfo = document.getElementById('customerInfo');
        const cashAmount = document.getElementById('cashAmount');
        const paymentDate = document.getElementById('payment-date');
        
        modal.classList.remove('hidden');
        form.action = `/admin/cash-payments/${orderId}`;
        customerInfo.textContent = `Recording payment for ${customerName}`;
        cashAmount.value = amount.toFixed(2);
        
        // Set today's date as default
        paymentDate.valueAsDate = new Date();
    }
    
    function closeModal() {
        const modal = document.getElementById('paymentModal');
        modal.classList.add('hidden');
    }
</script>

<%- include('../partials/footer') %> 