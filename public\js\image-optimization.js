/**
 * Image optimization utilities for JunkExpert
 * Adds lazy loading and WebP support for browsers
 */

// Function to check WebP support
function checkWebpSupport() {
  const canvas = document.createElement('canvas');
  if (!!(canvas.getContext && canvas.getContext('2d'))) {
    return canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0;
  }
  return false;
}

// Set a data attribute on html element for CSS targeting
document.documentElement.setAttribute('data-webp', checkWebpSupport() ? 'supported' : 'not-supported');

// Apply lazy loading to all images not already configured
document.addEventListener('DOMContentLoaded', function() {
  // Find all images that don't have loading="lazy" attribute
  const images = document.querySelectorAll('img:not([loading])');
  images.forEach(img => {
    // Skip small images that are likely UI elements
    if (img.width < 40 || img.height < 40) return;
    
    // Add lazy loading attribute to defer offscreen images
    img.setAttribute('loading', 'lazy');
    
    // Add decoding async for better handling
    img.setAttribute('decoding', 'async');
    
    // If image doesn't have explicit dimensions and is in image-dimensions.json, add them
    if (!img.hasAttribute('width') || !img.hasAttribute('height')) {
      fetch('/image-dimensions.json')
        .then(response => response.json())
        .then(data => {
          const src = img.getAttribute('src');
          if (data[src]) {
            if (!img.hasAttribute('width')) img.setAttribute('width', data[src].width);
            if (!img.hasAttribute('height')) img.setAttribute('height', data[src].height);
          }
        })
        .catch(error => console.error('Error loading image dimensions:', error));
    }
  });
  
  // Use WebP versions if supported
  if (checkWebpSupport()) {
    const jpgImages = document.querySelectorAll('img[src$=".jpg"], img[src$=".jpeg"], img[src$=".png"]');
    jpgImages.forEach(img => {
      // Create WebP version path
      const webpSrc = img.getAttribute('src').replace(/\.(jpe?g|png)$/i, '.webp');
      
      // Create an image object to test if WebP version exists
      const testImg = new Image();
      testImg.onload = function() {
        // WebP version exists, update the src
        img.setAttribute('src', webpSrc);
      };
      testImg.src = webpSrc;
    });
  }
  
  // Fix for iOS Safari lazy loading bug
  if (/iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream) {
    const lazyImages = document.querySelectorAll('img[loading="lazy"]');
    lazyImages.forEach(img => {
      img.addEventListener('load', function() {
        // Force a reflow to help Safari update the image
        img.style.transform = 'translateZ(0)';
      });
    });
  }
});

// Handle image errors by falling back to original format if WebP fails
document.addEventListener('error', function(event) {
  const target = event.target;
  if (target.tagName === 'IMG' && target.src.endsWith('.webp')) {
    // Try falling back to original format
    const originalSrc = target.src.replace('.webp', '.jpg');
    console.log(`WebP image failed to load, falling back to: ${originalSrc}`);
    target.src = originalSrc;
  }
}, true);

// Set default dimensions for common image sizes
const defaultDimensions = {
  'hero-image': { width: 1200, height: 800 },
  'service-thumb': { width: 600, height: 400 },
  'gallery-image': { width: 400, height: 300 }
};

// Apply default dimensions by class
document.addEventListener('DOMContentLoaded', function() {
  Object.keys(defaultDimensions).forEach(className => {
    const elements = document.getElementsByClassName(className);
    for (let i = 0; i < elements.length; i++) {
      const img = elements[i];
      if (!img.hasAttribute('width')) {
        img.setAttribute('width', defaultDimensions[className].width);
      }
      if (!img.hasAttribute('height')) {
        img.setAttribute('height', defaultDimensions[className].height);
      }
    }
  });
}); 