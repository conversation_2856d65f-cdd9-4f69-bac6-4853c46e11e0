const Order = require('../models/Order');
const Service = require('../models/Service');
const { 
  sendOrderNotificationToAdmin, 
  sendOrderConfirmationToCustomer, 
  sendPaymentConfirmationEmail, 
  sendPaymentNotificationToAdmin 
} = require('../config/email');
const axios = require('axios');
const mongoose = require('mongoose');

// Get booking form
exports.getBookingForm = async (req, res) => {
  try {
    const serviceId = req.query.service;
    let selectedService = null;
    const services = await Service.find().sort({ name: 1 });
    
    if (serviceId) {
      selectedService = await Service.findById(serviceId);
    }
    
    res.render('booking', {
      title: 'Book a Service',
      services,
      selectedService
    });
  } catch (error) {
    console.error('Error fetching booking data:', error);
    req.session.error = 'Failed to load booking form';
    res.redirect('/services');
  }
};

// Create a new order
exports.createOrder = async (req, res) => {
  try {
    // Get form data from request body
    const {
      name,
      email,
      phone,
      selectedServices,
      selectedServicesData,
      date,
      address,
      message,
      paymentMethod,
      totalAmount
    } = req.body;

    // Find user if logged in
    let userId = null;
    if (req.session && req.session.userId) {
      userId = req.session.userId;
    }
    
    // Parse the selected services data if it exists
    let servicesData = [];
    try {
      if (selectedServicesData) {
        servicesData = JSON.parse(selectedServicesData);
      }
    } catch (e) {
      console.error('Error parsing selected services data:', e);
    }
    
    // Handle multiple services or single service
    let primaryServiceId = null;
    let additionalServices = [];
    
    if (Array.isArray(selectedServices) && selectedServices.length > 0) {
      // Multiple services selected
      primaryServiceId = selectedServices[0]; // Use the first one as primary
      
      // Store additional services
      if (selectedServices.length > 1) {
        additionalServices = selectedServices.slice(1);
      }
    } else if (selectedServices) {
      // Single service selected
      primaryServiceId = selectedServices;
    } else if (req.body.service) {
      // Legacy support for single service in old format
      primaryServiceId = req.body.service;
    } else if (req.body.serviceId) {
      // Legacy support for serviceId field
      primaryServiceId = req.body.serviceId;
    }

    // Validate that we have at least one service
    if (!primaryServiceId) {
      return res.status(400).json({
        success: false,
        message: 'Please select at least one service.'
      });
    }

    // Create order object with all data
    const orderData = {
      // Map to new schema fields
      customer: userId,
      service: primaryServiceId,
      amount: parseFloat(totalAmount),
      scheduledDate: new Date(date),
      address: address,
      notes: message,
      paymentMethod: paymentMethod || 'cod',
      paymentStatus: paymentMethod === 'cash' ? 'cod_pending' : 'pending',
      status: 'pending',
      
      // Include old schema fields for backward compatibility
      customerName: name,
      email: email,
      phone: phone,
      totalAmount: parseFloat(totalAmount),
      date: new Date(date),
      message: message,
      user: userId,
      
      // Additional data for multiple services
      additionalServices: additionalServices,
      servicesData: servicesData
    };

    // For card payments, store data in session and redirect to payment page without saving to DB
    if (paymentMethod === 'card') {
      // Store order data in session for later use
      req.session.pendingOrderData = orderData;
      
      if (req.xhr || req.headers.accept.indexOf('json') > -1) {
        // If AJAX request, return JSON with redirect URL
        return res.status(200).json({
          success: true,
          message: 'Redirecting to payment page...',
          redirect: `/payment/stripe/pending`
        });
      } else {
        // If regular form submission, redirect to payment page
        return res.redirect(`/payment/stripe/pending`);
      }
    }

    // For non-card payments (COD, etc.), save to database immediately
    const order = new Order(orderData);

    console.log('Creating order with data:', {
      primaryServiceId,
      additionalServices,
      userId,
      amount: parseFloat(totalAmount),
      date: new Date(date)
    });

    // Save order to database
    const savedOrder = await order.save();
    
    // Send confirmation emails for non-card payments
    await sendOrderConfirmationEmail(savedOrder);
    await sendAdminNotificationEmail(savedOrder);

    // For other payment methods (cash, bank transfer), show confirmation directly
    if (req.xhr || req.headers.accept.indexOf('json') > -1) {
      // If AJAX request, return success JSON
      return res.status(201).json({
        success: true,
        message: 'Order created successfully!',
        order: savedOrder,
        redirect: `/order-confirmation/${savedOrder._id}`
      });
    } else {
      // If regular form submission, redirect to confirmation page
      return res.redirect(`/order-confirmation/${savedOrder._id}`);
    }
  } catch (error) {
    console.error('Error creating order:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create order. Please try again.',
      error: error.message
    });
  }
};

// Online payment processing
exports.processPayment = async (req, res) => {
  try {
    const orderId = req.params.id;
    const order = await Order.findById(orderId);
    
    if (!order) {
      req.session.error = 'Order not found';
      return res.redirect('/booking');
    }
    
    // Redirect to the Stripe payment page instead of rendering the old payment page
    return res.redirect(`/payment/stripe/${orderId}`);
  } catch (error) {
    console.error('Error processing payment:', error);
    req.session.error = 'Unable to process payment';
    res.redirect('/booking');
  }
};

// Mock payment processing (since we don't have a real payment gateway)
exports.confirmPayment = async (req, res) => {
  try {
    const { orderId, cardNumber, expiryDate, cvv } = req.body;
    
    // Simple validation
    if (!cardNumber || !expiryDate || !cvv) {
      req.session.error = 'All payment fields are required';
      return res.redirect(`/payment/${orderId}`);
    }
    
    // Mock payment processing - in a real app, you'd use a payment gateway API
    const isSuccessful = true; // For demo purposes
    const paymentId = 'MOCK_' + Date.now();
    
    if (isSuccessful) {
      // Update order status
      const updatedOrder = await Order.findByIdAndUpdate(
        orderId, 
        {
          paymentStatus: 'paid',
          paymentId
        },
        { new: true } // Return the updated document
      );
      
      // Send payment confirmation emails with retry logic
      if (updatedOrder) {
        try {
          // Try to send customer payment confirmation
          let customerEmailSent = await sendPaymentConfirmationEmail(updatedOrder);
          
          // Retry once if failed
          if (!customerEmailSent) {
            console.log('Retrying customer payment confirmation email...');
            setTimeout(async () => {
              customerEmailSent = await sendPaymentConfirmationEmail(updatedOrder);
              if (!customerEmailSent) {
                console.error('Failed to send customer payment confirmation email after retry');
              }
            }, 2000);
          }
          
          // Try to send admin payment notification
          let adminEmailSent = await sendPaymentNotificationToAdmin(updatedOrder);
          
          // Retry once if failed
          if (!adminEmailSent) {
            console.log('Retrying admin payment notification email...');
            setTimeout(async () => {
              adminEmailSent = await sendPaymentNotificationToAdmin(updatedOrder);
              if (!adminEmailSent) {
                console.error('Failed to send admin payment notification email after retry');
              }
            }, 2000);
          }
        } catch (emailError) {
          console.error('Error in payment email sending process:', emailError);
          // Continue with order processing even if emails fail
        }
      }
      
      req.session.success = 'Payment successful! Your booking is confirmed.';
      return res.redirect(`/order-confirmation/${orderId}`);
    } else {
      req.session.error = 'Payment failed. Please try again.';
      return res.redirect(`/payment/${orderId}`);
    }
  } catch (error) {
    console.error('Error confirming payment:', error);
    req.session.error = 'Payment processing failed';
    res.redirect('/');
  }
};

// Order confirmation page
exports.getOrderConfirmation = async (req, res) => {
  try {
    const orderId = req.params.id;
    
    // Get the order and populate service details
    const order = await Order.findById(orderId)
      .populate('service')
      .populate('additionalServices');
    
    if (!order) {
      req.session.error = 'Order not found';
      return res.redirect('/');
    }
    
    // If order contains populated service data, make sure it's used in the view
    let serviceData = order.servicesData || [];
    
    // If we don't have services data but have the services populated, create it
    if ((!serviceData || serviceData.length === 0) && order.service) {
      // Start with the primary service
      if (typeof order.service === 'object' && order.service !== null) {
        serviceData.push({
          id: order.service._id,
          name: order.service.name,
          price: order.service.price
        });
      }
      
      // Add any additional services if they exist
      if (order.additionalServices && order.additionalServices.length > 0) {
        order.additionalServices.forEach(service => {
          if (typeof service === 'object' && service !== null) {
            serviceData.push({
              id: service._id,
              name: service.name,
              price: service.price
            });
          }
        });
      }
      
      // Attach this to the order for use in the template
      order.servicesData = serviceData;
    }
    
    res.render('order-confirmation', {
      title: 'Order Confirmation',
      order
    });
  } catch (error) {
    console.error('Error fetching order details:', error);
    req.session.error = 'Unable to find order details';
    res.redirect('/');
  }
};

// Validate email address
exports.validateEmail = async (req, res) => {
  try {
    const { email } = req.body;
    
    if (!email) {
      return res.status(400).json({ valid: false, message: 'Email is required' });
    }
    
    // Basic email format validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return res.status(200).json({ valid: false, message: 'Invalid email format' });
    }

    // Get domain and username parts
    const parts = email.split('@');
    const username = parts[0].toLowerCase();
    const domain = parts[1].toLowerCase();
    
    try {
      // Check domain with Abstract API (free tier allows 100 requests/month)
      const response = await axios.get(`https://emailvalidation.abstractapi.com/v1/?api_key=acba6f7e81af4f6f9d37925f17dd15f4&email=${email}`);
      
      // Check if email is valid and deliverable
      if (response.data) {
        const { is_valid_format, is_mx_found, is_smtp_valid, deliverability } = response.data;
        
        // If format invalid or MX records missing
        if (!is_valid_format || !is_mx_found) {
          return res.status(200).json({ valid: false, message: 'This email cannot receive messages' });
        }
        
        // Check deliverability - whether the email actually exists
        if (deliverability === 'UNDELIVERABLE') {
          return res.status(200).json({ valid: false, message: 'This email address does not exist' });
        }
        
        // If SMTP is explicitly invalid
        if (is_smtp_valid === false) {
          return res.status(200).json({ valid: false, message: 'This email address is not valid' });
        }
        
        // If we reach here, email is likely valid
        return res.status(200).json({ 
          valid: true,
          message: 'Valid email address',
          verified: true
        });
      } else {
        // Fallback to basic validation if API returns empty response
        // At least verify that Gmail addresses follow valid format
        if (domain === 'gmail.com') {
          if (username.length < 6 || username.length > 30 || 
              !/^[a-z0-9.]+$/.test(username) || 
              username.startsWith('.') || username.endsWith('.') || username.includes('..')) {
            return res.status(200).json({ valid: false, message: 'Invalid Gmail address format' });
          }
        }
        
        // Accept valid format as backup
        return res.status(200).json({ 
          valid: true, 
          message: 'Email format is valid',
          verified: true
        });
      }
    } catch (error) {
      console.error('Email API validation error:', error);
      
      // Fallback validation if API fails
      // Accept the email if we can't verify with API but format is valid
      return res.status(200).json({ 
        valid: true, 
        message: 'Email format is valid',
        verified: true
      });
    }
  } catch (error) {
    console.error('Email validation error:', error);
    return res.status(500).json({ valid: false, message: 'Error validating email' });
  }
};

// Helper function to get service ID from name or ID
async function getServiceId(serviceInput) {
  try {
    const Service = require('../models/Service');
    
    // If it's already a valid ObjectId, return it
    if (mongoose.Types.ObjectId.isValid(serviceInput)) {
      return serviceInput;
    }
    
    // Try to find service by name
    const service = await Service.findOne({ name: serviceInput });
    
    if (service) {
      return service._id;
    } else {
      // If not found, return null
      console.error(`Service not found with name: ${serviceInput}`);
      return null;
    }
  } catch (error) {
    console.error('Error finding service:', error);
    return null;
  }
}

// Send order confirmation to customer
async function sendOrderConfirmationEmail(order) {
  try {
    const { sendOrderConfirmationToCustomer } = require('../config/email');
    return await sendOrderConfirmationToCustomer(order);
  } catch (error) {
    console.error('Error sending order confirmation email:', error);
    return false;
  }
}

// Send order notification to admin
async function sendAdminNotificationEmail(order) {
  try {
    const { sendOrderNotificationToAdmin } = require('../config/email');
    return await sendOrderNotificationToAdmin(order);
  } catch (error) {
    console.error('Error sending admin notification email:', error);
    return false;
  }
}

// Helper function to finalize order after payment
exports.finalizeOrder = async (orderData, paymentIntentId) => {
  try {
    // Create a new order from the stored data
    const order = new Order({
      ...orderData,
      paymentStatus: 'paid',
      paymentId: paymentIntentId,
      paidAt: new Date(),
      stripePaymentIntentId: paymentIntentId
    });
    
    // Save order to database
    const savedOrder = await order.save();
    console.log('Order finalized and saved after successful payment:', savedOrder._id);
    return savedOrder;
  } catch (error) {
    console.error('Error finalizing order:', error);
    return null;
  }
} 