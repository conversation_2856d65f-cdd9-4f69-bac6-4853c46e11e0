<%- include('../partials/header.ejs') %>

<!-- Stylish Background with Furniture Removal Theme -->
<div class="relative py-16 overflow-hidden" >
  
  <!-- Decorative Elements -->
  <div class="absolute top-0 left-0 w-full h-64 bg-gradient-to-b from-orange-500/20 to-transparent"></div>
  <div class="absolute bottom-0 right-0 w-full h-64 bg-gradient-to-t from-orange-500/20 to-transparent"></div>
  
  <div class="container mx-auto px-4 max-w-4xl relative z-10">
    <div class="bg-white rounded-xl shadow-2xl overflow-hidden border border-gray-100 transition-all duration-300">
      
      <!-- Header Section -->
      <div class="bg-gradient-to-r from-slate-800 to-slate-900 p-6 text-white relative overflow-hidden">
        <div class="absolute top-0 right-0 w-full h-full">
          <img src="/image5.jpg" alt="Junk Removal" class="opacity-20 object-cover h-full w-full">
        </div>
        <div class="relative z-10 flex items-center">
          <div class="w-14 h-14 bg-white rounded-full flex items-center justify-center mr-4 shadow-lg">
            <i class="fas fa-credit-card text-orange-500 text-2xl"></i>
          </div>
          <div>
            <h1 class="text-3xl font-bold">Secure Payment</h1>
            <p class="text-slate-200 text-sm">Complete your booking with our secure payment gateway</p>
          </div>
        </div>
      </div>
      
      <!-- Content Section -->
      <div class="p-8">
        <!-- Order Summary Section -->
        <div class="mb-8 bg-slate-50 rounded-xl border border-slate-200 overflow-hidden">
          <div class="bg-slate-100 px-4 sm:px-6 py-4 border-b border-slate-200">
            <h2 class="text-xl font-semibold text-slate-800 flex items-center">
              <i class="fas fa-clipboard-list text-orange-500 mr-2"></i> Order Summary
            </h2>
          </div>
          
          <div class="p-4 sm:p-6">
            <div class="flex flex-col md:flex-row mb-6">
              <!-- Service Image -->
              <div class="w-full md:w-1/3 mb-4 md:mb-0 md:pr-4">
                <div class="rounded-lg overflow-hidden border border-slate-200 shadow-sm h-36 sm:h-48">
                  <% 
                    let serviceImage = '/junkservice.jpg'; // Default fallback image
                    
                    // Check if we have a populated service object with an image
                    if (typeof order.service === 'object' && order.service !== null && order.service.image) {
                      serviceImage = order.service.image;
                    }
                    // Check if we have serviceData with image from the booking process
                    else if (order.servicesData && order.servicesData.length > 0 && order.servicesData[0].image) {
                      serviceImage = order.servicesData[0].image;
                    }

                    // Initialize serviceName before using it
                    let serviceName = 'Service';
                    if (typeof order.service === 'object' && order.service.name) {
                      serviceName = order.service.name;
                    } else if (order.serviceName) {
                      serviceName = order.serviceName;
                    } else if (typeof order.service === 'string') {
                      serviceName = order.service;
                    }
                  %>
                  <img src="<%= serviceImage %>" alt="<%= serviceName %>" class="w-full h-full object-cover">
                </div>
              </div>
              
              <!-- Service Details -->
              <div class="w-full md:w-2/3">
                <div class="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                  <div>
                    <p class="text-slate-500 text-sm mb-1">Service:</p>
                    <p class="font-medium text-slate-800 break-words">
                      <%= serviceName %>
                    </p>
                  </div>
                  <div>
                    <p class="text-slate-500 text-sm mb-1">Date:</p>
                    <p class="font-medium text-slate-800"><%= new Date(order.date).toLocaleDateString('en-US', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' }) %></p>
                  </div>
                  <div>
                    <p class="text-slate-500 text-sm mb-1">Address:</p>
                    <p class="font-medium text-slate-800 break-words"><%= order.address %></p>
                  </div>
                  <div>
                    <p class="text-slate-500 text-sm mb-1">Customer:</p>
                    <p class="font-medium text-slate-800"><%= order.customerName %></p>
                  </div>
                </div>
              </div>
            </div>
            
            <div class="flex flex-wrap justify-between items-center mt-4 pt-4 border-t border-slate-200 bg-slate-50 p-4 rounded-lg">
              <span class="text-slate-800 text-lg font-bold">Total Amount:</span>
              <span class="text-orange-600 text-xl font-bold">AED <%= order.totalAmount.toFixed(2) %></span>
            </div>
          </div>
        </div>
        
        <!-- Payment Form -->
        <div id="payment-form" class="mt-8">
          <div class="mb-8">
            <label for="card-element" class="block text-lg font-semibold text-slate-800 mb-3 flex items-center">
              <i class="fas fa-lock text-green-500 mr-2"></i> Card Details
            </label>
            
            <!-- Payment method selector -->
            <div class="mb-5 flex space-x-4">
              <div class="flex items-center bg-orange-50 border border-orange-200 rounded-lg px-4 py-2 shadow-sm">
                <div class="w-6 h-6 flex items-center justify-center bg-orange-500 rounded-full mr-2">
                  <i class="fas fa-check text-white text-xs"></i>
                </div>
                <span class="text-slate-700">Credit/Debit Card</span>
              </div>
            </div>
            
            <!-- Card brands -->
            <div class="mb-6 p-4 rounded-xl bg-white border border-slate-200 shadow-sm">
              <div class="flex flex-wrap justify-center gap-4 sm:gap-6">
                <img src="https://www.svgrepo.com/show/355039/visa.svg" alt="Visa" class="h-8 sm:h-10">
                <img src="https://www.svgrepo.com/show/303288/mastercard-2-logo.svg" alt="Mastercard" class="h-8 sm:h-10">
                <img src="https://www.svgrepo.com/show/331433/discover.svg" alt="Discover" class="h-8 sm:h-10">
                <img src="https://www.svgrepo.com/show/303307/american-express-logo.svg" alt="Amex" class="h-8 sm:h-10">
              </div>
            </div>
            
            <!-- Help text -->
            <div class="text-sm text-slate-600 mb-6 bg-blue-50 p-4 rounded-lg border border-blue-100">
              <div class="flex">
                <div class="mr-3 text-blue-500">
                  <i class="fas fa-info-circle text-lg"></i>
                </div>
                <div>
                  <p>Enter your card details below. All transactions are secure and encrypted.</p>
                  <p class="mt-1"><strong>Test mode:</strong> Use card number 4242 4242 4242 4242 with any future expiry date and any CVC.</p>
                </div>
              </div>
            </div>
            
            <!-- Card Element Container -->
            <div class="mb-6">
              <div class="mb-4">
                <label class="block text-sm font-medium text-slate-700 mb-1" for="card-number-element">Card Number</label>
                <div id="card-number-element" class="p-4 border border-slate-300 rounded-lg bg-white shadow-sm transition-all focus-within:shadow-md focus-within:border-orange-300">
                  <!-- Stripe Card Number Element will mount here -->
                </div>
              </div>
              
              <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-2">
                <div>
                  <label class="block text-sm font-medium text-slate-700 mb-1" for="card-expiry-element">Expiration Date</label>
                  <div id="card-expiry-element" class="p-4 border border-slate-300 rounded-lg bg-white shadow-sm transition-all focus-within:shadow-md focus-within:border-orange-300">
                    <!-- Stripe Card Expiry Element will mount here -->
                  </div>
                </div>
                <div>
                  <label class="block text-sm font-medium text-slate-700 mb-1" for="card-cvc-element">Security Code (CVC)</label>
                  <div id="card-cvc-element" class="p-4 border border-slate-300 rounded-lg bg-white shadow-sm transition-all focus-within:shadow-md focus-within:border-orange-300">
                    <!-- Stripe Card CVC Element will mount here -->
                  </div>
                </div>
              </div>
              
              <div id="payment-message" class="mt-2 min-h-[20px] text-sm" role="alert"></div>
            </div>
            
            <!-- Billing Info -->
            <div class="mb-6 bg-slate-50 p-5 rounded-lg border border-slate-200">
              <h3 class="font-medium text-slate-700 mb-2 flex items-center">
                <i class="fas fa-file-invoice-dollar text-orange-500 mr-2"></i> Billing Information
              </h3>
              <p class="text-slate-600 text-sm">Your card will be charged AED <%= order.totalAmount.toFixed(2) %>. No additional fees will be applied.</p>
            </div>
            
            <!-- Submit Button -->
            <button id="submit-button" class="w-full bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white font-bold py-4 px-6 rounded-lg transition-colors text-lg shadow-lg hover:shadow-xl flex items-center justify-center">
              <i class="fas fa-lock mr-2"></i>
              <span id="button-text">Pay AED <%= order.totalAmount.toFixed(2) %></span>
              <span id="spinner" class="hidden">
                <i class="fas fa-spinner fa-spin mr-2"></i> Processing...
              </span>
            </button>
          </div>
        </div>
        
        <!-- Security Badge -->
        <div class="mt-8 flex items-center justify-center text-slate-600 text-sm bg-green-50 p-5 rounded-lg border border-green-100">
          <div class="flex items-center">
            <div class="relative mr-4">
              <div class="w-14 h-14 bg-white rounded-full flex items-center justify-center shadow-md">
                <i class="fas fa-shield-alt text-green-500 text-2xl"></i>
              </div>
              <div class="absolute -top-1 -right-1 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center shadow">
                <i class="fas fa-check text-white text-xs"></i>
              </div>
            </div>
            <div>
              <p class="font-medium text-green-700">Secure Payment Protection</p>
              <p class="text-sm">Your payment information is secure. We use Stripe's encrypted payment processing.</p>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Footer Section -->
      <div class="bg-slate-100 px-8 py-4 border-t border-slate-200 flex justify-center">
        <div class="flex items-center space-x-6">
          <span class="text-slate-500 text-sm flex items-center">
            <i class="fas fa-truck text-slate-400 mr-1"></i> Free Delivery
          </span>
          <span class="text-slate-500 text-sm flex items-center">
            <i class="fas fa-headset text-slate-400 mr-1"></i> 24/7 Support
          </span>
          <span class="text-slate-500 text-sm flex items-center">
            <i class="fas fa-undo text-slate-400 mr-1"></i> 100% Satisfaction
          </span>
        </div>
      </div>
      
    </div>
  </div>
</div>

<script src="https://js.stripe.com/v3/"></script>
<script>
  // Get Stripe publishable key and order id
  const stripe = Stripe('<%= stripePublishableKey %>');
  const orderId = '<%= order._id %>';
  
  // Create separate elements for card number, expiry, and CVC
  const elements = stripe.elements();

  // Style the card elements
  const style = {
    base: {
      color: '#32325d',
      fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif',
      fontSmoothing: 'antialiased',
      fontSize: '16px',
      lineHeight: '1.4',
      '::placeholder': {
        color: '#aab7c4'
      },
      padding: '10px 12px',
    },
    invalid: {
      color: '#e53e3e',
      iconColor: '#e53e3e'
    }
  };

  const elementOptions = { style };

  // Create separate elements for card number, expiry, and CVC
  const cardNumberElement = elements.create('cardNumber', elementOptions);
  const cardExpiryElement = elements.create('cardExpiry', elementOptions);
  const cardCvcElement = elements.create('cardCvc', elementOptions);
  
  // Mount the elements
  cardNumberElement.mount('#card-number-element');
  cardExpiryElement.mount('#card-expiry-element');
  cardCvcElement.mount('#card-cvc-element');
  
  // Focus the card number element on page load
  window.addEventListener('load', function() {
    setTimeout(() => {
      cardNumberElement.focus();
    }, 500);
  });
  
  // Handle validation errors and track completion status for each field
  let cardNumberComplete = false;
  let cardExpiryComplete = false;
  let cardCvcComplete = false;
  
  const displayMessage = document.getElementById('payment-message');
  const updatePaymentMessage = () => {
    // Check if all fields are complete
    const allFieldsComplete = cardNumberComplete && cardExpiryComplete && cardCvcComplete;
    
    // Update submit button state
    const submitButton = document.getElementById('submit-button');
    if (allFieldsComplete) {
      submitButton.classList.remove('opacity-60');
      submitButton.disabled = false;
      displayMessage.textContent = "Card details complete";
      displayMessage.className = "mt-2 min-h-[20px] text-sm text-green-600";
    } else {
      // Don't disable button to allow manual submission attempts
      displayMessage.textContent = '';
    }
  };
  
  // Add event listeners to the individual elements
  cardNumberElement.on('change', ({error, complete}) => {
    cardNumberComplete = complete;
    if (error) {
      displayMessage.textContent = "Card number: " + error.message;
      displayMessage.className = "mt-2 min-h-[20px] text-sm text-red-600";
    }
    updatePaymentMessage();
  });
  
  cardExpiryElement.on('change', ({error, complete}) => {
    cardExpiryComplete = complete;
    if (error) {
      displayMessage.textContent = "Expiry date: " + error.message;
      displayMessage.className = "mt-2 min-h-[20px] text-sm text-red-600";
    }
    updatePaymentMessage();
  });
  
  cardCvcElement.on('change', ({error, complete}) => {
    cardCvcComplete = complete;
    if (error) {
      displayMessage.textContent = "CVC: " + error.message;
      displayMessage.className = "mt-2 min-h-[20px] text-sm text-red-600";
    }
    updatePaymentMessage();
  });
  
  // Handle form submission
  const form = document.getElementById('payment-form');
  const submitButton = document.getElementById('submit-button');
  const buttonText = document.getElementById('button-text');
  const spinner = document.getElementById('spinner');
  
  submitButton.addEventListener('click', async (event) => {
    event.preventDefault();
    
    // Disable the submit button to prevent multiple clicks
    submitButton.disabled = true;
    buttonText.classList.add('hidden');
    spinner.classList.remove('hidden');
    
    try {
      // Create payment intent on the server
      const response = await fetch('/payment/create-payment-intent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ orderId })
      });
      
      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.message || 'Something went wrong');
      }
      
      // Confirm card payment
      const { error, paymentIntent } = await stripe.confirmCardPayment(result.clientSecret, {
        payment_method: {
          card: cardNumberElement,
          billing_details: {
            name: '<%= order.customerName %>',
            email: '<%= order.email %>'
          }
        }
      });
      
      if (error) {
        // Handle payment error - display message and redirect to failed page
        const displayMessage = document.getElementById('payment-message');
        displayMessage.textContent = "We couldn't process your payment. " + error.message;
        displayMessage.className = "mt-2 mb-2 text-sm text-gray-700 p-2 bg-gray-100 rounded";
        
        // Add additional explanatory text
        const errorExplanation = document.createElement('p');
        errorExplanation.classList.add('text-gray-600', 'text-sm', 'mb-3');
        errorExplanation.textContent = 'Taking you to payment options...';
        displayMessage.parentNode.appendChild(errorExplanation);
        
        // Re-enable the button but change text to show redirect is happening
        buttonText.textContent = 'Try Another Payment Method';
        buttonText.classList.remove('hidden');
        spinner.classList.add('hidden');
        submitButton.disabled = true;
        submitButton.classList.add('bg-gray-500');
        
        // Store error in localStorage so it can be displayed on the failed page
        localStorage.setItem('paymentError', error.message);
        
        // Redirect to the failed page after a short delay
        setTimeout(() => {
          window.location.href = `/payment/failed/${orderId}`;
        }, 2000);
        
        throw new Error(error.message);
      } else {
        // Payment succeeded - show brief success message before redirecting
        const displayMessage = document.getElementById('payment-message');
        displayMessage.className = "mt-2 mb-2 text-sm text-green-600 p-2 bg-green-50 rounded border border-green-100";
        displayMessage.textContent = 'Payment successful! Confirmation email will be sent shortly. Redirecting...';
        
        // Redirect after a brief delay
        setTimeout(() => {
          window.location.href = `/payment/success/${orderId}`;
        }, 1500);
      }
    } catch (error) {
      console.error('Error:', error);
      
      // Display error message
      const displayMessage = document.getElementById('payment-message');
      displayMessage.textContent = "There was an error processing your payment: " + error.message;
      displayMessage.className = "mt-2 text-sm text-red-600 p-2 bg-red-50 rounded";
      
      // Re-enable the button
      buttonText.classList.remove('hidden');
      spinner.classList.add('hidden');
      submitButton.disabled = false;
    }
  });
</script>

<%- include('../partials/footer.ejs') %> 