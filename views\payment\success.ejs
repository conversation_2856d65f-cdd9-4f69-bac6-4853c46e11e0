<%- include('../partials/header.ejs') %>

<!-- Hero Section with Background Image -->
<div class="relative min-h-screen bg-gradient-to-br from-green-50 to-green-100 overflow-hidden py-16">
  <!-- Background Elements -->
  <div class="absolute inset-0 z-0">
    <img src="/image8.jpg" alt="Success Background" class="w-full h-full object-cover opacity-10">
  </div>
  <div class="absolute top-0 right-0 w-1/2 h-64 bg-green-400 opacity-10 rounded-bl-full transform rotate-12"></div>
  <div class="absolute bottom-0 left-0 w-1/2 h-64 bg-orange-400 opacity-10 rounded-tr-full transform -rotate-12"></div>
  
  <!-- Main Content Container -->
  <div class="container mx-auto px-4 max-w-4xl">
    <!-- Order Confirmation Card -->
    <div class="bg-white rounded-xl shadow-xl overflow-hidden relative z-10 border border-gray-100 mb-12">
      <!-- Success Header -->
      <div class="bg-gradient-to-r from-green-500 to-green-600 p-8 text-white text-center relative overflow-hidden">
        <div class="absolute inset-0 opacity-10">
          <svg viewBox="0 0 100 100" preserveAspectRatio="xMidYMid slice" xmlns="http://www.w3.org/2000/svg">
            <path d="M0,0 L100,0 L100,100 L0,100 Z" fill="none" stroke="#fff" stroke-width="0.5"></path>
            <path d="M0,0 L100,100 M100,0 L0,100" stroke="#fff" stroke-width="0.5"></path>
            <circle cx="50" cy="50" r="30" fill="none" stroke="#fff" stroke-width="0.5"></circle>
          </svg>
        </div>
        
        <div class="relative z-10 mb-6">
          <!-- Success Icon -->
          <div class="w-24 h-24 mx-auto bg-white rounded-full flex items-center justify-center mb-4 shadow-lg">
            <i class="fas fa-check-circle text-green-500 text-5xl"></i>
          </div>
          <h1 class="text-4xl font-bold mb-4">Payment Successful!</h1>
          <p class="text-xl text-green-100">Your booking has been confirmed</p>
        </div>
      </div>
      
      <!-- Order Details -->
      <div class="p-4 sm:p-8">
        <!-- Main content - now full width -->
        <div class="w-full">
          <!-- Order Confirmation Section -->
          <div class="mb-6 sm:mb-8">
            <h2 class="text-xl sm:text-2xl font-bold text-slate-800 mb-3 sm:mb-4 flex items-center">
              <i class="fas fa-clipboard-check text-green-500 mr-2"></i> Order Confirmation
            </h2>
            
            <div class="bg-slate-50 rounded-xl border border-slate-200 p-4 sm:p-6 mb-6">
              <p class="text-slate-600 mb-4">Your payment has been processed successfully. An email with the details has been sent to <strong class="text-slate-800"><%= order.email %></strong>.</p>
              
              <div class="flex flex-col sm:flex-row sm:justify-between md:items-center mb-4 pb-4 border-b border-slate-200">
                <div class="mb-2 sm:mb-0">
                  <span class="text-slate-500 text-sm">Order Reference</span>
                  <div class="flex items-center mt-1">
                    <span class="font-mono text-lg font-semibold text-slate-800 mr-2"><%= order._id.toString().slice(-8).toUpperCase() %></span>
                    <button class="text-green-600 hover:text-green-700 text-sm" onclick="copyToClipboard('<%= order._id %>')">
                      <i class="fas fa-copy"></i>
                    </button>
                  </div>
                </div>
                <div class="px-4 py-2 bg-green-100 text-green-700 rounded-lg font-medium flex items-center">
                  <i class="fas fa-calendar-check mr-2"></i>
                  Confirmed
                </div>
              </div>
              
              <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                <div>
                  <h3 class="text-slate-500 text-sm mb-1">Service Details</h3>
                  <div class="flex items-start mt-2">
                    <div class="w-10 h-10 sm:w-12 sm:h-12 rounded-lg bg-white border border-slate-200 flex items-center justify-center mr-2 sm:mr-3 shadow-sm flex-shrink-0">
                      <i class="fas fa-truck-loading text-orange-500 text-lg sm:text-xl"></i>
                    </div>
                    <div>
                      <% 
                        let serviceName = 'Service';
                        if (typeof order.service === 'object' && order.service.name) {
                          serviceName = order.service.name;
                        } else if (order.serviceName) {
                          serviceName = order.serviceName;
                        } else if (typeof order.service === 'string') {
                          serviceName = order.service;
                        }
                      %>
                      <p class="font-medium text-slate-800 break-words"><%= serviceName %></p>
                      <p class="text-sm text-slate-500">Scheduled for <%= new Date(order.date).toLocaleDateString('en-US', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' }) %></p>
                    </div>
                  </div>
                </div>
                
                <div>
                  <h3 class="text-slate-500 text-sm mb-1">Customer Information</h3>
                  <div class="flex items-start mt-2">
                    <div class="w-10 h-10 sm:w-12 sm:h-12 rounded-lg bg-white border border-slate-200 flex items-center justify-center mr-2 sm:mr-3 shadow-sm flex-shrink-0">
                      <i class="fas fa-user text-slate-500 text-lg sm:text-xl"></i>
                    </div>
                    <div class="overflow-hidden flex-grow">
                      <p class="font-medium text-slate-800 truncate"><%= order.customerName %></p>
                      <div class="bg-white px-2 py-1 rounded mt-1 border border-slate-100">
                        <p class="text-sm text-slate-500 break-all truncate"><%= order.email %></p>
                      </div>
                      <% if (order.phone) { %>
                        <div class="mt-1 bg-white px-2 py-1 rounded border border-slate-100">
                          <p class="text-sm text-slate-500"><%= order.phone %></p>
                        </div>
                      <% } %>
                    </div>
                  </div>
                </div>
                
                <div>
                  <h3 class="text-slate-500 text-sm mb-1">Service Location</h3>
                  <div class="flex items-start mt-2">
                    <div class="w-10 h-10 sm:w-12 sm:h-12 rounded-lg bg-white border border-slate-200 flex items-center justify-center mr-2 sm:mr-3 shadow-sm flex-shrink-0">
                      <i class="fas fa-map-marker-alt text-slate-500 text-lg sm:text-xl"></i>
                    </div>
                    <div class="overflow-hidden flex-grow">
                      <p class="font-medium text-slate-800">Service Address</p>
                      <div class="bg-white px-2 py-1 rounded mt-1 border border-slate-100">
                        <p class="text-sm text-slate-500 break-words"><%= order.address %></p>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div>
                  <h3 class="text-slate-500 text-sm mb-1">Payment Information</h3>
                  <div class="flex items-start mt-2">
                    <div class="w-10 h-10 sm:w-12 sm:h-12 rounded-lg bg-white border border-slate-200 flex items-center justify-center mr-2 sm:mr-3 shadow-sm flex-shrink-0">
                      <i class="fas fa-credit-card text-green-500 text-lg sm:text-xl"></i>
                    </div>
                    <div class="overflow-hidden flex-grow">
                      <p class="font-medium text-slate-800">Credit Card</p>
                      <div class="mt-1 flex flex-col gap-1">
                        <div class="bg-green-50 px-2 py-1 rounded border border-green-100">
                          <p class="text-sm text-green-600">Payment complete</p>
                        </div>
                        <div class="bg-white px-2 py-1 rounded border border-slate-100">
                          <p class="text-sm text-slate-500 truncate">Transaction ID: <span class="font-mono text-xs"><%= order.transactionId || 'N/A' %></span></p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- Payment Summary Card -->
            <div class="bg-white rounded-xl border border-slate-200 shadow-sm overflow-hidden">
              <div class="bg-slate-50 px-4 sm:px-6 py-3 sm:py-4 border-b border-slate-200">
                <h3 class="font-semibold text-slate-800">Payment Summary</h3>
              </div>
              <div class="p-4 sm:p-6">
                <div class="flex justify-between mb-2">
                  <span class="text-slate-600">Subtotal</span>
                  <span class="text-slate-800">AED <%= (order.totalAmount - (order.tax || 0)).toFixed(2) %></span>
                </div>
                <% if (order.tax) { %>
                <div class="flex justify-between mb-2">
                  <span class="text-slate-600">Tax</span>
                  <span class="text-slate-800">AED <%= order.tax.toFixed(2) %></span>
                </div>
                <% } %>
                <div class="flex justify-between pt-4 mt-4 border-t border-slate-200 text-lg font-bold">
                  <span class="text-slate-800">Total</span>
                  <span class="text-green-600">AED <%= order.totalAmount.toFixed(2) %></span>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Actions -->
        <div class="flex flex-col sm:flex-row gap-3 mt-6 sm:mt-8 pt-4 border-t border-slate-200">
          <a href="/" class="flex-1 bg-white py-3 sm:py-4 px-4 sm:px-6 rounded-lg border border-slate-300 text-center text-slate-800 font-medium hover:bg-slate-50 transition-colors flex items-center justify-center">
            <i class="fas fa-home mr-2"></i> Return to Home
          </a>
          <a href="/services" class="flex-1 bg-gradient-to-r from-orange-500 to-orange-600 py-3 sm:py-4 px-4 sm:px-6 rounded-lg text-center text-white font-medium hover:from-orange-600 hover:to-orange-700 transition-colors flex items-center justify-center">
            <i class="fas fa-box-open mr-2"></i> Explore Services
          </a>
        </div>
      </div>
    </div>
    
    <!-- Testimonial -->
    <div class="mt-12 bg-white rounded-xl p-8 shadow-sm border border-gray-100 relative overflow-hidden">
      <div class="absolute top-0 right-0 w-40 h-40 bg-orange-100 rounded-full transform translate-x-16 -translate-y-16 opacity-50"></div>
      <div class="relative z-10">
        <div class="flex flex-col md:flex-row items-center">
          <div class="mb-6 md:mb-0 md:mr-8">
            <div class="w-24 h-24 rounded-full overflow-hidden border-4 border-white shadow-lg">
              <img src="/image2.jpg" alt="Happy Customer" class="w-full h-full object-cover">
            </div>
          </div>
          <div>
            <div class="mb-4">
              <div class="flex text-yellow-400 mb-2">
                <i class="fas fa-star"></i>
                <i class="fas fa-star"></i>
                <i class="fas fa-star"></i>
                <i class="fas fa-star"></i>
                <i class="fas fa-star"></i>
              </div>
              <p class="text-slate-600 italic">"Exceptional service from start to finish! The booking was easy, and the team was professional, efficient, and respectful. I'll definitely use Junk Experts again!"</p>
            </div>
            <div>
              <p class="font-semibold text-slate-800">Sarah Johnson</p>
              <p class="text-sm text-slate-500">Satisfied Customer</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  function copyToClipboard(text) {
    const tempInput = document.createElement('input');
    tempInput.value = text;
    document.body.appendChild(tempInput);
    tempInput.select();
    document.execCommand('copy');
    document.body.removeChild(tempInput);
    
    // Show a small toast notification
    const toast = document.createElement('div');
    toast.className = 'fixed bottom-4 right-4 bg-slate-800 text-white px-4 py-2 rounded-lg shadow-lg text-sm';
    toast.textContent = 'Order ID copied to clipboard';
    document.body.appendChild(toast);
    
    setTimeout(() => {
      toast.style.opacity = '0';
      toast.style.transition = 'opacity 0.5s ease';
      setTimeout(() => {
        document.body.removeChild(toast);
      }, 500);
    }, 2000);
  }
  
  // Confetti celebration effect
  document.addEventListener('DOMContentLoaded', function() {
    // Simple confetti effect (can be replaced with a proper library like canvas-confetti)
    const confettiContainer = document.createElement('div');
    confettiContainer.className = 'fixed inset-0 pointer-events-none z-50';
    document.body.appendChild(confettiContainer);
    
    const colors = ['#22c55e', '#f97316', '#f59e0b', '#3b82f6', '#8b5cf6'];
    
    for (let i = 0; i < 100; i++) {
      setTimeout(() => {
        const confetti = document.createElement('div');
        confetti.className = 'absolute rounded-sm';
        confetti.style.width = Math.random() * 10 + 5 + 'px';
        confetti.style.height = Math.random() * 10 + 5 + 'px';
        confetti.style.background = colors[Math.floor(Math.random() * colors.length)];
        confetti.style.left = Math.random() * 100 + 'vw';
        confetti.style.top = -20 + 'px';
        confetti.style.transform = 'rotate(' + Math.random() * 360 + 'deg)';
        confetti.style.opacity = Math.random() * 0.7 + 0.3;
        
        confettiContainer.appendChild(confetti);
        
        // Animate falling
        const animation = confetti.animate([
          { transform: `translate(0, 0) rotate(${Math.random() * 360}deg)` },
          { transform: `translate(${Math.random() * 100 - 50}px, ${window.innerHeight + 100}px) rotate(${Math.random() * 360}deg)` }
        ], {
          duration: Math.random() * 3000 + 2000,
          easing: 'cubic-bezier(0.215, 0.61, 0.355, 1)'
        });
        
        animation.onfinish = () => confetti.remove();
      }, Math.random() * 2000);
    }
    
    // Remove container after animation is done
    setTimeout(() => {
      confettiContainer.remove();
    }, 6000);
  });
</script>

<%- include('../partials/footer.ejs') %> 